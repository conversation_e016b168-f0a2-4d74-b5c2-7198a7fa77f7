import { useTranslation } from 'react-i18next';
import { useState, useEffect } from 'react';

// نظام ترجمة احترافي مثل التطبيقات الكبيرة
export const useAppTranslation = () => {
  const { i18n, t: i18nT } = useTranslation('common');
  const [currentLang, setCurrentLang] = useState<'ar' | 'en'>('ar');
  const [isReady, setIsReady] = useState(false);

  // تهيئة اللغة عند بدء التطبيق
  useEffect(() => {
    const initLanguage = () => {
      try {
        // جلب اللغة المحفوظة أو استخدام العربية كافتراضي
        const savedLang = localStorage.getItem('language') || 'ar';
        const validLang = (savedLang === 'en' || savedLang === 'ar') ? savedLang : 'ar';

        setCurrentLang(validLang);
        i18n.changeLanguage(validLang);
        setIsReady(true);

        console.log('🌐 Language initialized:', validLang);
      } catch (error) {
        console.error('❌ Language initialization error:', error);
        setCurrentLang('ar');
        setIsReady(true);
      }
    };

    initLanguage();
  }, [i18n]);

  // مراقبة تغيير اللغة
  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      const validLang = (lng === 'en' || lng === 'ar') ? lng as 'ar' | 'en' : 'ar';
      setCurrentLang(validLang);
      localStorage.setItem('language', validLang);
      console.log('🔄 Language changed to:', validLang);
    };

    i18n.on('languageChanged', handleLanguageChange);
    return () => i18n.off('languageChanged', handleLanguageChange);
  }, [i18n]);

  // دالة ترجمة آمنة ومضمونة مع interpolation
  const t = (key: string, options?: { [key: string]: any }, fallback?: string): string => {
    try {
      if (!isReady) return fallback || key;

      let translation = i18nT(key, options);

      // إذا كانت الترجمة مفقودة، استخدم الاحتياطي أو المفتاح
      if (translation === key && fallback) {
        translation = fallback;
      }

      // معالجة interpolation يدوياً إذا لم يعمل i18next
      if (options && translation && typeof translation === 'string') {
        Object.keys(options).forEach(optionKey => {
          const placeholder = `{{${optionKey}}}`;
          if (translation.includes(placeholder)) {
            translation = translation.replace(new RegExp(placeholder, 'g'), String(options[optionKey]));
          }
        });
      }

      return translation || fallback || key;
    } catch (error) {
      console.error('❌ Translation error for key:', key, error);
      return fallback || key;
    }
  };

  // دالة تغيير اللغة
  const changeLanguage = (newLang: 'ar' | 'en') => {
    try {
      i18n.changeLanguage(newLang);
    } catch (error) {
      console.error('❌ Language change error:', error);
    }
  };

  // دالة ترجمة أنواع المواد (محفوظة كما هي)
  const tMediaType = (type: string): string => {
    const mediaTypeMap: Record<string, Record<'ar' | 'en', string>> = {
      'FILM': { ar: 'Film', en: 'Film' },
      'SERIES': { ar: 'Series', en: 'Series' },
      'PROGRAM': { ar: 'Program', en: 'Program' },
      'SONG': { ar: 'Song', en: 'Song' },
      'FILLER': { ar: 'Filler', en: 'Filler' },
      'STING': { ar: 'Sting', en: 'Sting' },
      'PROMO': { ar: 'Promo', en: 'Promo' },
      'NEXT': { ar: 'Next', en: 'Next' },
      'NOW': { ar: 'Now', en: 'Now' },
      'سنعود': { ar: 'سنعود', en: 'سنعود' },
      'عدنا': { ar: 'عدنا', en: 'عدنا' },
      'MINI': { ar: 'Mini', en: 'Mini' },
      'CROSS': { ar: 'Cross', en: 'Cross' },
      'ALL': { ar: 'جميع الأنواع', en: 'All Types' }
    };

    return mediaTypeMap[type]?.[currentLang] || type;
  };

  return {
    t,
    tMediaType,
    currentLang,
    isRTL: currentLang === 'ar',
    isReady,
    changeLanguage,
    i18n
  };
};
