"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAppTranslation.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAppTranslation.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppTranslation: () => (/* binding */ useAppTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// نظام ترجمة احترافي مثل التطبيقات الكبيرة\nconst useAppTranslation = ()=>{\n    const { i18n, t: i18nT } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)('common');\n    const [currentLang, setCurrentLang] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ar');\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تهيئة اللغة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAppTranslation.useEffect\": ()=>{\n            const initLanguage = {\n                \"useAppTranslation.useEffect.initLanguage\": ()=>{\n                    try {\n                        // جلب اللغة المحفوظة أو استخدام العربية كافتراضي\n                        const savedLang = localStorage.getItem('language') || 'ar';\n                        const validLang = savedLang === 'en' || savedLang === 'ar' ? savedLang : 'ar';\n                        setCurrentLang(validLang);\n                        i18n.changeLanguage(validLang);\n                        setIsReady(true);\n                        console.log('🌐 Language initialized:', validLang);\n                    } catch (error) {\n                        console.error('❌ Language initialization error:', error);\n                        setCurrentLang('ar');\n                        setIsReady(true);\n                    }\n                }\n            }[\"useAppTranslation.useEffect.initLanguage\"];\n            initLanguage();\n        }\n    }[\"useAppTranslation.useEffect\"], [\n        i18n\n    ]);\n    // مراقبة تغيير اللغة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAppTranslation.useEffect\": ()=>{\n            const handleLanguageChange = {\n                \"useAppTranslation.useEffect.handleLanguageChange\": (lng)=>{\n                    const validLang = lng === 'en' || lng === 'ar' ? lng : 'ar';\n                    setCurrentLang(validLang);\n                    localStorage.setItem('language', validLang);\n                    console.log('🔄 Language changed to:', validLang);\n                }\n            }[\"useAppTranslation.useEffect.handleLanguageChange\"];\n            i18n.on('languageChanged', handleLanguageChange);\n            return ({\n                \"useAppTranslation.useEffect\": ()=>i18n.off('languageChanged', handleLanguageChange)\n            })[\"useAppTranslation.useEffect\"];\n        }\n    }[\"useAppTranslation.useEffect\"], [\n        i18n\n    ]);\n    // دالة ترجمة آمنة ومضمونة مع interpolation\n    const t = (key, options, fallback)=>{\n        try {\n            if (!isReady) return fallback || key;\n            let translation = i18nT(key, options);\n            // إذا كانت الترجمة مفقودة، استخدم الاحتياطي أو المفتاح\n            if (translation === key && fallback) {\n                translation = fallback;\n            }\n            // معالجة interpolation يدوياً إذا لم يعمل i18next\n            if (options && translation && typeof translation === 'string') {\n                Object.keys(options).forEach((optionKey)=>{\n                    const placeholder = \"{{\".concat(optionKey, \"}}\");\n                    if (translation.includes(placeholder)) {\n                        translation = translation.replace(new RegExp(placeholder, 'g'), String(options[optionKey]));\n                    }\n                });\n            }\n            return translation || fallback || key;\n        } catch (error) {\n            console.error('❌ Translation error for key:', key, error);\n            return fallback || key;\n        }\n    };\n    // دالة تغيير اللغة\n    const changeLanguage = (newLang)=>{\n        try {\n            i18n.changeLanguage(newLang);\n        } catch (error) {\n            console.error('❌ Language change error:', error);\n        }\n    };\n    // دالة ترجمة أنواع المواد (محفوظة كما هي)\n    const tMediaType = (type)=>{\n        var _mediaTypeMap_type;\n        const mediaTypeMap = {\n            'FILM': {\n                ar: 'Film',\n                en: 'Film'\n            },\n            'SERIES': {\n                ar: 'Series',\n                en: 'Series'\n            },\n            'PROGRAM': {\n                ar: 'Program',\n                en: 'Program'\n            },\n            'SONG': {\n                ar: 'Song',\n                en: 'Song'\n            },\n            'FILLER': {\n                ar: 'Filler',\n                en: 'Filler'\n            },\n            'STING': {\n                ar: 'Sting',\n                en: 'Sting'\n            },\n            'PROMO': {\n                ar: 'Promo',\n                en: 'Promo'\n            },\n            'NEXT': {\n                ar: 'Next',\n                en: 'Next'\n            },\n            'NOW': {\n                ar: 'Now',\n                en: 'Now'\n            },\n            'سنعود': {\n                ar: 'سنعود',\n                en: 'سنعود'\n            },\n            'عدنا': {\n                ar: 'عدنا',\n                en: 'عدنا'\n            },\n            'MINI': {\n                ar: 'Mini',\n                en: 'Mini'\n            },\n            'CROSS': {\n                ar: 'Cross',\n                en: 'Cross'\n            },\n            'ALL': {\n                ar: 'جميع الأنواع',\n                en: 'All Types'\n            }\n        };\n        return ((_mediaTypeMap_type = mediaTypeMap[type]) === null || _mediaTypeMap_type === void 0 ? void 0 : _mediaTypeMap_type[currentLang]) || type;\n    };\n    return {\n        t,\n        tMediaType,\n        currentLang,\n        isRTL: currentLang === 'ar',\n        isReady,\n        changeLanguage,\n        i18n\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAppTranslation.ts\n"));

/***/ })

});