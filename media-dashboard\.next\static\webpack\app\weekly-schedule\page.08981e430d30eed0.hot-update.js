"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/weekly-schedule/page",{

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WeeklySchedulePage() {\n    _s();\n    const { isViewer } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [readOnlyMode, setReadOnlyMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // مرجع للجدول لتثبيت موضع التمرير\n    const scheduleTableRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const visibleRowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const visibleRowIndexRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(-1);\n    // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (isViewer) {\n                setReadOnlyMode(true);\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        isViewer\n    ]);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع\n    const days = [\n        t('common.sunday'),\n        t('common.monday'),\n        t('common.tuesday'),\n        t('common.wednesday'),\n        t('common.thursday'),\n        t('common.friday'),\n        t('common.saturday')\n    ];\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        // التأكد من أن selectedWeek يمثل يوم الأحد\n        const inputDate = new Date(selectedWeek + 'T00:00:00'); // استخدام منتصف الليل\n        console.log('📅 التاريخ المدخل:', selectedWeek);\n        console.log('📅 يوم الأسبوع للتاريخ المدخل:', inputDate.getDay(), '(0=أحد)');\n        // التأكد من أن نبدأ من يوم الأحد\n        const sundayDate = new Date(inputDate);\n        const dayOfWeek = inputDate.getDay();\n        if (dayOfWeek !== 0) {\n            // إذا لم يكن الأحد، نحسب الأحد السابق\n            sundayDate.setDate(inputDate.getDate() - dayOfWeek);\n        }\n        console.log('📅 يوم الأحد المحسوب:', sundayDate.toISOString().split('T')[0]);\n        const dates = [];\n        for(let i = 0; i < 7; i++){\n            // إنشاء تاريخ جديد لكل يوم بدءاً من الأحد\n            const currentDate = new Date(sundayDate);\n            currentDate.setDate(sundayDate.getDate() + i);\n            // استخدام التنسيق العربي للتاريخ (يوم/شهر/سنة)\n            const day = currentDate.getDate();\n            const month = currentDate.getMonth() + 1; // الشهور تبدأ من 0\n            const year = currentDate.getFullYear();\n            // تنسيق التاريخ بالشكل dd/mm/yyyy\n            const dateStr = \"\".concat(day.toString().padStart(2, '0'), \"/\").concat(month.toString().padStart(2, '0'), \"/\").concat(year);\n            dates.push(dateStr);\n            // التحقق من صحة اليوم\n            const actualDayOfWeek = currentDate.getDay();\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(currentDate.toISOString().split('T')[0], \" → \").concat(dateStr, \" [يوم الأسبوع: \").concat(actualDayOfWeek, \"]\"));\n            // تحذير إذا كان اليوم لا يتطابق مع المتوقع\n            if (actualDayOfWeek !== i) {\n                console.warn(\"⚠️ عدم تطابق: متوقع يوم \".concat(i, \" لكن حصلنا على \").concat(actualDayOfWeek));\n            }\n        }\n        return dates;\n    };\n    // استخدام useEffect لتحديث التواريخ عند تغيير الأسبوع المحدد\n    const [weekDates, setWeekDates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--'\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const dates = getWeekDates();\n            console.log('🔄 تحديث التواريخ:', dates);\n            setWeekDates(dates);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || t('schedule.unknown');\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.season'), \" \").concat(item.seasonNumber, \" \").concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.season'), \" \").concat(item.seasonNumber, \" \").concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - \".concat(t('schedule.part'), \" \").concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            // استخدام التاريخ المحلي مع تجنب مشاكل المنطقة الزمنية\n            const today = new Date();\n            // تحويل إلى تاريخ محلي بدون وقت\n            const year = today.getFullYear();\n            const month = today.getMonth();\n            const day = today.getDate();\n            const localDate = new Date(year, month, day);\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم الفعلي:', \"\".concat(year, \"-\").concat((month + 1).toString().padStart(2, '0'), \"-\").concat(day.toString().padStart(2, '0')));\n            console.log('  📊 يوم الأسبوع:', localDate.getDay(), '(0=أحد)');\n            // حساب يوم الأحد لهذا الأسبوع\n            const sunday = new Date(localDate);\n            sunday.setDate(localDate.getDate() - localDate.getDay());\n            // تحويل إلى string بطريقة محلية\n            const weekStart = \"\".concat(sunday.getFullYear(), \"-\").concat((sunday.getMonth() + 1).toString().padStart(2, '0'), \"-\").concat(sunday.getDate().toString().padStart(2, '0'));\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            console.log('  📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // حفظ الصف المرئي قبل تحديث البيانات\n    const saveVisibleRowIndex = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\": ()=>{\n            if (!scheduleTableRef.current) {\n                console.log('⚠️ لم يتم العثور على مرجع الجدول');\n                return;\n            }\n            // الحصول على جميع صفوف الساعات في الجدول\n            const hourRows = scheduleTableRef.current.querySelectorAll('.hour-row');\n            if (!hourRows.length) {\n                console.log('⚠️ لم يتم العثور على صفوف الساعات');\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D تم العثور على \".concat(hourRows.length, \" صف ساعة\"));\n            // تحديد الصف المرئي حاليًا في منتصف الشاشة\n            const viewportHeight = window.innerHeight;\n            const viewportMiddle = window.scrollY + viewportHeight / 2;\n            console.log(\"\\uD83D\\uDCCF منتصف الشاشة: \".concat(viewportMiddle, \"px (ارتفاع الشاشة: \").concat(viewportHeight, \"px, موضع التمرير: \").concat(window.scrollY, \"px)\"));\n            let closestRow = null;\n            let closestDistance = Infinity;\n            let closestIndex = -1;\n            // البحث عن أقرب صف للمنتصف\n            hourRows.forEach({\n                \"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\": (row, index)=>{\n                    const rect = row.getBoundingClientRect();\n                    const rowTop = window.scrollY + rect.top;\n                    const rowBottom = rowTop + rect.height;\n                    const rowMiddle = rowTop + rect.height / 2;\n                    const distance = Math.abs(viewportMiddle - rowMiddle);\n                    console.log(\"  صف \".concat(index, \" (\").concat(hours[index], \"): العلوي=\").concat(rect.top.toFixed(0), \", الارتفاع=\").concat(rect.height.toFixed(0), \", المسافة=\").concat(distance.toFixed(0)));\n                    if (distance < closestDistance) {\n                        closestDistance = distance;\n                        closestRow = row;\n                        closestIndex = index;\n                    }\n                }\n            }[\"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\"]);\n            if (closestRow) {\n                visibleRowRef.current = closestRow;\n                visibleRowIndexRef.current = closestIndex;\n                console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي: الساعة \".concat(hours[closestIndex], \", الفهرس \").concat(closestIndex, \", المسافة=\").concat(closestDistance.toFixed(0), \"px\"));\n            } else {\n                console.log('⚠️ لم يتم العثور على صف مرئي');\n            }\n        }\n    }[\"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\"], [\n        hours\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات - نسخة مبسطة وأكثر فعالية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current !== undefined) {\n                console.log(\"\\uD83D\\uDD04 استعادة موضع التمرير: \".concat(scrollPositionRef.current, \"px\"));\n                // تأخير للتأكد من اكتمال الرندر\n                const timer = setTimeout({\n                    \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                        const targetPosition = scrollPositionRef.current;\n                        // استعادة الموضع مباشرة\n                        window.scrollTo({\n                            top: targetPosition,\n                            behavior: 'instant'\n                        });\n                        console.log(\"\\uD83D\\uDCCD تم استعادة الموضع إلى: \".concat(targetPosition, \"px\"));\n                        // التحقق من نجاح العملية والتصحيح إذا لزم الأمر\n                        setTimeout({\n                            \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                                const currentPosition = window.scrollY;\n                                const difference = Math.abs(currentPosition - targetPosition);\n                                if (difference > 5) {\n                                    console.log(\"\\uD83D\\uDCCD تصحيح الموضع: \".concat(currentPosition, \"px → \").concat(targetPosition, \"px (فرق: \").concat(difference, \"px)\"));\n                                    window.scrollTo({\n                                        top: targetPosition,\n                                        behavior: 'instant'\n                                    });\n                                } else {\n                                    console.log(\"✅ تم تثبيت موضع التمرير بنجاح\");\n                                }\n                            }\n                        }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 100);\n                        shouldRestoreScroll.current = false;\n                    }\n                }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 200);\n                return ({\n                    \"WeeklySchedulePage.useLayoutEffect\": ()=>clearTimeout(timer)\n                })[\"WeeklySchedulePage.useLayoutEffect\"];\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            // التأكد من حفظ موضع التمرير إذا لم يكن محفوظاً\n            if (!shouldRestoreScroll.current) {\n                const currentScrollPosition = window.scrollY;\n                scrollPositionRef.current = currentScrollPosition;\n                shouldRestoreScroll.current = true;\n                console.log(\"\\uD83D\\uDCCD حفظ تلقائي لموضع التمرير: \".concat(currentScrollPosition, \"px\"));\n            }\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"خطأ في تحديث البيانات: \".concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"خطأ في الاتصال: \".concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert(t('schedule.enterMediaName'));\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || t('messages.saveFailed'));\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert(t('messages.saveFailed'));\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm(t('schedule.confirmDeleteTemp'))) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || t('messages.deleteFailed'));\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert('خطأ في حذف المادة المؤقتة');\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي بدقة\n            const currentScrollPosition = window.scrollY;\n            scrollPositionRef.current = currentScrollPosition;\n            // حفظ الصف المرئي قبل التحديث\n            saveVisibleRowIndex();\n            shouldRestoreScroll.current = true;\n            console.log(\"\\uD83D\\uDCCD تم حفظ موضع التمرير: \".concat(currentScrollPosition, \"px والصف المرئي عند إضافة مادة\"));\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ ' + t('schedule.timeConflict'));\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تحديث محلي بدلاً من إعادة تحميل كامل\n                const newScheduleItem = {\n                    id: result.data.id,\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime: startTime,\n                    endTime: endTime,\n                    weekStart: selectedWeek,\n                    mediaItem: mediaItem,\n                    isTemporary: mediaItem.isTemporary || false,\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                };\n                // إضافة المادة للجدول محلياً\n                setScheduleItems((prev)=>[\n                        ...prev,\n                        newScheduleItem\n                    ]);\n                console.log('✅ تم إضافة المادة للجدول محلياً بدون إعادة تحميل');\n            } else {\n                alert(result.error);\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || t('schedule.unknown');\n        const itemType = item.isRerun ? t('schedule.rerunMaterial') : item.isTemporary ? t('schedule.tempMaterial') : t('schedule.originalMaterial');\n        const warningMessage = item.isRerun ? t('schedule.deleteWarningRerun') : item.isTemporary ? t('schedule.deleteWarningTemp') : t('schedule.deleteWarningOriginal');\n        const confirmed = window.confirm(\"\".concat(t('schedule.confirmDelete', {\n            type: itemType,\n            name: itemName\n        }), \"\\n\\n\") + \"\".concat(t('schedule.time'), \": \").concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + warningMessage);\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف من الخادم أيضًا\n            if (item.isTemporary) {\n                console.log(\"\\uD83D\\uDDD1️ بدء حذف المادة المؤقتة: \".concat(itemName, \" (\").concat(item.id, \")\"));\n                try {\n                    var _item_mediaItem1, _item_mediaItem2;\n                    // حذف المادة المؤقتة من الخادم\n                    console.log(\"\\uD83D\\uDD0D إرسال طلب حذف المادة المؤقتة: \".concat(item.id, \" (\").concat(itemName, \")\"));\n                    console.log(\"\\uD83D\\uDCCA بيانات المادة المؤقتة:\", {\n                        id: item.id,\n                        mediaItemId: item.mediaItemId,\n                        dayOfWeek: item.dayOfWeek,\n                        startTime: item.startTime,\n                        endTime: item.endTime,\n                        isTemporary: item.isTemporary,\n                        isRerun: item.isRerun,\n                        mediaItemName: (_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.name\n                    });\n                    const response = await fetch('/api/weekly-schedule', {\n                        method: 'PATCH',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            action: 'deleteTempItem',\n                            tempItemId: item.id,\n                            mediaItemId: item.mediaItemId,\n                            dayOfWeek: item.dayOfWeek,\n                            startTime: item.startTime,\n                            endTime: item.endTime,\n                            weekStart: selectedWeek,\n                            mediaItemName: (_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.name\n                        })\n                    });\n                    const result = await response.json();\n                    if (result.success) {\n                        console.log(\"✅ تم حذف المادة المؤقتة من الخادم: \".concat(itemName));\n                        // حذف محلي بعد نجاح الحذف من الخادم\n                        if (item.isRerun) {\n                            // حذف إعادة مؤقتة فقط\n                            setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                            console.log(\"✅ تم حذف إعادة مؤقتة محليًا: \".concat(itemName));\n                        } else {\n                            // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                            setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isRerun && scheduleItem.originalId === item.id)));\n                            console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها محليًا: \".concat(itemName));\n                        }\n                        // إعادة تحميل البيانات للتأكد من التزامن\n                        // حفظ الصف المرئي قبل إعادة التحميل\n                        saveVisibleRowIndex();\n                        shouldRestoreScroll.current = true;\n                        console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي قبل إعادة تحميل البيانات\");\n                        await fetchScheduleData();\n                    } else {\n                        console.error(\"❌ فشل حذف المادة المؤقتة من الخادم: \".concat(result.error));\n                        alert(\"فشل حذف المادة المؤقتة: \".concat(result.error));\n                    }\n                } catch (error) {\n                    console.error(\"❌ خطأ أثناء حذف المادة المؤقتة: \".concat(error));\n                    alert('حدث خطأ أثناء حذف المادة المؤقتة');\n                }\n                return;\n            }\n            // حفظ الصف المرئي قبل التحديث\n            saveVisibleRowIndex();\n            shouldRestoreScroll.current = true;\n            console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي عند حذف مادة\");\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                // حفظ الصف المرئي قبل إعادة التحميل\n                saveVisibleRowIndex();\n                shouldRestoreScroll.current = true;\n                console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي قبل إعادة تحميل البيانات بعد الحذف\");\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        if (!selectedWeek) return;\n        const currentDate = new Date(selectedWeek + 'T00:00:00');\n        console.log('📅 تغيير الأسبوع - البداية:', {\n            direction: direction > 0 ? 'التالي' : 'السابق',\n            currentWeek: selectedWeek,\n            currentDayOfWeek: currentDate.getDay()\n        });\n        // إضافة أو طرح 7 أيام\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        const newWeekStart = currentDate.toISOString().split('T')[0];\n        console.log('📅 تغيير الأسبوع - النتيجة:', {\n            newWeek: newWeekStart,\n            newDayOfWeek: currentDate.getDay()\n        });\n        setSelectedWeek(newWeekStart);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"⏳ \",\n                    t('schedule.loadingSchedule')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 955,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 954,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"\\uD83D\\uDCC5 \",\n                    t('schedule.selectingDate')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 964,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 963,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: t('schedule.weeklySchedule'),\n            subtitle: t('schedule.weeklySubtitle'),\n            icon: \"\\uD83D\\uDCC5\",\n            fullWidth: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    height: 'calc(100vh - 120px)',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: isRTL ? 'rtl' : 'ltr',\n                    gap: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '320px',\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            border: '1px solid #6b7280',\n                            padding: '20px',\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: '0 0 15px 0',\n                                    color: '#f3f4f6'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDA \",\n                                    t('schedule.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    border: '2px solid #f59e0b',\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            margin: '0 0 10px 0',\n                                            color: '#fbbf24',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: [\n                                            \"⚡ \",\n                                            t('schedule.addTempMedia')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.mediaName'),\n                                        value: tempMediaName,\n                                        onChange: (e)=>setTempMediaName(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: tempMediaType,\n                                        onChange: (e)=>setTempMediaType(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROGRAM\",\n                                                children: \"\\uD83D\\uDCFB Program\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SERIES\",\n                                                children: \"\\uD83D\\uDCFA Series\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"FILM\",\n                                                children: \"\\uD83C\\uDFA5 Film\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"FILLER\",\n                                                children: \"⏸️ Filler\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"STING\",\n                                                children: \"⚡ Sting\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROMO\",\n                                                children: \"\\uD83D\\uDCE2 Promo\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"NEXT\",\n                                                children: \"▶️ Next\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"NOW\",\n                                                children: \"\\uD83D\\uDD34 Now\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"سنعود\",\n                                                children: \"⏰ سنعود\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1041,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"عدنا\",\n                                                children: \"✅ عدنا\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MINI\",\n                                                children: \"\\uD83D\\uDD38 Mini\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"CROSS\",\n                                                children: \"✖️ Cross\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1044,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"LIVE\",\n                                                children: \"\\uD83D\\uDD34 برنامج هواء مباشر\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PENDING\",\n                                                children: \"\\uD83D\\uDFE1 مادة قيد التسليم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.duration'),\n                                        value: tempMediaDuration,\n                                        onChange: (e)=>setTempMediaDuration(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.notes'),\n                                        value: tempMediaNotes,\n                                        onChange: (e)=>setTempMediaNotes(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '10px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: addTempMedia,\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            background: '#ff9800',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '13px',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            marginBottom: '8px'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('schedule.add')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1083,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            console.log('🔄 تحديث الإعادات...');\n                                            scrollPositionRef.current = window.scrollY;\n                                            shouldRestoreScroll.current = true;\n                                            await fetchScheduleData();\n                                        },\n                                        style: {\n                                            width: '100%',\n                                            padding: '6px',\n                                            background: '#4caf50',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: [\n                                            \"♻️ \",\n                                            t('schedule.updateReruns')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedType,\n                                onChange: (e)=>setSelectedType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '10px',\n                                    fontSize: '14px',\n                                    backgroundColor: 'white',\n                                    color: '#333'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('schedule.allTypes')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: \"\\uD83D\\uDCFA Series\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILM\",\n                                        children: \"\\uD83C\\uDFA5 Film\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: \"\\uD83D\\uDCFB Program\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SONG\",\n                                        children: \"\\uD83C\\uDFB5 Song\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROMO\",\n                                        children: \"\\uD83D\\uDCE2 Promo\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"STING\",\n                                        children: \"⚡ Sting\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILLER\",\n                                        children: \"⏸️ Filler\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NEXT\",\n                                        children: \"▶️ Next\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NOW\",\n                                        children: \"\\uD83D\\uDD34 Now\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"سنعود\",\n                                        children: \"⏰ سنعود\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"عدنا\",\n                                        children: \"✅ عدنا\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MINI\",\n                                        children: \"\\uD83D\\uDD38 Mini\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"CROSS\",\n                                        children: \"✖️ Cross\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"\\uD83D\\uDD0D \".concat(t('schedule.searchMedia')),\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '15px',\n                                    fontSize: '14px',\n                                    color: '#333',\n                                    background: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '12px',\n                                    color: '#d1d5db',\n                                    marginBottom: '10px',\n                                    textAlign: 'center',\n                                    padding: '5px',\n                                    background: '#1f2937',\n                                    borderRadius: '4px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    t('schedule.resultsCount', {\n                                        count: filteredMedia.length,\n                                        total: allAvailableMedia.length\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '8px'\n                                },\n                                children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '20px',\n                                        color: '#666',\n                                        background: '#f8f9fa',\n                                        borderRadius: '8px',\n                                        border: '2px dashed #dee2e6'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1197,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: 'bold',\n                                                marginBottom: '5px'\n                                            },\n                                            children: t('schedule.noMedia')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: searchTerm || selectedType ? t('schedule.changeFilter') : t('schedule.addNewMedia')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1189,\n                                    columnNumber: 17\n                                }, this) : filteredMedia.map((item)=>{\n                                    // تحديد لون المادة حسب النوع\n                                    const getItemStyle = ()=>{\n                                        if (item.isTemporary) {\n                                            switch(item.type){\n                                                case 'LIVE':\n                                                    return {\n                                                        background: '#ffebee',\n                                                        border: '2px solid #f44336',\n                                                        borderLeft: '5px solid #f44336'\n                                                    };\n                                                case 'PENDING':\n                                                    return {\n                                                        background: '#fff8e1',\n                                                        border: '2px solid #ffc107',\n                                                        borderLeft: '5px solid #ffc107'\n                                                    };\n                                                default:\n                                                    return {\n                                                        background: '#f3e5f5',\n                                                        border: '2px solid #9c27b0',\n                                                        borderLeft: '5px solid #9c27b0'\n                                                    };\n                                            }\n                                        }\n                                        return {\n                                            background: '#fff',\n                                            border: '1px solid #ddd'\n                                        };\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>handleDragStart(e, item),\n                                        style: {\n                                            ...getItemStyle(),\n                                            borderRadius: '8px',\n                                            padding: '12px',\n                                            cursor: 'grab',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                            position: 'relative'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(-2px)';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(0)';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteTempMedia(item.id);\n                                                },\n                                                style: {\n                                                    position: 'absolute',\n                                                    top: '5px',\n                                                    left: '5px',\n                                                    background: '#f44336',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '50%',\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    fontSize: '12px',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                title: t('schedule.confirmDeleteTemp'),\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: 'bold',\n                                                    color: '#333',\n                                                    marginBottom: '4px'\n                                                },\n                                                children: [\n                                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '10px',\n                                                            background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                            color: 'white',\n                                                            padding: '2px 6px',\n                                                            borderRadius: '10px',\n                                                            marginLeft: '5px'\n                                                        },\n                                                        children: item.type === 'LIVE' ? \"\\uD83D\\uDD34 \".concat(t('schedule.liveProgram')) : item.type === 'PENDING' ? \"\\uD83D\\uDFE1 \".concat(t('schedule.pendingDelivery')) : \"\\uD83D\\uDFE3 \".concat(t('schedule.temporary'))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1289,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    getMediaDisplayText(item)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '12px',\n                                                    color: '#666'\n                                                },\n                                                children: [\n                                                    item.type,\n                                                    \" • \",\n                                                    item.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '11px',\n                                                    color: '#888',\n                                                    marginTop: '4px'\n                                                },\n                                                children: item.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1236,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 980,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px',\n                                    background: '#4a5568',\n                                    padding: '15px',\n                                    borderRadius: '15px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/daily-schedule',\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: [\n                                                    \"\\uD83D\\uDCCB \",\n                                                    t('schedule.broadcastSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1333,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                        const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                        if (!response.ok) {\n                                                            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                        }\n                                                        const blob = await response.blob();\n                                                        const url = window.URL.createObjectURL(blob);\n                                                        const a = document.createElement('a');\n                                                        a.href = url;\n                                                        a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                        document.body.appendChild(a);\n                                                        a.click();\n                                                        window.URL.revokeObjectURL(url);\n                                                        document.body.removeChild(a);\n                                                        console.log('✅ تم تصدير الخريطة بنجاح');\n                                                    } catch (error) {\n                                                        console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                        alert('فشل في تصدير الخريطة: ' + error.message);\n                                                    }\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: [\n                                                    \"\\uD83D\\uDCCA \",\n                                                    t('schedule.exportSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#f3f4f6',\n                                                    fontSize: '1.2rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCC5 \",\n                                                    t('schedule.selectedWeek')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1332,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(-1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: isRTL ? '← الأسبوع السابق' : '← Previous Week'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1409,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedWeek,\n                                                onChange: (e)=>{\n                                                    const selectedDate = new Date(e.target.value + 'T00:00:00');\n                                                    const dayOfWeek = selectedDate.getDay();\n                                                    console.log('📅 تغيير التاريخ من التقويم:', {\n                                                        selectedDate: e.target.value,\n                                                        dayOfWeek: dayOfWeek,\n                                                        dayName: [\n                                                            'الأحد',\n                                                            'الاثنين',\n                                                            'الثلاثاء',\n                                                            'الأربعاء',\n                                                            'الخميس',\n                                                            'الجمعة',\n                                                            'السبت'\n                                                        ][dayOfWeek]\n                                                    });\n                                                    // حساب بداية الأسبوع (الأحد)\n                                                    const sunday = new Date(selectedDate);\n                                                    sunday.setHours(0, 0, 0, 0); // تصفير الوقت\n                                                    sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                                    const weekStart = sunday.toISOString().split('T')[0];\n                                                    console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                                    console.log('📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');\n                                                    setSelectedWeek(weekStart);\n                                                },\n                                                style: {\n                                                    padding: '8px',\n                                                    border: '1px solid #6b7280',\n                                                    borderRadius: '5px',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1423,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: isRTL ? 'الأسبوع التالي →' : 'Next Week →'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1322,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: scheduleTableRef,\n                                style: {\n                                    background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',\n                                    borderRadius: '10px',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                    minHeight: '80vh'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#f8f9fa'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '12px',\n                                                            border: '1px solid #dee2e6',\n                                                            fontWeight: 'bold',\n                                                            width: '80px',\n                                                            color: '#000'\n                                                        },\n                                                        children: t('schedule.time')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1486,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '12px',\n                                                                border: '1px solid #dee2e6',\n                                                                fontWeight: 'bold',\n                                                                width: \"\".concat(100 / 7, \"%\"),\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        marginBottom: '4px',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: day\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1503,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: weekDates[index]\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1504,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1496,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1484,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hour-row\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                background: '#f8f9fa',\n                                                                fontWeight: 'bold',\n                                                                textAlign: 'center',\n                                                                padding: '8px',\n                                                                border: '1px solid #dee2e6',\n                                                                color: '#000'\n                                                            },\n                                                            children: hour\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1516,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        days.map((_, dayIndex)=>{\n                                                            const cellItems = getItemsForCell(dayIndex, hour);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                onDragOver: handleDragOver,\n                                                                onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                                style: {\n                                                                    border: '1px solid #dee2e6',\n                                                                    padding: '8px',\n                                                                    height: '150px',\n                                                                    cursor: 'pointer',\n                                                                    background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',\n                                                                    verticalAlign: 'top'\n                                                                },\n                                                                children: cellItems.map((item)=>{\n                                                                    var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        draggable: true,\n                                                                        onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                        onClick: ()=>deleteItem(item),\n                                                                        style: {\n                                                                            background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                            border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                            borderRadius: '4px',\n                                                                            padding: '8px 6px',\n                                                                            marginBottom: '4px',\n                                                                            fontSize: '1rem',\n                                                                            cursor: 'grab',\n                                                                            transition: 'all 0.2s ease',\n                                                                            minHeight: '60px',\n                                                                            display: 'flex',\n                                                                            flexDirection: 'column',\n                                                                            justifyContent: 'center'\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1.02)';\n                                                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1)';\n                                                                            e.currentTarget.style.boxShadow = 'none';\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    gap: '4px',\n                                                                                    color: '#000'\n                                                                                },\n                                                                                children: [\n                                                                                    item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#666'\n                                                                                        },\n                                                                                        children: [\n                                                                                            \"♻️ \",\n                                                                                            item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                            item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1584,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                        },\n                                                                                        children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1588,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#ff9800'\n                                                                                        },\n                                                                                        children: \"\\uD83C\\uDF1F\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1596,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#000'\n                                                                                        },\n                                                                                        children: item.mediaItem ? getMediaDisplayText(item.mediaItem) : 'غير معروف'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1598,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1582,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.8rem',\n                                                                                    color: '#000',\n                                                                                    marginTop: '4px'\n                                                                                },\n                                                                                children: [\n                                                                                    item.startTime,\n                                                                                    \" - \",\n                                                                                    item.endTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1602,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.5rem',\n                                                                                    color: '#888',\n                                                                                    fontStyle: 'italic'\n                                                                                },\n                                                                                children: t('schedule.rerunIndicator')\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1606,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, item.id, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1544,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, dayIndex, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1530,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, hourIndex, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1515,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1513,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1482,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    padding: '20px',\n                                    borderRadius: '15px',\n                                    marginTop: '20px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 20px 0',\n                                            fontSize: '1.3rem',\n                                            textAlign: 'center'\n                                        },\n                                        children: t('schedule.usageInstructions')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.addMediaTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1633,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction1')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1635,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction2')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1636,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction3')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1637,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction4')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1638,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1634,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1632,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.deleteMediaTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1642,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            t('schedule.originalMaterial'),\n                                                                            \":\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1644,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    t('schedule.deleteOriginalInfo')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1644,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            t('schedule.rerunMaterial'),\n                                                                            \":\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1645,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    t('schedule.deleteRerunInfo')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1645,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.deleteConfirmInfo')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1646,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1643,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1641,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1631,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.primeTimeTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1653,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.primeTimeSchedule1')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1655,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.primeTimeSchedule2')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1656,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    style: {\n                                                                        color: '#fbbf24'\n                                                                    },\n                                                                    children: t('schedule.primeTimeColor')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1657,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1657,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1654,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1652,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.rerunsTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1661,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: t('schedule.rerunsSchedule1')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1663,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1663,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart1Sun')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1665,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart2Sun')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1666,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1664,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: t('schedule.rerunsSchedule2')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1668,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1668,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart1Thu')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1670,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart2Thu')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1669,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    style: {\n                                                                        color: '#9ca3af'\n                                                                    },\n                                                                    children: t('schedule.rerunsColor')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1673,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1673,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1662,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1660,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1651,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #f59e0b'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: t('schedule.dateManagementTitle')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1679,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: [\n                                                    \" \",\n                                                    t('schedule.dateManagementInfo')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1680,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1678,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #3b82f6'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: t('schedule.importantNoteTitle')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1684,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: [\n                                                    \" \",\n                                                    t('schedule.importantNoteInfo')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1685,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1683,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1622,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1320,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 972,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 971,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 970,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"z+bOjI1IMp+y5Jz2wzsc+HDFldA=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ })

});