/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/dashboard/page"],{

/***/ "(app-pages-browser)/./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var void_elements__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! void-elements */ \"(app-pages-browser)/./node_modules/void-elements/index.js\");\n/* harmony import */ var void_elements__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(void_elements__WEBPACK_IMPORTED_MODULE_0__);\nvar t=/\\s([^'\"/\\s><]+?)[\\s/>]|([^\\s=]+)=\\s?(\".*?\"|'.*?')/g;function n(n){var r={type:\"tag\",name:\"\",voidElement:!1,attrs:{},children:[]},i=n.match(/<\\/?([^\\s]+?)[/\\s>]/);if(i&&(r.name=i[1],((void_elements__WEBPACK_IMPORTED_MODULE_0___default())[i[1]]||\"/\"===n.charAt(n.length-2))&&(r.voidElement=!0),r.name.startsWith(\"!--\"))){var s=n.indexOf(\"--\\x3e\");return{type:\"comment\",comment:-1!==s?n.slice(4,s):\"\"}}for(var a=new RegExp(t),c=null;null!==(c=a.exec(n));)if(c[0].trim())if(c[1]){var o=c[1].trim(),l=[o,\"\"];o.indexOf(\"=\")>-1&&(l=o.split(\"=\")),r.attrs[l[0]]=l[1],a.lastIndex--}else c[2]&&(r.attrs[c[2]]=c[3].trim().substring(1,c[3].length-1));return r}var r=/<[a-zA-Z0-9\\-\\!\\/](?:\"[^\"]*\"|'[^']*'|[^'\">])*>/g,i=/^\\s*$/,s=Object.create(null);function a(e,t){switch(t.type){case\"text\":return e+t.content;case\"tag\":return e+=\"<\"+t.name+(t.attrs?function(e){var t=[];for(var n in e)t.push(n+'=\"'+e[n]+'\"');return t.length?\" \"+t.join(\" \"):\"\"}(t.attrs):\"\")+(t.voidElement?\"/>\":\">\"),t.voidElement?e:e+t.children.reduce(a,\"\")+\"</\"+t.name+\">\";case\"comment\":return e+\"\\x3c!--\"+t.comment+\"--\\x3e\"}}var c={parse:function(e,t){t||(t={}),t.components||(t.components=s);var a,c=[],o=[],l=-1,m=!1;if(0!==e.indexOf(\"<\")){var u=e.indexOf(\"<\");c.push({type:\"text\",content:-1===u?e:e.substring(0,u)})}return e.replace(r,function(r,s){if(m){if(r!==\"</\"+a.name+\">\")return;m=!1}var u,f=\"/\"!==r.charAt(1),h=r.startsWith(\"\\x3c!--\"),p=s+r.length,d=e.charAt(p);if(h){var v=n(r);return l<0?(c.push(v),c):((u=o[l]).children.push(v),c)}if(f&&(l++,\"tag\"===(a=n(r)).type&&t.components[a.name]&&(a.type=\"component\",m=!0),a.voidElement||m||!d||\"<\"===d||a.children.push({type:\"text\",content:e.slice(p,e.indexOf(\"<\",p))}),0===l&&c.push(a),(u=o[l-1])&&u.children.push(a),o[l]=a),(!f||a.voidElement)&&(l>-1&&(a.voidElement||a.name===r.slice(2,-1))&&(l--,a=-1===l?c:o[l]),!m&&\"<\"!==d&&d)){u=-1===l?c:o[l].children;var x=e.indexOf(\"<\",p),g=e.slice(p,-1===x?void 0:x);i.test(g)&&(g=\" \"),(x>-1&&l+u.length>=0||\" \"!==g)&&u.push({type:\"text\",content:g})}}),c},stringify:function(e){return e.reduce(function(e,t){return e+a(\"\",t)},\"\")}};/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (c);\n//# sourceMappingURL=html-parse-stringify.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(app-pages-browser)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3Byb2plY3QlMjBzcG9ydCU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Rhc2hib2FyZCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0xBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxwcm9qZWN0IHNwb3J0XFxcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcXFxtZWRpYS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/I18nextProvider.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/I18nextProvider.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nextProvider: () => (/* binding */ I18nextProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nfunction I18nextProvider({\n  i18n,\n  defaultNS,\n  children\n}) {\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext.Provider, {\n    value\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvSTE4bmV4dFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUNKO0FBQ3BDO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELGdCQUFnQiw4Q0FBTztBQUN2QjtBQUNBO0FBQ0EsR0FBRztBQUNILFNBQVMsb0RBQWEsQ0FBQyxvREFBVztBQUNsQztBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxJMThuZXh0UHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlRWxlbWVudCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEkxOG5Db250ZXh0IH0gZnJvbSAnLi9jb250ZXh0LmpzJztcbmV4cG9ydCBmdW5jdGlvbiBJMThuZXh0UHJvdmlkZXIoe1xuICBpMThuLFxuICBkZWZhdWx0TlMsXG4gIGNoaWxkcmVuXG59KSB7XG4gIGNvbnN0IHZhbHVlID0gdXNlTWVtbygoKSA9PiAoe1xuICAgIGkxOG4sXG4gICAgZGVmYXVsdE5TXG4gIH0pLCBbaTE4biwgZGVmYXVsdE5TXSk7XG4gIHJldHVybiBjcmVhdGVFbGVtZW50KEkxOG5Db250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWVcbiAgfSwgY2hpbGRyZW4pO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/I18nextProvider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/Trans.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Trans.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\nfunction Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_2__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return (0,_TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans)({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvVHJhbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUM7QUFDb0Q7QUFDbkM7QUFDM0I7QUFDbEI7QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsSUFBSSxFQUFFLGlEQUFVLENBQUMsb0RBQVc7QUFDNUIsbURBQW1ELG9EQUFPO0FBQzFEO0FBQ0EsU0FBUyw4REFBbUI7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxUcmFucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbm9kZXNUb1N0cmluZywgVHJhbnMgYXMgVHJhbnNXaXRob3V0Q29udGV4dCB9IGZyb20gJy4vVHJhbnNXaXRob3V0Q29udGV4dC5qcyc7XG5pbXBvcnQgeyBnZXRJMThuLCBJMThuQ29udGV4dCB9IGZyb20gJy4vY29udGV4dC5qcyc7XG5leHBvcnQgeyBub2Rlc1RvU3RyaW5nIH07XG5leHBvcnQgZnVuY3Rpb24gVHJhbnMoe1xuICBjaGlsZHJlbixcbiAgY291bnQsXG4gIHBhcmVudCxcbiAgaTE4bktleSxcbiAgY29udGV4dCxcbiAgdE9wdGlvbnMgPSB7fSxcbiAgdmFsdWVzLFxuICBkZWZhdWx0cyxcbiAgY29tcG9uZW50cyxcbiAgbnMsXG4gIGkxOG46IGkxOG5Gcm9tUHJvcHMsXG4gIHQ6IHRGcm9tUHJvcHMsXG4gIHNob3VsZFVuZXNjYXBlLFxuICAuLi5hZGRpdGlvbmFsUHJvcHNcbn0pIHtcbiAgY29uc3Qge1xuICAgIGkxOG46IGkxOG5Gcm9tQ29udGV4dCxcbiAgICBkZWZhdWx0TlM6IGRlZmF1bHROU0Zyb21Db250ZXh0XG4gIH0gPSB1c2VDb250ZXh0KEkxOG5Db250ZXh0KSB8fCB7fTtcbiAgY29uc3QgaTE4biA9IGkxOG5Gcm9tUHJvcHMgfHwgaTE4bkZyb21Db250ZXh0IHx8IGdldEkxOG4oKTtcbiAgY29uc3QgdCA9IHRGcm9tUHJvcHMgfHwgaTE4bj8udC5iaW5kKGkxOG4pO1xuICByZXR1cm4gVHJhbnNXaXRob3V0Q29udGV4dCh7XG4gICAgY2hpbGRyZW4sXG4gICAgY291bnQsXG4gICAgcGFyZW50LFxuICAgIGkxOG5LZXksXG4gICAgY29udGV4dCxcbiAgICB0T3B0aW9ucyxcbiAgICB2YWx1ZXMsXG4gICAgZGVmYXVsdHMsXG4gICAgY29tcG9uZW50cyxcbiAgICBuczogbnMgfHwgdD8ubnMgfHwgZGVmYXVsdE5TRnJvbUNvbnRleHQgfHwgaTE4bj8ub3B0aW9ucz8uZGVmYXVsdE5TLFxuICAgIGkxOG4sXG4gICAgdDogdEZyb21Qcm9wcyxcbiAgICBzaG91bGRVbmVzY2FwZSxcbiAgICAuLi5hZGRpdGlvbmFsUHJvcHNcbiAgfSk7XG59Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/Trans.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/TransWithoutContext.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/TransWithoutContext.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* binding */ nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! html-parse-stringify */ \"(app-pages-browser)/./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./defaults.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./i18nInstance.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\n\n\n\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(react__WEBPACK_IMPORTED_MODULE_0__.isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nconst nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, knownComponentsMap, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !knownComponentsMap && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = knownComponentsMap ?? {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && !(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...react__WEBPACK_IMPORTED_MODULE_0__.Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (!tmp && knownComponentsMap) tmp = knownComponentsMap[node.name];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && child.dummy && !isElement;\n        const isKnownComponent = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(knownComponentsMap) && Object.hasOwnProperty.call(knownComponentsMap, node.name);\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, comp);\n  }\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nconst isComponentsMap = object => {\n  if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(object)) return false;\n  if (Array.isArray(object)) return false;\n  return Object.keys(object).reduce((acc, key) => acc && Number.isNaN(Number.parseFloat(key)), true);\n};\nfunction Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__.getI18n)();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...(0,_defaults_js__WEBPACK_IMPORTED_MODULE_3__.getDefaults)(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  let indexedChildren = generatedComponents || children;\n  let componentsMap = null;\n  if (isComponentsMap(generatedComponents)) {\n    componentsMap = generatedComponents;\n    indexedChildren = children;\n  }\n  const content = renderNodes(indexedChildren, componentsMap, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(useAsParent, additionalProps, content) : content;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/Translation.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Translation.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Translation: () => (/* binding */ Translation)\n/* harmony export */ });\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useTranslation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n\nconst Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_0__.useTranslation)(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvVHJhbnNsYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFDOUM7QUFDUDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsMkJBQTJCLGtFQUFjO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxUcmFuc2xhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJy4vdXNlVHJhbnNsYXRpb24uanMnO1xuZXhwb3J0IGNvbnN0IFRyYW5zbGF0aW9uID0gKHtcbiAgbnMsXG4gIGNoaWxkcmVuLFxuICAuLi5vcHRpb25zXG59KSA9PiB7XG4gIGNvbnN0IFt0LCBpMThuLCByZWFkeV0gPSB1c2VUcmFuc2xhdGlvbihucywgb3B0aW9ucyk7XG4gIHJldHVybiBjaGlsZHJlbih0LCB7XG4gICAgaTE4bixcbiAgICBsbmc6IGkxOG4ubGFuZ3VhZ2VcbiAgfSwgcmVhZHkpO1xufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/Translation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/context.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* binding */ I18nContext),\n/* harmony export */   ReportNamespaces: () => (/* binding */ ReportNamespaces),\n/* harmony export */   composeInitialProps: () => (/* binding */ composeInitialProps),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n),\n/* harmony export */   getInitialProps: () => (/* binding */ getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__.initReactI18next),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.setI18n)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaults.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./i18nInstance.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initReactI18next.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n\n\n\n\n\nconst I18nContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();\nclass ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nconst composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nconst getInitialProps = () => {\n  const i18n = (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQXNDO0FBQ21CO0FBQ0o7QUFDSTtBQUNlO0FBQ2pFLG9CQUFvQixvREFBYTtBQUNqQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsZUFBZSx5REFBTztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcY29udGV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0RGVmYXVsdHMsIHNldERlZmF1bHRzIH0gZnJvbSAnLi9kZWZhdWx0cy5qcyc7XG5pbXBvcnQgeyBnZXRJMThuLCBzZXRJMThuIH0gZnJvbSAnLi9pMThuSW5zdGFuY2UuanMnO1xuaW1wb3J0IHsgaW5pdFJlYWN0STE4bmV4dCB9IGZyb20gJy4vaW5pdFJlYWN0STE4bmV4dC5qcyc7XG5leHBvcnQgeyBnZXREZWZhdWx0cywgc2V0RGVmYXVsdHMsIGdldEkxOG4sIHNldEkxOG4sIGluaXRSZWFjdEkxOG5leHQgfTtcbmV4cG9ydCBjb25zdCBJMThuQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQoKTtcbmV4cG9ydCBjbGFzcyBSZXBvcnROYW1lc3BhY2VzIHtcbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy51c2VkTmFtZXNwYWNlcyA9IHt9O1xuICB9XG4gIGFkZFVzZWROYW1lc3BhY2VzKG5hbWVzcGFjZXMpIHtcbiAgICBuYW1lc3BhY2VzLmZvckVhY2gobnMgPT4ge1xuICAgICAgaWYgKCF0aGlzLnVzZWROYW1lc3BhY2VzW25zXSkgdGhpcy51c2VkTmFtZXNwYWNlc1tuc10gPSB0cnVlO1xuICAgIH0pO1xuICB9XG4gIGdldFVzZWROYW1lc3BhY2VzKCkge1xuICAgIHJldHVybiBPYmplY3Qua2V5cyh0aGlzLnVzZWROYW1lc3BhY2VzKTtcbiAgfVxufVxuZXhwb3J0IGNvbnN0IGNvbXBvc2VJbml0aWFsUHJvcHMgPSBGb3JDb21wb25lbnQgPT4gYXN5bmMgY3R4ID0+IHtcbiAgY29uc3QgY29tcG9uZW50c0luaXRpYWxQcm9wcyA9IChhd2FpdCBGb3JDb21wb25lbnQuZ2V0SW5pdGlhbFByb3BzPy4oY3R4KSkgPz8ge307XG4gIGNvbnN0IGkxOG5Jbml0aWFsUHJvcHMgPSBnZXRJbml0aWFsUHJvcHMoKTtcbiAgcmV0dXJuIHtcbiAgICAuLi5jb21wb25lbnRzSW5pdGlhbFByb3BzLFxuICAgIC4uLmkxOG5Jbml0aWFsUHJvcHNcbiAgfTtcbn07XG5leHBvcnQgY29uc3QgZ2V0SW5pdGlhbFByb3BzID0gKCkgPT4ge1xuICBjb25zdCBpMThuID0gZ2V0STE4bigpO1xuICBjb25zdCBuYW1lc3BhY2VzID0gaTE4bi5yZXBvcnROYW1lc3BhY2VzPy5nZXRVc2VkTmFtZXNwYWNlcygpID8/IFtdO1xuICBjb25zdCByZXQgPSB7fTtcbiAgY29uc3QgaW5pdGlhbEkxOG5TdG9yZSA9IHt9O1xuICBpMThuLmxhbmd1YWdlcy5mb3JFYWNoKGwgPT4ge1xuICAgIGluaXRpYWxJMThuU3RvcmVbbF0gPSB7fTtcbiAgICBuYW1lc3BhY2VzLmZvckVhY2gobnMgPT4ge1xuICAgICAgaW5pdGlhbEkxOG5TdG9yZVtsXVtuc10gPSBpMThuLmdldFJlc291cmNlQnVuZGxlKGwsIG5zKSB8fCB7fTtcbiAgICB9KTtcbiAgfSk7XG4gIHJldC5pbml0aWFsSTE4blN0b3JlID0gaW5pdGlhbEkxOG5TdG9yZTtcbiAgcmV0LmluaXRpYWxMYW5ndWFnZSA9IGkxOG4ubGFuZ3VhZ2U7XG4gIHJldHVybiByZXQ7XG59OyJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/defaults.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaults: () => (/* binding */ getDefaults),\n/* harmony export */   setDefaults: () => (/* binding */ setDefaults)\n/* harmony export */ });\n/* harmony import */ var _unescape_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unescape.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/unescape.js\");\n\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape: _unescape_js__WEBPACK_IMPORTED_MODULE_0__.unescape\n};\nconst setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nconst getDefaults = () => defaultOptions;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvZGVmYXVsdHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTyxpQ0FBaUM7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXGRlZmF1bHRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVuZXNjYXBlIH0gZnJvbSAnLi91bmVzY2FwZS5qcyc7XG5sZXQgZGVmYXVsdE9wdGlvbnMgPSB7XG4gIGJpbmRJMThuOiAnbGFuZ3VhZ2VDaGFuZ2VkJyxcbiAgYmluZEkxOG5TdG9yZTogJycsXG4gIHRyYW5zRW1wdHlOb2RlVmFsdWU6ICcnLFxuICB0cmFuc1N1cHBvcnRCYXNpY0h0bWxOb2RlczogdHJ1ZSxcbiAgdHJhbnNXcmFwVGV4dE5vZGVzOiAnJyxcbiAgdHJhbnNLZWVwQmFzaWNIdG1sTm9kZXNGb3I6IFsnYnInLCAnc3Ryb25nJywgJ2knLCAncCddLFxuICB1c2VTdXNwZW5zZTogdHJ1ZSxcbiAgdW5lc2NhcGVcbn07XG5leHBvcnQgY29uc3Qgc2V0RGVmYXVsdHMgPSAob3B0aW9ucyA9IHt9KSA9PiB7XG4gIGRlZmF1bHRPcHRpb25zID0ge1xuICAgIC4uLmRlZmF1bHRPcHRpb25zLFxuICAgIC4uLm9wdGlvbnNcbiAgfTtcbn07XG5leHBvcnQgY29uc3QgZ2V0RGVmYXVsdHMgPSAoKSA9PiBkZWZhdWx0T3B0aW9uczsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js":
/*!************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/i18nInstance.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   setI18n: () => (/* binding */ setI18n)\n/* harmony export */ });\nlet i18nInstance;\nconst setI18n = instance => {\n  i18nInstance = instance;\n};\nconst getI18n = () => i18nInstance;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvaTE4bkluc3RhbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxpMThuSW5zdGFuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGkxOG5JbnN0YW5jZTtcbmV4cG9ydCBjb25zdCBzZXRJMThuID0gaW5zdGFuY2UgPT4ge1xuICBpMThuSW5zdGFuY2UgPSBpbnN0YW5jZTtcbn07XG5leHBvcnQgY29uc3QgZ2V0STE4biA9ICgpID0+IGkxOG5JbnN0YW5jZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.I18nContext),\n/* harmony export */   I18nextProvider: () => (/* reexport safe */ _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__.I18nextProvider),\n/* harmony export */   Trans: () => (/* reexport safe */ _Trans_js__WEBPACK_IMPORTED_MODULE_0__.Trans),\n/* harmony export */   TransWithoutContext: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans),\n/* harmony export */   Translation: () => (/* reexport safe */ _Translation_js__WEBPACK_IMPORTED_MODULE_4__.Translation),\n/* harmony export */   composeInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.composeInitialProps),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.getI18n),\n/* harmony export */   getInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__.initReactI18next),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   plural: () => (/* binding */ plural),\n/* harmony export */   select: () => (/* binding */ select),\n/* harmony export */   selectOrdinal: () => (/* binding */ selectOrdinal),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.setI18n),\n/* harmony export */   time: () => (/* binding */ time),\n/* harmony export */   useSSR: () => (/* reexport safe */ _useSSR_js__WEBPACK_IMPORTED_MODULE_7__.useSSR),\n/* harmony export */   useTranslation: () => (/* reexport safe */ _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__.useTranslation),\n/* harmony export */   withSSR: () => (/* reexport safe */ _withSSR_js__WEBPACK_IMPORTED_MODULE_6__.withSSR),\n/* harmony export */   withTranslation: () => (/* reexport safe */ _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__.withTranslation)\n/* harmony export */ });\n/* harmony import */ var _Trans_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Trans.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/Trans.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useTranslation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./withTranslation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/withTranslation.js\");\n/* harmony import */ var _Translation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Translation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/Translation.js\");\n/* harmony import */ var _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./I18nextProvider.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/I18nextProvider.js\");\n/* harmony import */ var _withSSR_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./withSSR.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/withSSR.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useSSR.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./initReactI18next.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./defaults.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./i18nInstance.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst date = () => '';\nconst time = () => '';\nconst number = () => '';\nconst select = () => '';\nconst plural = () => '';\nconst selectOrdinal = () => '';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFtQztBQUNxQztBQUNuQjtBQUNFO0FBQ1I7QUFDUTtBQUNoQjtBQUNGO0FBQ29CO0FBQ0E7QUFDSjtBQUM0QjtBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgVHJhbnMgfSBmcm9tICcuL1RyYW5zLmpzJztcbmV4cG9ydCB7IFRyYW5zIGFzIFRyYW5zV2l0aG91dENvbnRleHQgfSBmcm9tICcuL1RyYW5zV2l0aG91dENvbnRleHQuanMnO1xuZXhwb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICcuL3VzZVRyYW5zbGF0aW9uLmpzJztcbmV4cG9ydCB7IHdpdGhUcmFuc2xhdGlvbiB9IGZyb20gJy4vd2l0aFRyYW5zbGF0aW9uLmpzJztcbmV4cG9ydCB7IFRyYW5zbGF0aW9uIH0gZnJvbSAnLi9UcmFuc2xhdGlvbi5qcyc7XG5leHBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICcuL0kxOG5leHRQcm92aWRlci5qcyc7XG5leHBvcnQgeyB3aXRoU1NSIH0gZnJvbSAnLi93aXRoU1NSLmpzJztcbmV4cG9ydCB7IHVzZVNTUiB9IGZyb20gJy4vdXNlU1NSLmpzJztcbmV4cG9ydCB7IGluaXRSZWFjdEkxOG5leHQgfSBmcm9tICcuL2luaXRSZWFjdEkxOG5leHQuanMnO1xuZXhwb3J0IHsgc2V0RGVmYXVsdHMsIGdldERlZmF1bHRzIH0gZnJvbSAnLi9kZWZhdWx0cy5qcyc7XG5leHBvcnQgeyBzZXRJMThuLCBnZXRJMThuIH0gZnJvbSAnLi9pMThuSW5zdGFuY2UuanMnO1xuZXhwb3J0IHsgSTE4bkNvbnRleHQsIGNvbXBvc2VJbml0aWFsUHJvcHMsIGdldEluaXRpYWxQcm9wcyB9IGZyb20gJy4vY29udGV4dC5qcyc7XG5leHBvcnQgY29uc3QgZGF0ZSA9ICgpID0+ICcnO1xuZXhwb3J0IGNvbnN0IHRpbWUgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBudW1iZXIgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBzZWxlY3QgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBwbHVyYWwgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBzZWxlY3RPcmRpbmFsID0gKCkgPT4gJyc7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/initReactI18next.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/initReactI18next.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initReactI18next: () => (/* binding */ initReactI18next)\n/* harmony export */ });\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaults.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./i18nInstance.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\nconst initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    (0,_defaults_js__WEBPACK_IMPORTED_MODULE_0__.setDefaults)(instance.options.react);\n    (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__.setI18n)(instance);\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvaW5pdFJlYWN0STE4bmV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFDQTtBQUNyQztBQUNQO0FBQ0E7QUFDQSxJQUFJLHlEQUFXO0FBQ2YsSUFBSSx5REFBTztBQUNYO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcaW5pdFJlYWN0STE4bmV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzZXREZWZhdWx0cyB9IGZyb20gJy4vZGVmYXVsdHMuanMnO1xuaW1wb3J0IHsgc2V0STE4biB9IGZyb20gJy4vaTE4bkluc3RhbmNlLmpzJztcbmV4cG9ydCBjb25zdCBpbml0UmVhY3RJMThuZXh0ID0ge1xuICB0eXBlOiAnM3JkUGFydHknLFxuICBpbml0KGluc3RhbmNlKSB7XG4gICAgc2V0RGVmYXVsdHMoaW5zdGFuY2Uub3B0aW9ucy5yZWFjdCk7XG4gICAgc2V0STE4bihpbnN0YW5jZSk7XG4gIH1cbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/initReactI18next.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/unescape.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/unescape.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unescape: () => (/* binding */ unescape)\n/* harmony export */ });\nconst matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nconst unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvdW5lc2NhcGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHlIQUF5SDtBQUN6SDtBQUNBLFFBQVE7QUFDUixRQUFRO0FBQ1IsT0FBTztBQUNQLFFBQVE7QUFDUixPQUFPO0FBQ1AsUUFBUTtBQUNSLFNBQVM7QUFDVCxRQUFRO0FBQ1IsU0FBUztBQUNULFFBQVE7QUFDUixTQUFTO0FBQ1QsU0FBUztBQUNULFNBQVM7QUFDVCxTQUFTO0FBQ1QsUUFBUTtBQUNSLFNBQVM7QUFDVCxXQUFXO0FBQ1gsVUFBVTtBQUNWLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNPIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXHVuZXNjYXBlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1hdGNoSHRtbEVudGl0eSA9IC8mKD86YW1wfCMzOHxsdHwjNjB8Z3R8IzYyfGFwb3N8IzM5fHF1b3R8IzM0fG5ic3B8IzE2MHxjb3B5fCMxNjl8cmVnfCMxNzR8aGVsbGlwfCM4MjMwfCN4MkZ8IzQ3KTsvZztcbmNvbnN0IGh0bWxFbnRpdGllcyA9IHtcbiAgJyZhbXA7JzogJyYnLFxuICAnJiMzODsnOiAnJicsXG4gICcmbHQ7JzogJzwnLFxuICAnJiM2MDsnOiAnPCcsXG4gICcmZ3Q7JzogJz4nLFxuICAnJiM2MjsnOiAnPicsXG4gICcmYXBvczsnOiBcIidcIixcbiAgJyYjMzk7JzogXCInXCIsXG4gICcmcXVvdDsnOiAnXCInLFxuICAnJiMzNDsnOiAnXCInLFxuICAnJm5ic3A7JzogJyAnLFxuICAnJiMxNjA7JzogJyAnLFxuICAnJmNvcHk7JzogJ8KpJyxcbiAgJyYjMTY5Oyc6ICfCqScsXG4gICcmcmVnOyc6ICfCricsXG4gICcmIzE3NDsnOiAnwq4nLFxuICAnJmhlbGxpcDsnOiAn4oCmJyxcbiAgJyYjODIzMDsnOiAn4oCmJyxcbiAgJyYjeDJGOyc6ICcvJyxcbiAgJyYjNDc7JzogJy8nXG59O1xuY29uc3QgdW5lc2NhcGVIdG1sRW50aXR5ID0gbSA9PiBodG1sRW50aXRpZXNbbV07XG5leHBvcnQgY29uc3QgdW5lc2NhcGUgPSB0ZXh0ID0+IHRleHQucmVwbGFjZShtYXRjaEh0bWxFbnRpdHksIHVuZXNjYXBlSHRtbEVudGl0eSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/unescape.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/useSSR.js":
/*!******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useSSR.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSSR: () => (/* binding */ useSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nconst useSSR = (initialI18nStore, initialLanguage, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n.options?.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvdXNlU1NSLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFtQztBQUNpQjtBQUM3Qyw2REFBNkQ7QUFDcEU7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSSxFQUFFLGlEQUFVLENBQUMsb0RBQVc7QUFDNUIsbURBQW1ELG9EQUFPO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXHVzZVNTUi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0STE4biwgSTE4bkNvbnRleHQgfSBmcm9tICcuL2NvbnRleHQuanMnO1xuZXhwb3J0IGNvbnN0IHVzZVNTUiA9IChpbml0aWFsSTE4blN0b3JlLCBpbml0aWFsTGFuZ3VhZ2UsIHByb3BzID0ge30pID0+IHtcbiAgY29uc3Qge1xuICAgIGkxOG46IGkxOG5Gcm9tUHJvcHNcbiAgfSA9IHByb3BzO1xuICBjb25zdCB7XG4gICAgaTE4bjogaTE4bkZyb21Db250ZXh0XG4gIH0gPSB1c2VDb250ZXh0KEkxOG5Db250ZXh0KSB8fCB7fTtcbiAgY29uc3QgaTE4biA9IGkxOG5Gcm9tUHJvcHMgfHwgaTE4bkZyb21Db250ZXh0IHx8IGdldEkxOG4oKTtcbiAgaWYgKGkxOG4ub3B0aW9ucz8uaXNDbG9uZSkgcmV0dXJuO1xuICBpZiAoaW5pdGlhbEkxOG5TdG9yZSAmJiAhaTE4bi5pbml0aWFsaXplZFN0b3JlT25jZSkge1xuICAgIGkxOG4uc2VydmljZXMucmVzb3VyY2VTdG9yZS5kYXRhID0gaW5pdGlhbEkxOG5TdG9yZTtcbiAgICBpMThuLm9wdGlvbnMubnMgPSBPYmplY3QudmFsdWVzKGluaXRpYWxJMThuU3RvcmUpLnJlZHVjZSgobWVtLCBsbmdSZXNvdXJjZXMpID0+IHtcbiAgICAgIE9iamVjdC5rZXlzKGxuZ1Jlc291cmNlcykuZm9yRWFjaChucyA9PiB7XG4gICAgICAgIGlmIChtZW0uaW5kZXhPZihucykgPCAwKSBtZW0ucHVzaChucyk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBtZW07XG4gICAgfSwgaTE4bi5vcHRpb25zLm5zKTtcbiAgICBpMThuLmluaXRpYWxpemVkU3RvcmVPbmNlID0gdHJ1ZTtcbiAgICBpMThuLmlzSW5pdGlhbGl6ZWQgPSB0cnVlO1xuICB9XG4gIGlmIChpbml0aWFsTGFuZ3VhZ2UgJiYgIWkxOG4uaW5pdGlhbGl6ZWRMYW5ndWFnZU9uY2UpIHtcbiAgICBpMThuLmNoYW5nZUxhbmd1YWdlKGluaXRpYWxMYW5ndWFnZSk7XG4gICAgaTE4bi5pbml0aWFsaXplZExhbmd1YWdlT25jZSA9IHRydWU7XG4gIH1cbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/useSSR.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useTranslation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst usePrevious = (value, ignore) => {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nconst useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new _context_js__WEBPACK_IMPORTED_MODULE_1__.ReportNamespaces();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(optsOrDefaultValue)) return optsOrDefaultValue;\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(optsOrDefaultValue) && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...(0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults)(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasLoadedNamespace)(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => resolve());\n    }\n  });\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDisplayName: () => (/* binding */ getDisplayName),\n/* harmony export */   hasLoadedNamespace: () => (/* binding */ hasLoadedNamespace),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   loadLanguages: () => (/* binding */ loadLanguages),\n/* harmony export */   loadNamespaces: () => (/* binding */ loadNamespaces),\n/* harmony export */   warn: () => (/* binding */ warn),\n/* harmony export */   warnOnce: () => (/* binding */ warnOnce)\n/* harmony export */ });\nconst warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nconst warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nconst loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nconst loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nconst hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nconst getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nconst isString = obj => typeof obj === 'string';\nconst isObject = obj => typeof obj === 'object' && obj !== null;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/withSSR.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withSSR.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withSSR: () => (/* binding */ withSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useSSR.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\n\nconst withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    (0,_useSSR_js__WEBPACK_IMPORTED_MODULE_1__.useSSR)(initialI18nStore, initialLanguage);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.composeInitialProps)(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.getDisplayName)(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvd2l0aFNTUi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzQztBQUNEO0FBQ2M7QUFDUDtBQUNyQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILElBQUksa0RBQU07QUFDVixXQUFXLG9EQUFhO0FBQ3hCO0FBQ0EsS0FBSztBQUNMO0FBQ0EsbUNBQW1DLGdFQUFtQjtBQUN0RCxpREFBaUQseURBQWMsbUJBQW1CO0FBQ2xGO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFx3aXRoU1NSLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUVsZW1lbnQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VTU1IgfSBmcm9tICcuL3VzZVNTUi5qcyc7XG5pbXBvcnQgeyBjb21wb3NlSW5pdGlhbFByb3BzIH0gZnJvbSAnLi9jb250ZXh0LmpzJztcbmltcG9ydCB7IGdldERpc3BsYXlOYW1lIH0gZnJvbSAnLi91dGlscy5qcyc7XG5leHBvcnQgY29uc3Qgd2l0aFNTUiA9ICgpID0+IGZ1bmN0aW9uIEV4dGVuZChXcmFwcGVkQ29tcG9uZW50KSB7XG4gIGZ1bmN0aW9uIEkxOG5leHRXaXRoU1NSKHtcbiAgICBpbml0aWFsSTE4blN0b3JlLFxuICAgIGluaXRpYWxMYW5ndWFnZSxcbiAgICAuLi5yZXN0XG4gIH0pIHtcbiAgICB1c2VTU1IoaW5pdGlhbEkxOG5TdG9yZSwgaW5pdGlhbExhbmd1YWdlKTtcbiAgICByZXR1cm4gY3JlYXRlRWxlbWVudChXcmFwcGVkQ29tcG9uZW50LCB7XG4gICAgICAuLi5yZXN0XG4gICAgfSk7XG4gIH1cbiAgSTE4bmV4dFdpdGhTU1IuZ2V0SW5pdGlhbFByb3BzID0gY29tcG9zZUluaXRpYWxQcm9wcyhXcmFwcGVkQ29tcG9uZW50KTtcbiAgSTE4bmV4dFdpdGhTU1IuZGlzcGxheU5hbWUgPSBgd2l0aEkxOG5leHRTU1IoJHtnZXREaXNwbGF5TmFtZShXcmFwcGVkQ29tcG9uZW50KX0pYDtcbiAgSTE4bmV4dFdpdGhTU1IuV3JhcHBlZENvbXBvbmVudCA9IFdyYXBwZWRDb21wb25lbnQ7XG4gIHJldHVybiBJMThuZXh0V2l0aFNTUjtcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/withSSR.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/withTranslation.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withTranslation.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withTranslation: () => (/* binding */ withTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useTranslation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst withTranslation = (ns, options = {}) => function Extend(WrappedComponent) {\n  function I18nextWithTranslation({\n    forwardedRef,\n    ...rest\n  }) {\n    const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(ns, {\n      ...rest,\n      keyPrefix: options.keyPrefix\n    });\n    const passDownProps = {\n      ...rest,\n      t,\n      i18n,\n      tReady: ready\n    };\n    if (options.withRef && forwardedRef) {\n      passDownProps.ref = forwardedRef;\n    } else if (!options.withRef && forwardedRef) {\n      passDownProps.forwardedRef = forwardedRef;\n    }\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, passDownProps);\n  }\n  I18nextWithTranslation.displayName = `withI18nextTranslation(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getDisplayName)(WrappedComponent)})`;\n  I18nextWithTranslation.WrappedComponent = WrappedComponent;\n  const forwardRef = (props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(I18nextWithTranslation, Object.assign({}, props, {\n    forwardedRef: ref\n  }));\n  return options.withRef ? (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(forwardRef) : I18nextWithTranslation;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/withTranslation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/void-elements/index.js":
/*!*********************************************!*\
  !*** ./node_modules/void-elements/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * This file automatically generated from `pre-publish.js`.\n * Do not manually edit.\n */\n\nmodule.exports = {\n  \"area\": true,\n  \"base\": true,\n  \"br\": true,\n  \"col\": true,\n  \"embed\": true,\n  \"hr\": true,\n  \"img\": true,\n  \"input\": true,\n  \"link\": true,\n  \"meta\": true,\n  \"param\": true,\n  \"source\": true,\n  \"track\": true,\n  \"wbr\": true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy92b2lkLWVsZW1lbnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcdm9pZC1lbGVtZW50c1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUaGlzIGZpbGUgYXV0b21hdGljYWxseSBnZW5lcmF0ZWQgZnJvbSBgcHJlLXB1Ymxpc2guanNgLlxuICogRG8gbm90IG1hbnVhbGx5IGVkaXQuXG4gKi9cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIFwiYXJlYVwiOiB0cnVlLFxuICBcImJhc2VcIjogdHJ1ZSxcbiAgXCJiclwiOiB0cnVlLFxuICBcImNvbFwiOiB0cnVlLFxuICBcImVtYmVkXCI6IHRydWUsXG4gIFwiaHJcIjogdHJ1ZSxcbiAgXCJpbWdcIjogdHJ1ZSxcbiAgXCJpbnB1dFwiOiB0cnVlLFxuICBcImxpbmtcIjogdHJ1ZSxcbiAgXCJtZXRhXCI6IHRydWUsXG4gIFwicGFyYW1cIjogdHJ1ZSxcbiAgXCJzb3VyY2VcIjogdHJ1ZSxcbiAgXCJ0cmFja1wiOiB0cnVlLFxuICBcIndiclwiOiB0cnVlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/void-elements/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NavigationCard */ \"(app-pages-browser)/./src/components/NavigationCard.tsx\");\n/* harmony import */ var _components_Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _styles_dashboard_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/dashboard.css */ \"(app-pages-browser)/./src/styles/dashboard.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__.useAppTranslation)();\n    // تعريف عناصر التنقل في القائمة العلوية\n    const topNavigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            active: false,\n            path: '/reports'\n        },\n        ...(user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && (user === null || user === void 0 ? void 0 : user.username) === 'admin' ? [\n            {\n                name: t('navigation.unifiedSystem'),\n                icon: '📤',\n                active: false,\n                path: '/unified-system'\n            }\n        ] : [],\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard'\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics'\n        }\n    ];\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Dashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Dashboard.useEffect.timer\"], 1000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // بيانات الإحصائيات الحقيقية\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMedia: 0,\n        validMedia: 0,\n        rejectedMedia: 0,\n        expiredMedia: 0,\n        pendingMedia: 0,\n        activeUsers: 0,\n        onlineUsers: 0,\n        todayAdded: 0\n    });\n    // حالة تنبيه المواد المنتهية\n    const [expiredAlertShown, setExpiredAlertShown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تخزين المواد المنتهية للتنبيه\n    const [expiredMediaItems, setExpiredMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showExpiredAlert, setShowExpiredAlert] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // جلب البيانات الحقيقية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchRealStats();\n            // تحديث البيانات كل 30 ثانية\n            const interval = setInterval(fetchRealStats, 30000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(interval)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchRealStats = async ()=>{\n        try {\n            setLoading(true);\n            // جلب البيانات من API مع timeout أطول\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 15000); // 15 ثانية timeout\n            const [mediaResponse, usersResponse] = await Promise.all([\n                fetch('/api/media', {\n                    signal: controller.signal,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                }),\n                fetch('/api/users', {\n                    signal: controller.signal,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                })\n            ]);\n            clearTimeout(timeoutId);\n            let mediaData = [];\n            let userData = [];\n            if (mediaResponse.ok) {\n                try {\n                    const mediaResult = await mediaResponse.json();\n                    mediaData = mediaResult.success ? mediaResult.data : [];\n                } catch (e) {\n                    console.warn('Failed to parse media response:', e);\n                }\n            }\n            if (usersResponse.ok) {\n                try {\n                    const usersResult = await usersResponse.json();\n                    userData = usersResult.success ? usersResult.users : [];\n                } catch (e) {\n                    console.warn('Failed to parse users response:', e);\n                }\n            }\n            // حساب الإحصائيات الحقيقية\n            const totalMedia = mediaData.length;\n            const validMedia = mediaData.filter((item)=>item.status === 'VALID').length;\n            const rejectedMedia = mediaData.filter((item)=>item.status === 'REJECTED_CENSORSHIP' || item.status === 'REJECTED_TECHNICAL').length;\n            // التحقق من المواد المنتهية حسب التاريخ\n            const today = new Date();\n            const expiredByDateItems = mediaData.filter((item)=>item.endDate && new Date(item.endDate) < today);\n            // المواد المنتهية (إما بالحالة أو بالتاريخ)\n            const expiredStatusItems = mediaData.filter((item)=>item.status === 'EXPIRED');\n            const allExpiredItems = [\n                ...new Set([\n                    ...expiredByDateItems,\n                    ...expiredStatusItems\n                ])\n            ];\n            const expiredMedia = allExpiredItems.length;\n            // تحديث قائمة المواد المنتهية للتنبيه\n            setExpiredMediaItems(allExpiredItems);\n            // إظهار التنبيه مرة واحدة فقط في الجلسة\n            const alertKey = \"expired_alert_\".concat(new Date().toDateString());\n            const alertShownToday = localStorage.getItem(alertKey);\n            if (allExpiredItems.length > 0 && !alertShownToday && !expiredAlertShown) {\n                setShowExpiredAlert(true);\n                setExpiredAlertShown(true);\n                localStorage.setItem(alertKey, 'true');\n                console.log(\"⚠️ تم العثور على \".concat(allExpiredItems.length, \" مادة منتهية - عرض التنبيه\"));\n            } else {\n                setShowExpiredAlert(false);\n            }\n            const pendingMedia = mediaData.filter((item)=>item.status === 'PENDING').length;\n            // حساب المواد المضافة اليوم\n            const todayStr = new Date().toDateString();\n            const todayAdded = mediaData.filter((item)=>{\n                if (!item.createdAt) return false;\n                return new Date(item.createdAt).toDateString() === todayStr;\n            }).length;\n            setStats({\n                totalMedia,\n                validMedia,\n                rejectedMedia,\n                expiredMedia,\n                pendingMedia,\n                activeUsers: userData.length,\n                onlineUsers: userData.filter((u)=>u.isActive).length,\n                todayAdded\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // التحقق من نوع الخطأ\n            if (error instanceof Error && error.name === 'AbortError') {\n                console.log('Request was aborted due to timeout');\n            }\n            // بيانات افتراضية في حالة الخطأ\n            setStats({\n                totalMedia: 0,\n                validMedia: 0,\n                rejectedMedia: 0,\n                expiredMedia: 0,\n                pendingMedia: 0,\n                activeUsers: 4,\n                onlineUsers: 1,\n                todayAdded: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const navigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    color: 'white'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            marginBottom: '30px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: \"large\",\n                            style: {\n                                fontSize: '2rem'\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '3rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.5rem',\n                            marginBottom: '10px'\n                        },\n                        children: t('common.loadingData')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#a0aec0',\n                            fontSize: '1rem'\n                        },\n                        children: t('messages.pleaseWait')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: isRTL ? 'rtl' : 'ltr'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                showExpiredAlert && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: '20px',\n                        left: '20px',\n                        right: '20px',\n                        zIndex: 1000,\n                        background: '#ef4444',\n                        color: 'white',\n                        padding: '15px 20px',\n                        borderRadius: '8px',\n                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        maxHeight: '300px',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontWeight: 'bold',\n                                        fontSize: '1.1rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: [\n                                        \"⚠️ \",\n                                        t('messages.warning'),\n                                        \": \",\n                                        t('stats.expiredMedia'),\n                                        \" \",\n                                        expiredMediaItems.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            paddingRight: '20px',\n                                            margin: '5px 0'\n                                        },\n                                        children: [\n                                            expiredMediaItems.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        item.name,\n                                                        \" - \",\n                                                        item.endDate ? \"منتهية بتاريخ: \".concat(new Date(item.endDate).toLocaleDateString('ar-EG')) : 'منتهية'\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            expiredMediaItems.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"... والمزيد (\",\n                                                    expiredMediaItems.length - 5,\n                                                    \" مادة أخرى)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setShowExpiredAlert(false);\n                                setExpiredAlertShown(true);\n                                // حفظ في localStorage لمنع ظهور التنبيه مرة أخرى اليوم\n                                const alertKey = \"expired_alert_\".concat(new Date().toDateString());\n                                localStorage.setItem(alertKey, 'true');\n                                console.log('🔕 تم إغلاق تنبيه المواد المنتهية نهائياً لليوم');\n                            },\n                            style: {\n                                background: 'rgba(255, 255, 255, 0.2)',\n                                border: 'none',\n                                color: 'white',\n                                borderRadius: '4px',\n                                padding: '5px 10px',\n                                cursor: 'pointer',\n                                marginRight: '10px'\n                            },\n                            children: t('common.close')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: topNavigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const newLang = currentLang === 'ar' ? 'en' : 'ar';\n                                        i18n.changeLanguage(newLang);\n                                    },\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '5px'\n                                    },\n                                    title: currentLang === 'ar' ? 'Switch to English' : 'التبديل للعربية',\n                                    children: [\n                                        \"\\uD83C\\uDF10\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: currentLang === 'ar' ? 'EN' : 'عر'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    title: t('auth.logout'),\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        ...isRTL ? {\n                            marginRight: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-right 0.3s ease'\n                        } : {\n                            marginLeft: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-left 0.3s ease'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: t('dashboard.title')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: t('dashboard.subtitle')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#68d391',\n                                                        margin: '5px 0 0 0',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        t('dashboard.overview'),\n                                                        \": \",\n                                                        stats.totalMedia,\n                                                        \" \",\n                                                        t('stats.totalMedia'),\n                                                        \" - \",\n                                                        stats.activeUsers,\n                                                        \" \",\n                                                        t('stats.activeUsers')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: t('common.loading')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"المزامنة: \",\n                                                        currentTime.toLocaleTimeString('ar-EG')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83C\\uDFAC\",\n                                    title: t('navigation.mediaList'),\n                                    subtitle: t('media.list'),\n                                    path: \"/media-list\",\n                                    permission: \"MEDIA_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"➕\",\n                                    title: t('navigation.addMedia'),\n                                    subtitle: t('media.addNew'),\n                                    path: \"/add-media\",\n                                    permission: \"MEDIA_CREATE\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    title: t('navigation.weeklySchedule'),\n                                    subtitle: t('schedule.weekly'),\n                                    path: \"/weekly-schedule\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCA\",\n                                    title: t('navigation.dailySchedule'),\n                                    subtitle: t('schedule.daily'),\n                                    path: \"/daily-schedule\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCB\",\n                                    title: t('navigation.reports'),\n                                    subtitle: t('dashboard.recentActivity'),\n                                    path: \"/reports\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 13\n                                }, this),\n                                (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && (user === null || user === void 0 ? void 0 : user.username) === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCE4\",\n                                    title: t('navigation.unifiedSystem'),\n                                    subtitle: t('common.import') + '/' + t('common.export'),\n                                    path: \"/unified-system\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDC65\",\n                                    title: t('navigation.adminDashboard'),\n                                    subtitle: t('admin.users'),\n                                    path: \"/admin-dashboard\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC8\",\n                                    title: t('navigation.statistics'),\n                                    subtitle: t('dashboard.statistics'),\n                                    path: \"/statistics\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"TOxbwKN4xJEapjey2i9NarXO5zs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__.useAppTranslation\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction AuthGuard(param) {\n    let { children, requiredPermissions = [], requiredRole, fallbackComponent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    // المدير له صلاحيات كاملة\n    if (user.role === 'ADMIN') {\n      console.log('✅ المستخدم هو مدير النظام - تم منح جميع الصلاحيات');\n      return true;\n    }\n\n    // التحقق من الدور المطلوب\n    if (role && user.role !== role) {\n      console.log(`❌ المستخدم ليس لديه الدور المطلوب: ${role}`);\n      return false;\n    }\n\n    // التحقق من الصلاحيات المطلوبة\n    if (permissions.length > 0) {\n      const userPermissions = getUserPermissions(user.role);\n      console.log('🔍 التحقق من الصلاحيات:', {\n        required: permissions,\n        userHas: userPermissions\n      });\n      \n      const hasAllPermissions = permissions.every(permission => \n        userPermissions.includes(permission) || userPermissions.includes('ALL')\n      );\n      \n      if (!hasAllPermissions) {\n        console.log('❌ المستخدم ليس لديه جميع الصلاحيات المطلوبة');\n      } else {\n        console.log('✅ المستخدم لديه جميع الصلاحيات المطلوبة');\n      }\n      \n      return hasAllPermissions;\n    }\n\n    return true;\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user === null || user === void 0 ? void 0 : user.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user === null || user === void 0 ? void 0 : user.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AuthGuard, \"fqF8YvhaHbrPIfzSUHTCm0cPUfQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthGuard;\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    if (!user) return false;\n    if (user.role === 'ADMIN') return true;\n\n    const userPermissions = getUserPermissions(user.role);\n    return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN',\n        isMediaManager: (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER',\n        isScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER',\n        isViewer: (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER',\n        isFullViewer: (user === null || user === void 0 ? void 0 : user.role) === 'FULL_VIEWER',\n        isDataEntry: (user === null || user === void 0 ? void 0 : user.role) === 'DATA_ENTRY',\n        isMapScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'MAP_SCHEDULER',\n        isContentManager: (user === null || user === void 0 ? void 0 : user.role) === 'CONTENT_MANAGER'\n    };\n}\n_s1(useAuth, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AuthGuard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Logo(param) {\n    let { size = 'medium', className, style } = param;\n    const sizes = {\n        small: {\n            fontSize: '1rem',\n            gap: '3px',\n            xSize: '1.2rem'\n        },\n        medium: {\n            fontSize: '1.2rem',\n            gap: '5px',\n            xSize: '1.5rem'\n        },\n        large: {\n            fontSize: '2rem',\n            gap: '8px',\n            xSize: '2.5rem'\n        }\n    };\n    const currentSize = sizes[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        style: {\n            display: 'flex',\n            alignItems: 'center',\n            fontSize: currentSize.fontSize,\n            fontWeight: '900',\n            fontFamily: 'Arial, sans-serif',\n            gap: currentSize.gap,\n            direction: 'ltr',\n            ...style\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '800',\n                    letterSpacing: '1px'\n                },\n                children: \"Prime\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: '#6c757d',\n                    fontSize: '0.8em',\n                    fontWeight: '300'\n                },\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '900',\n                    fontSize: currentSize.xSize\n                },\n                children: \"X\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c = Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Logo.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/NavigationCard.tsx":
/*!*******************************************!*\
  !*** ./src/components/NavigationCard.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n// خلفية مطابقة للشاشة مع حدود ضوئية ملونة\nconst getIconColors = (icon)=>{\n    switch(icon){\n        case '🎬':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(139, 92, 246, 0.3)',\n                hoverShadow: 'rgba(139, 92, 246, 0.8)',\n                hoverBorder: '#8B5CF6',\n                glowColor: '#8B5CF6',\n                iconBackground: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%)',\n                iconShadow: 'rgba(139, 92, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(139, 92, 246, 0.3)'\n            };\n        case '➕':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(6, 182, 212, 0.3)',\n                hoverShadow: 'rgba(6, 182, 212, 0.8)',\n                hoverBorder: '#06B6D4',\n                glowColor: '#06B6D4',\n                iconBackground: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 50%, #0E7490 100%)',\n                iconShadow: 'rgba(6, 182, 212, 0.4)',\n                borderGlow: '0 0 20px rgba(6, 182, 212, 0.3)'\n            };\n        case '🔄':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(249, 115, 22, 0.3)',\n                hoverShadow: 'rgba(249, 115, 22, 0.8)',\n                hoverBorder: '#F97316',\n                glowColor: '#F97316',\n                iconBackground: 'linear-gradient(135deg, #F97316 0%, #EA580C 50%, #DC2626 100%)',\n                iconShadow: 'rgba(249, 115, 22, 0.4)',\n                borderGlow: '0 0 20px rgba(249, 115, 22, 0.3)'\n            };\n        case '📅':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(59, 130, 246, 0.3)',\n                hoverShadow: 'rgba(59, 130, 246, 0.8)',\n                hoverBorder: '#3B82F6',\n                glowColor: '#3B82F6',\n                iconBackground: 'linear-gradient(135deg, #3B82F6 0%, #2563EB 50%, #1D4ED8 100%)',\n                iconShadow: 'rgba(59, 130, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(59, 130, 246, 0.3)'\n            };\n        case '📊':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(16, 185, 129, 0.3)',\n                hoverShadow: 'rgba(16, 185, 129, 0.8)',\n                hoverBorder: '#10B981',\n                glowColor: '#10B981',\n                iconBackground: 'linear-gradient(135deg, #10B981 0%, #059669 50%, #047857 100%)',\n                iconShadow: 'rgba(16, 185, 129, 0.4)',\n                borderGlow: '0 0 20px rgba(16, 185, 129, 0.3)'\n            };\n        case '📋':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(239, 68, 68, 0.3)',\n                hoverShadow: 'rgba(239, 68, 68, 0.8)',\n                hoverBorder: '#EF4444',\n                glowColor: '#EF4444',\n                iconBackground: 'linear-gradient(135deg, #EF4444 0%, #DC2626 50%, #B91C1C 100%)',\n                iconShadow: 'rgba(239, 68, 68, 0.4)',\n                borderGlow: '0 0 20px rgba(239, 68, 68, 0.3)'\n            };\n        case '👥':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(245, 158, 11, 0.3)',\n                hoverShadow: 'rgba(245, 158, 11, 0.8)',\n                hoverBorder: '#F59E0B',\n                glowColor: '#F59E0B',\n                iconBackground: 'linear-gradient(135deg, #F59E0B 0%, #D97706 50%, #B45309 100%)',\n                iconShadow: 'rgba(245, 158, 11, 0.4)',\n                borderGlow: '0 0 20px rgba(245, 158, 11, 0.3)'\n            };\n        case '📈':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(236, 72, 153, 0.3)',\n                hoverShadow: 'rgba(236, 72, 153, 0.8)',\n                hoverBorder: '#EC4899',\n                glowColor: '#EC4899',\n                iconBackground: 'linear-gradient(135deg, #EC4899 0%, #DB2777 50%, #BE185D 100%)',\n                iconShadow: 'rgba(236, 72, 153, 0.4)',\n                borderGlow: '0 0 20px rgba(236, 72, 153, 0.3)'\n            };\n        default:\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(139, 92, 246, 0.3)',\n                hoverShadow: 'rgba(139, 92, 246, 0.8)',\n                hoverBorder: '#8B5CF6',\n                glowColor: '#8B5CF6',\n                iconBackground: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%)',\n                iconShadow: 'rgba(139, 92, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(139, 92, 246, 0.3)'\n            };\n    }\n};\nfunction NavigationCard(param) {\n    let { icon, title, subtitle, path, permission, adminOnly = false, height = 'auto' } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    // التحقق من الصلاحيات\n    if (adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') {\n        return null;\n    }\n    if (permission && !hasPermission(permission)) {\n        return null;\n    }\n    const handleClick = ()=>{\n        router.push(path);\n    };\n    const iconColors = getIconColors(icon);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: handleClick,\n        style: {\n            background: iconColors.background,\n            borderRadius: '20px',\n            padding: '30px',\n            border: \"3px solid \".concat(iconColors.border),\n            position: 'relative',\n            overflow: 'hidden',\n            cursor: 'pointer',\n            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n            transform: 'translateZ(0)',\n            height: height,\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            textAlign: 'center',\n            boxShadow: \"0 10px 30px \".concat(iconColors.shadow, \", \").concat(iconColors.borderGlow, \", inset 0 1px 0 rgba(255,255,255,0.1)\"),\n            direction: isRTL ? 'rtl' : 'ltr',\n            backdropFilter: 'blur(10px)',\n            WebkitBackdropFilter: 'blur(10px)'\n        },\n        onMouseEnter: (e)=>{\n            e.currentTarget.style.transform = 'translateY(-12px) scale(1.03)';\n            e.currentTarget.style.boxShadow = \"0 25px 50px \".concat(iconColors.hoverShadow, \", 0 0 40px \").concat(iconColors.hoverShadow, \", 0 0 80px \").concat(iconColors.hoverShadow, \", inset 0 1px 0 rgba(255,255,255,0.2)\");\n            e.currentTarget.style.border = \"3px solid \".concat(iconColors.hoverBorder);\n            e.currentTarget.style.background = \"rgba(17, 24, 39, 0.95)\";\n            // تأثير الإضاءة على الأيقونة\n            const iconElement = e.currentTarget.querySelector('.card-icon');\n            if (iconElement) {\n                iconElement.style.transform = 'scale(1.2) rotateY(5deg)';\n                iconElement.style.boxShadow = \"0 12px 35px \".concat(iconColors.iconShadow, \", 0 0 30px \").concat(iconColors.glowColor, \", 0 0 50px \").concat(iconColors.glowColor, \", inset 0 2px 8px rgba(255,255,255,0.3)\");\n                iconElement.style.filter = \"brightness(1.4) contrast(1.3) drop-shadow(0 0 25px \".concat(iconColors.glowColor, \")\");\n            }\n        },\n        onMouseLeave: (e)=>{\n            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n            e.currentTarget.style.boxShadow = \"0 10px 30px \".concat(iconColors.shadow, \", \").concat(iconColors.borderGlow, \", inset 0 1px 0 rgba(255,255,255,0.1)\");\n            e.currentTarget.style.border = \"3px solid \".concat(iconColors.border);\n            e.currentTarget.style.background = iconColors.background;\n            // إعادة الأيقونة لحالتها الطبيعية\n            const iconElement = e.currentTarget.querySelector('.card-icon');\n            if (iconElement) {\n                iconElement.style.transform = 'scale(1) rotateY(0deg)';\n                iconElement.style.boxShadow = \"0 8px 25px \".concat(iconColors.iconShadow, \", 0 4px 15px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255,255,255,0.2)\");\n                iconElement.style.filter = 'brightness(1.1) contrast(1.1) drop-shadow(0 2px 4px rgba(0,0,0,0.3))';\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-icon\",\n                style: {\n                    position: 'absolute',\n                    top: '25px',\n                    [isRTL ? 'right' : 'left']: '25px',\n                    width: '75px',\n                    height: '75px',\n                    background: iconColors.iconBackground,\n                    borderRadius: '20px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '2.4rem',\n                    border: '3px solid rgba(255, 255, 255, 0.4)',\n                    boxShadow: \"0 8px 25px \".concat(iconColors.iconShadow, \", 0 4px 15px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255,255,255,0.2)\"),\n                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n                    backdropFilter: 'blur(10px)',\n                    WebkitBackdropFilter: 'blur(10px)',\n                    transformStyle: 'preserve-3d'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        filter: 'brightness(1.3) contrast(1.2) drop-shadow(0 2px 4px rgba(0,0,0,0.3))',\n                        textShadow: '0 2px 8px rgba(0,0,0,0.4)',\n                        color: 'white',\n                        transition: 'all 0.3s ease'\n                    },\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    height: '100%',\n                    paddingTop: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.7rem',\n                            fontWeight: '600',\n                            color: 'rgba(255, 255, 255, 0.95)',\n                            marginBottom: '12px',\n                            textShadow: '0 2px 12px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.1)',\n                            textAlign: 'center',\n                            letterSpacing: '0.5px',\n                            transition: 'all 0.3s ease'\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'rgba(255, 255, 255, 0.75)',\n                            fontSize: '1.1rem',\n                            lineHeight: '1.5',\n                            textShadow: '0 1px 6px rgba(0,0,0,0.6)',\n                            textAlign: 'center',\n                            opacity: 0.9,\n                            transition: 'all 0.3s ease'\n                        },\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: '0',\n                    left: '0',\n                    right: '0',\n                    height: '2px',\n                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)',\n                    transform: 'translateX(-100%)',\n                    transition: 'transform 0.6s ease'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationCard, \"Bc/aYeUZbhNEmAfuZtp6ihsSHwc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = NavigationCard;\nvar _c;\n$RefreshReg$(_c, \"NavigationCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/NavigationCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Sidebar(param) {\n    let { isOpen, onToggle } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const menuItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.importSchedule'),\n            icon: '📤',\n            path: '/daily-schedule/import',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            path: '/statistics',\n            permission: null\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    ...isRTL ? {\n                        right: isOpen ? 0 : '-280px',\n                        borderLeft: '1px solid #2d3748'\n                    } : {\n                        left: isOpen ? 0 : '-280px',\n                        borderRight: '1px solid #2d3748'\n                    },\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    transition: \"\".concat(isRTL ? 'right' : 'left', \" 0.3s ease\"),\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: \"small\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                margin: 0,\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: t('dashboard.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#2d3748' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    borderTop: 'none',\n                                    borderBottom: 'none',\n                                    ...isRTL ? {\n                                        borderLeft: 'none',\n                                        borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    } : {\n                                        borderRight: 'none',\n                                        borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    },\n                                    padding: isRTL ? '12px 20px 12px 8px' : '12px 8px 12px 20px',\n                                    textAlign: isRTL ? 'right' : 'left',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    transition: 'all 0.2s ease',\n                                    direction: isRTL ? 'rtl' : 'ltr'\n                                },\n                                children: item.name\n                            }, index, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('user');\n                                localStorage.removeItem('token');\n                                router.push('/login');\n                            },\n                            style: {\n                                width: '100%',\n                                background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '10px',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '8px',\n                                fontSize: '0.9rem',\n                                fontWeight: 'bold',\n                                marginBottom: '15px'\n                            },\n                            children: [\n                                \"\\uD83D\\uDEAA \",\n                                t('navigation.logout')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"VJIKuK+HsyvOcEJPeGwsV88vEIU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAppTranslation.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAppTranslation.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppTranslation: () => (/* binding */ useAppTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _utils_translations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/translations */ \"(app-pages-browser)/./src/utils/translations.ts\");\n\n\n// Hook مخصص للترجمة يضمن الاتساق\nconst useAppTranslation = ()=>{\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const currentLang = i18n.language || 'ar';\n    // دالة الترجمة الأساسية\n    const t = (key)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getTranslation)(key, currentLang);\n    };\n    // دالة ترجمة أنواع المواد\n    const tMediaType = (type)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getMediaTypeLabel)(type, currentLang);\n    };\n    // دالة ترجمة الأدوار\n    const tRole = (role)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getRoleLabel)(role, currentLang);\n    };\n    // دالة ترجمة أوصاف الأدوار\n    const tRoleDesc = (role)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getRoleDescription)(role, currentLang);\n    };\n    return {\n        t,\n        tMediaType,\n        tRole,\n        tRoleDesc,\n        currentLang,\n        isRTL: currentLang === 'ar'\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAppTranslation.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/dashboard.css":
/*!**********************************!*\
  !*** ./src/styles/dashboard.css ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8f0a083acb13\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZGFzaGJvYXJkLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxzdHlsZXNcXGRhc2hib2FyZC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZjBhMDgzYWNiMTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/dashboard.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/translations.ts":
/*!***********************************!*\
  !*** ./src/utils/translations.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMediaTypeLabel: () => (/* binding */ getMediaTypeLabel),\n/* harmony export */   getRoleDescription: () => (/* binding */ getRoleDescription),\n/* harmony export */   getRoleLabel: () => (/* binding */ getRoleLabel),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\n// مركز الترجمة الموحد - يضمن الترجمة الصحيحة في كلا اللغتين\nconst translations = {\n    ar: {\n        // Navigation\n        navigation: {\n            dashboard: \"لوحة التحكم\",\n            mediaList: \"المواد الإعلامية\",\n            addMedia: \"إضافة مادة\",\n            weeklySchedule: \"الخريطة البرامجية\",\n            dailySchedule: \"الجدول الإذاعي اليومي\",\n            importSchedule: \"استيراد جدول\",\n            statistics: \"الإحصائيات\",\n            adminDashboard: \"إدارة المستخدمين\",\n            reports: \"تقارير البث\",\n            logout: \"تسجيل الخروج\"\n        },\n        // Common terms\n        common: {\n            name: \"الاسم\",\n            type: \"النوع\",\n            status: \"الحالة\",\n            actions: \"الإجراءات\",\n            time: \"الوقت\",\n            duration: \"المدة\",\n            content: \"المحتوى\",\n            code: \"الكود\",\n            segments: \"السيجمنتات\"\n        },\n        // Media types - أسماء ثابتة حسب المطلوب\n        mediaTypes: {\n            ALL: \"جميع الأنواع\",\n            PROGRAM: \"برنامج\",\n            SERIES: \"مسلسل\",\n            FILM: \"فيلم\",\n            SONG: \"أغنية\",\n            PROMO: \"إعلان ترويجي\",\n            STING: \"فاصل\",\n            FILLER: \"مادة مالئة\",\n            NEXT: \"التالي\",\n            NOW: \"الآن\",\n            MINI: \"Mini\",\n            CROSS: \"Cross\",\n            \"سنعود\": \"سنعود\",\n            \"عدنا\": \"عدنا\" // يبقى بالعربية\n        },\n        // User roles\n        roles: {\n            ADMIN: \"مدير النظام\",\n            CONTENT_MANAGER: \"مدير المحتوى\",\n            MEDIA_MANAGER: \"مدير قاعدة البيانات\",\n            SCHEDULER: \"مجدول البرامج\",\n            FULL_VIEWER: \"مستخدم عرض كامل\",\n            DATA_ENTRY: \"إدخال البيانات\",\n            MAP_SCHEDULER: \"مدير الخريطة والجداول\",\n            VIEWER: \"مستخدم عرض\"\n        },\n        // Role descriptions\n        roleDescriptions: {\n            ADMIN: \"صلاحيات كاملة لإدارة النظام والمستخدمين\",\n            CONTENT_MANAGER: \"إدارة المحتوى والمواد الإعلامية\",\n            MEDIA_MANAGER: \"إدارة قاعدة بيانات المواد\",\n            SCHEDULER: \"إنشاء وتعديل الجداول البرامجية\",\n            FULL_VIEWER: \"عرض جميع البيانات والتقارير\",\n            DATA_ENTRY: \"إدخال وتعديل البيانات الأساسية\",\n            MAP_SCHEDULER: \"إدارة الخريطة البرامجية والجداول\",\n            VIEWER: \"عرض البيانات الأساسية فقط\"\n        }\n    },\n    en: {\n        // Navigation\n        navigation: {\n            dashboard: \"Dashboard\",\n            mediaList: \"Media List\",\n            addMedia: \"Add Media\",\n            weeklySchedule: \"Weekly Schedule\",\n            dailySchedule: \"Daily Schedule\",\n            importSchedule: \"Import Schedule\",\n            statistics: \"Statistics\",\n            adminDashboard: \"User Management\",\n            reports: \"Broadcast Reports\",\n            logout: \"Logout\"\n        },\n        // Common terms\n        common: {\n            name: \"Name\",\n            type: \"Type\",\n            status: \"Status\",\n            actions: \"Actions\",\n            time: \"Time\",\n            duration: \"Duration\",\n            content: \"Content\",\n            code: \"Code\",\n            segments: \"Segments\"\n        },\n        // Media types - أسماء ثابتة حسب المطلوب\n        mediaTypes: {\n            ALL: \"All Types\",\n            PROGRAM: \"Program\",\n            SERIES: \"Series\",\n            FILM: \"Film\",\n            SONG: \"Song\",\n            PROMO: \"Promo\",\n            STING: \"Sting\",\n            FILLER: \"Filler\",\n            NEXT: \"Next\",\n            NOW: \"Now\",\n            MINI: \"Mini\",\n            CROSS: \"Cross\",\n            \"سنعود\": \"We'll Be Back\",\n            \"عدنا\": \"We're Back\" // ترجمة للإنجليزية\n        },\n        // User roles\n        roles: {\n            ADMIN: \"System Administrator\",\n            CONTENT_MANAGER: \"Content Manager\",\n            MEDIA_MANAGER: \"Database Manager\",\n            SCHEDULER: \"Program Scheduler\",\n            FULL_VIEWER: \"Full View User\",\n            DATA_ENTRY: \"Data Entry\",\n            MAP_SCHEDULER: \"Map & Schedule Manager\",\n            VIEWER: \"Viewer\"\n        },\n        // Role descriptions\n        roleDescriptions: {\n            ADMIN: \"Full system administration and user management permissions\",\n            CONTENT_MANAGER: \"Manage content and media materials\",\n            MEDIA_MANAGER: \"Manage media database\",\n            SCHEDULER: \"Create and edit program schedules\",\n            FULL_VIEWER: \"View all data and reports\",\n            DATA_ENTRY: \"Enter and edit basic data\",\n            MAP_SCHEDULER: \"Manage program map and schedules\",\n            VIEWER: \"View basic data only\"\n        }\n    }\n};\n// دالة الترجمة المركزية\nconst getTranslation = function(key) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'ar';\n    const keys = key.split('.');\n    let value = translations[language];\n    for (const k of keys){\n        if (value && typeof value === 'object' && k in value) {\n            value = value[k];\n        } else {\n            // إذا لم توجد الترجمة، ارجع المفتاح نفسه\n            console.warn(\"Translation missing for key: \".concat(key, \" in language: \").concat(language));\n            return key;\n        }\n    }\n    return typeof value === 'string' ? value : key;\n};\n// دالة مساعدة للحصول على ترجمة نوع المادة\nconst getMediaTypeLabel = function(type) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'ar';\n    return getTranslation(\"mediaTypes.\".concat(type), language);\n};\n// دالة مساعدة للحصول على ترجمة الدور\nconst getRoleLabel = function(role) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'ar';\n    return getTranslation(\"roles.\".concat(role), language);\n};\n// دالة مساعدة للحصول على وصف الدور\nconst getRoleDescription = function(role) {\n    let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'ar';\n    return getTranslation(\"roleDescriptions.\".concat(role), language);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/translations.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);