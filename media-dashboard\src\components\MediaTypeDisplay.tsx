'use client';

import React from 'react';
import { useAppTranslation } from '@/hooks/useAppTranslation';

interface MediaTypeDisplayProps {
  type: string;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * مكون مركزي لعرض أنواع المواد مع ضمان الترجمة الصحيحة
 * يضمن أن أسماء المواد تظهر بالشكل الصحيح حسب المتطلبات:
 * - Mini, Cross: تبقى بالإنجليزية دائماً
 * - سنعود, عدنا: تبقى بالعربية دائماً
 * - باقي الأنواع: تترجم حسب اللغة المختارة
 */
const MediaTypeDisplay: React.FC<MediaTypeDisplayProps> = ({ 
  type, 
  className = '', 
  style = {} 
}) => {
  const { tMediaType } = useAppTranslation();

  return (
    <span className={className} style={style}>
      {tMediaType(type)}
    </span>
  );
};

export default MediaTypeDisplay;
