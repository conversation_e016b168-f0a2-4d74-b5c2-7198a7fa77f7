'use client';

import React from 'react';
import { useAppTranslation } from '@/hooks/useAppTranslation';

interface RoleDisplayProps {
  role: string;
  showDescription?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * مكون مركزي لعرض أدوار المستخدمين مع ضمان الترجمة الصحيحة
 * يضمن عرض الأدوار والأوصاف بالشكل الصحيح في كلا اللغتين
 */
const RoleDisplay: React.FC<RoleDisplayProps> = ({ 
  role, 
  showDescription = false,
  className = '', 
  style = {} 
}) => {
  const { tRole, tRoleDesc } = useAppTranslation();

  return (
    <div className={className} style={style}>
      <span className="role-name">{tRole(role)}</span>
      {showDescription && (
        <div className="role-description" style={{ 
          fontSize: '0.85em', 
          color: '#666', 
          marginTop: '4px' 
        }}>
          {tRoleDesc(role)}
        </div>
      )}
    </div>
  );
};

export default RoleDisplay;
