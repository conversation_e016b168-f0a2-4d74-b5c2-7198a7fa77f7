"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Sidebar(param) {\n    let { isOpen, onToggle } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const menuItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.importSchedule'),\n            icon: '📤',\n            path: '/daily-schedule/import',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            path: '/statistics',\n            permission: null\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-container\",\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    ...isRTL ? {\n                        right: isOpen ? 0 : '-280px',\n                        borderLeft: '1px solid #2d3748'\n                    } : {\n                        left: isOpen ? 0 : '-280px',\n                        borderRight: '1px solid #2d3748'\n                    },\n                    width: 'min(280px, 90vw)',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    transition: \"\".concat(isRTL ? 'right' : 'left', \" 0.3s ease\"),\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif',\n                    boxShadow: isOpen ? '0 0 20px rgba(0, 0, 0, 0.5)' : 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar-header\",\n                        style: {\n                            padding: 'clamp(15px, 3vw, 20px)',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            minHeight: '70px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    flex: 1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: \"small\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                margin: 0,\n                                                fontSize: 'clamp(0.7rem, 2vw, 0.8rem)',\n                                                lineHeight: 1.3\n                                            },\n                                            children: t('dashboard.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: 'clamp(1rem, 3vw, 1.2rem)',\n                                        cursor: 'pointer',\n                                        padding: '8px',\n                                        borderRadius: '4px',\n                                        transition: 'all 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.target.style.background = 'rgba(102, 126, 234, 0.1)';\n                                        e.target.style.color = '#667eea';\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar-menu\",\n                        style: {\n                            flex: 1,\n                            padding: 'clamp(15px, 3vw, 20px) 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"sidebar-menu-item\",\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#2d3748' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    borderTop: 'none',\n                                    borderBottom: 'none',\n                                    ...isRTL ? {\n                                        borderLeft: 'none',\n                                        borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    } : {\n                                        borderRight: 'none',\n                                        borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    },\n                                    padding: isRTL ? 'clamp(10px, 2.5vw, 12px) clamp(15px, 4vw, 20px) clamp(10px, 2.5vw, 12px) clamp(6px, 2vw, 8px)' : 'clamp(10px, 2.5vw, 12px) clamp(6px, 2vw, 8px) clamp(10px, 2.5vw, 12px) clamp(15px, 4vw, 20px)',\n                                    textAlign: isRTL ? 'right' : 'left',\n                                    cursor: 'pointer',\n                                    fontSize: 'clamp(0.8rem, 2.5vw, 0.9rem)',\n                                    fontWeight: 'bold',\n                                    transition: 'all 0.2s ease',\n                                    direction: isRTL ? 'rtl' : 'ltr',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: 'clamp(8px, 2vw, 12px)',\n                                    minHeight: '44px'\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'rgba(102, 126, 234, 0.1)';\n                                        e.target.style.color = '#667eea';\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: 'clamp(1rem, 3vw, 1.2rem)'\n                                        },\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar-footer\",\n                        style: {\n                            padding: 'clamp(15px, 3vw, 20px)',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('user');\n                                localStorage.removeItem('token');\n                                router.push('/login');\n                            },\n                            style: {\n                                width: '100%',\n                                background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: 'clamp(10px, 2.5vw, 12px)',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: 'clamp(6px, 2vw, 8px)',\n                                fontSize: 'clamp(0.8rem, 2.5vw, 0.9rem)',\n                                fontWeight: 'bold',\n                                marginBottom: 'clamp(10px, 2.5vw, 15px)',\n                                transition: 'all 0.2s ease',\n                                minHeight: '44px'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.transform = 'translateY(-2px)';\n                                e.target.style.boxShadow = '0 4px 12px rgba(245, 101, 101, 0.4)';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.transform = 'translateY(0)';\n                                e.target.style.boxShadow = 'none';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: 'clamp(1rem, 3vw, 1.2rem)'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('navigation.logout')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"VJIKuK+HsyvOcEJPeGwsV88vEIU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ })

});