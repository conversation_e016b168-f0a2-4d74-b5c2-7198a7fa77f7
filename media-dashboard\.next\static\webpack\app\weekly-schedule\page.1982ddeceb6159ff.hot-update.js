"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/weekly-schedule/page",{

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WeeklySchedulePage() {\n    _s();\n    const { isViewer } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [readOnlyMode, setReadOnlyMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // مرجع للجدول لتثبيت موضع التمرير\n    const scheduleTableRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const visibleRowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const visibleRowIndexRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(-1);\n    // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (isViewer) {\n                setReadOnlyMode(true);\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        isViewer\n    ]);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع\n    const days = [\n        t('common.sunday'),\n        t('common.monday'),\n        t('common.tuesday'),\n        t('common.wednesday'),\n        t('common.thursday'),\n        t('common.friday'),\n        t('common.saturday')\n    ];\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        // التأكد من أن selectedWeek يمثل يوم الأحد\n        const inputDate = new Date(selectedWeek + 'T00:00:00'); // استخدام منتصف الليل\n        console.log('📅 التاريخ المدخل:', selectedWeek);\n        console.log('📅 يوم الأسبوع للتاريخ المدخل:', inputDate.getDay(), '(0=أحد)');\n        // التأكد من أن نبدأ من يوم الأحد\n        const sundayDate = new Date(inputDate);\n        const dayOfWeek = inputDate.getDay();\n        if (dayOfWeek !== 0) {\n            // إذا لم يكن الأحد، نحسب الأحد السابق\n            sundayDate.setDate(inputDate.getDate() - dayOfWeek);\n        }\n        console.log('📅 يوم الأحد المحسوب:', sundayDate.toISOString().split('T')[0]);\n        const dates = [];\n        for(let i = 0; i < 7; i++){\n            // إنشاء تاريخ جديد لكل يوم بدءاً من الأحد\n            const currentDate = new Date(sundayDate);\n            currentDate.setDate(sundayDate.getDate() + i);\n            // استخدام التنسيق العربي للتاريخ (يوم/شهر/سنة)\n            const day = currentDate.getDate();\n            const month = currentDate.getMonth() + 1; // الشهور تبدأ من 0\n            const year = currentDate.getFullYear();\n            // تنسيق التاريخ بالشكل dd/mm/yyyy\n            const dateStr = \"\".concat(day.toString().padStart(2, '0'), \"/\").concat(month.toString().padStart(2, '0'), \"/\").concat(year);\n            dates.push(dateStr);\n            // التحقق من صحة اليوم\n            const actualDayOfWeek = currentDate.getDay();\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(currentDate.toISOString().split('T')[0], \" → \").concat(dateStr, \" [يوم الأسبوع: \").concat(actualDayOfWeek, \"]\"));\n            // تحذير إذا كان اليوم لا يتطابق مع المتوقع\n            if (actualDayOfWeek !== i) {\n                console.warn(\"⚠️ عدم تطابق: متوقع يوم \".concat(i, \" لكن حصلنا على \").concat(actualDayOfWeek));\n            }\n        }\n        return dates;\n    };\n    // استخدام useEffect لتحديث التواريخ عند تغيير الأسبوع المحدد\n    const [weekDates, setWeekDates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--'\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const dates = getWeekDates();\n            console.log('🔄 تحديث التواريخ:', dates);\n            setWeekDates(dates);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || t('schedule.unknown');\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.season'), \" \").concat(item.seasonNumber, \" \").concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.season'), \" \").concat(item.seasonNumber, \" \").concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - \".concat(t('schedule.part'), \" \").concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            // استخدام التاريخ المحلي مع تجنب مشاكل المنطقة الزمنية\n            const today = new Date();\n            // تحويل إلى تاريخ محلي بدون وقت\n            const year = today.getFullYear();\n            const month = today.getMonth();\n            const day = today.getDate();\n            const localDate = new Date(year, month, day);\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم الفعلي:', \"\".concat(year, \"-\").concat((month + 1).toString().padStart(2, '0'), \"-\").concat(day.toString().padStart(2, '0')));\n            console.log('  📊 يوم الأسبوع:', localDate.getDay(), '(0=أحد)');\n            // حساب يوم الأحد لهذا الأسبوع\n            const sunday = new Date(localDate);\n            sunday.setDate(localDate.getDate() - localDate.getDay());\n            // تحويل إلى string بطريقة محلية\n            const weekStart = \"\".concat(sunday.getFullYear(), \"-\").concat((sunday.getMonth() + 1).toString().padStart(2, '0'), \"-\").concat(sunday.getDate().toString().padStart(2, '0'));\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            console.log('  📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // حفظ الصف المرئي قبل تحديث البيانات\n    const saveVisibleRowIndex = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\": ()=>{\n            if (!scheduleTableRef.current) {\n                console.log('⚠️ لم يتم العثور على مرجع الجدول');\n                return;\n            }\n            // الحصول على جميع صفوف الساعات في الجدول\n            const hourRows = scheduleTableRef.current.querySelectorAll('.hour-row');\n            if (!hourRows.length) {\n                console.log('⚠️ لم يتم العثور على صفوف الساعات');\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D تم العثور على \".concat(hourRows.length, \" صف ساعة\"));\n            // تحديد الصف المرئي حاليًا في منتصف الشاشة\n            const viewportHeight = window.innerHeight;\n            const viewportMiddle = window.scrollY + viewportHeight / 2;\n            console.log(\"\\uD83D\\uDCCF منتصف الشاشة: \".concat(viewportMiddle, \"px (ارتفاع الشاشة: \").concat(viewportHeight, \"px, موضع التمرير: \").concat(window.scrollY, \"px)\"));\n            let closestRow = null;\n            let closestDistance = Infinity;\n            let closestIndex = -1;\n            // البحث عن أقرب صف للمنتصف\n            hourRows.forEach({\n                \"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\": (row, index)=>{\n                    const rect = row.getBoundingClientRect();\n                    const rowTop = window.scrollY + rect.top;\n                    const rowBottom = rowTop + rect.height;\n                    const rowMiddle = rowTop + rect.height / 2;\n                    const distance = Math.abs(viewportMiddle - rowMiddle);\n                    console.log(\"  صف \".concat(index, \" (\").concat(hours[index], \"): العلوي=\").concat(rect.top.toFixed(0), \", الارتفاع=\").concat(rect.height.toFixed(0), \", المسافة=\").concat(distance.toFixed(0)));\n                    if (distance < closestDistance) {\n                        closestDistance = distance;\n                        closestRow = row;\n                        closestIndex = index;\n                    }\n                }\n            }[\"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\"]);\n            if (closestRow) {\n                visibleRowRef.current = closestRow;\n                visibleRowIndexRef.current = closestIndex;\n                console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي: الساعة \".concat(hours[closestIndex], \", الفهرس \").concat(closestIndex, \", المسافة=\").concat(closestDistance.toFixed(0), \"px\"));\n            } else {\n                console.log('⚠️ لم يتم العثور على صف مرئي');\n            }\n        }\n    }[\"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\"], [\n        hours\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات - نسخة مبسطة وأكثر فعالية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current !== undefined) {\n                console.log(\"\\uD83D\\uDD04 استعادة موضع التمرير: \".concat(scrollPositionRef.current, \"px\"));\n                // تأخير للتأكد من اكتمال الرندر\n                const timer = setTimeout({\n                    \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                        const targetPosition = scrollPositionRef.current;\n                        // استعادة الموضع مباشرة\n                        window.scrollTo({\n                            top: targetPosition,\n                            behavior: 'instant'\n                        });\n                        console.log(\"\\uD83D\\uDCCD تم استعادة الموضع إلى: \".concat(targetPosition, \"px\"));\n                        // التحقق من نجاح العملية والتصحيح إذا لزم الأمر\n                        setTimeout({\n                            \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                                const currentPosition = window.scrollY;\n                                const difference = Math.abs(currentPosition - targetPosition);\n                                if (difference > 5) {\n                                    console.log(\"\\uD83D\\uDCCD تصحيح الموضع: \".concat(currentPosition, \"px → \").concat(targetPosition, \"px (فرق: \").concat(difference, \"px)\"));\n                                    window.scrollTo({\n                                        top: targetPosition,\n                                        behavior: 'instant'\n                                    });\n                                } else {\n                                    console.log(\"✅ تم تثبيت موضع التمرير بنجاح\");\n                                }\n                            }\n                        }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 100);\n                        shouldRestoreScroll.current = false;\n                    }\n                }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 200);\n                return ({\n                    \"WeeklySchedulePage.useLayoutEffect\": ()=>clearTimeout(timer)\n                })[\"WeeklySchedulePage.useLayoutEffect\"];\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            // التأكد من حفظ موضع التمرير إذا لم يكن محفوظاً\n            if (!shouldRestoreScroll.current) {\n                const currentScrollPosition = window.scrollY;\n                scrollPositionRef.current = currentScrollPosition;\n                shouldRestoreScroll.current = true;\n                console.log(\"\\uD83D\\uDCCD حفظ تلقائي لموضع التمرير: \".concat(currentScrollPosition, \"px\"));\n            }\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"خطأ في تحديث البيانات: \".concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"خطأ في الاتصال: \".concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert(t('schedule.enterMediaName'));\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حفظ المادة المؤقتة');\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert('خطأ في حفظ المادة المؤقتة');\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm(t('schedule.confirmDeleteTemp'))) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || 'فشل في حذف المادة المؤقتة');\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert('خطأ في حذف المادة المؤقتة');\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي بدقة\n            const currentScrollPosition = window.scrollY;\n            scrollPositionRef.current = currentScrollPosition;\n            // حفظ الصف المرئي قبل التحديث\n            saveVisibleRowIndex();\n            shouldRestoreScroll.current = true;\n            console.log(\"\\uD83D\\uDCCD تم حفظ موضع التمرير: \".concat(currentScrollPosition, \"px والصف المرئي عند إضافة مادة\"));\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ ' + t('schedule.timeConflict'));\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تحديث محلي بدلاً من إعادة تحميل كامل\n                const newScheduleItem = {\n                    id: result.data.id,\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime: startTime,\n                    endTime: endTime,\n                    weekStart: selectedWeek,\n                    mediaItem: mediaItem,\n                    isTemporary: mediaItem.isTemporary || false,\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                };\n                // إضافة المادة للجدول محلياً\n                setScheduleItems((prev)=>[\n                        ...prev,\n                        newScheduleItem\n                    ]);\n                console.log('✅ تم إضافة المادة للجدول محلياً بدون إعادة تحميل');\n            } else {\n                alert(result.error);\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || t('schedule.unknown');\n        const itemType = item.isRerun ? t('schedule.rerunMaterial') : item.isTemporary ? t('schedule.tempMaterial') : t('schedule.originalMaterial');\n        const warningMessage = item.isRerun ? t('schedule.deleteWarningRerun') : item.isTemporary ? t('schedule.deleteWarningTemp') : t('schedule.deleteWarningOriginal');\n        const confirmed = window.confirm(\"\".concat(t('schedule.confirmDelete', {\n            type: itemType,\n            name: itemName\n        }), \"\\n\\n\") + \"\".concat(t('schedule.time'), \": \").concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + warningMessage);\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف من الخادم أيضًا\n            if (item.isTemporary) {\n                console.log(\"\\uD83D\\uDDD1️ بدء حذف المادة المؤقتة: \".concat(itemName, \" (\").concat(item.id, \")\"));\n                try {\n                    var _item_mediaItem1, _item_mediaItem2;\n                    // حذف المادة المؤقتة من الخادم\n                    console.log(\"\\uD83D\\uDD0D إرسال طلب حذف المادة المؤقتة: \".concat(item.id, \" (\").concat(itemName, \")\"));\n                    console.log(\"\\uD83D\\uDCCA بيانات المادة المؤقتة:\", {\n                        id: item.id,\n                        mediaItemId: item.mediaItemId,\n                        dayOfWeek: item.dayOfWeek,\n                        startTime: item.startTime,\n                        endTime: item.endTime,\n                        isTemporary: item.isTemporary,\n                        isRerun: item.isRerun,\n                        mediaItemName: (_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.name\n                    });\n                    const response = await fetch('/api/weekly-schedule', {\n                        method: 'PATCH',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            action: 'deleteTempItem',\n                            tempItemId: item.id,\n                            mediaItemId: item.mediaItemId,\n                            dayOfWeek: item.dayOfWeek,\n                            startTime: item.startTime,\n                            endTime: item.endTime,\n                            weekStart: selectedWeek,\n                            mediaItemName: (_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.name\n                        })\n                    });\n                    const result = await response.json();\n                    if (result.success) {\n                        console.log(\"✅ تم حذف المادة المؤقتة من الخادم: \".concat(itemName));\n                        // حذف محلي بعد نجاح الحذف من الخادم\n                        if (item.isRerun) {\n                            // حذف إعادة مؤقتة فقط\n                            setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                            console.log(\"✅ تم حذف إعادة مؤقتة محليًا: \".concat(itemName));\n                        } else {\n                            // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                            setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isRerun && scheduleItem.originalId === item.id)));\n                            console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها محليًا: \".concat(itemName));\n                        }\n                        // إعادة تحميل البيانات للتأكد من التزامن\n                        // حفظ الصف المرئي قبل إعادة التحميل\n                        saveVisibleRowIndex();\n                        shouldRestoreScroll.current = true;\n                        console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي قبل إعادة تحميل البيانات\");\n                        await fetchScheduleData();\n                    } else {\n                        console.error(\"❌ فشل حذف المادة المؤقتة من الخادم: \".concat(result.error));\n                        alert(\"فشل حذف المادة المؤقتة: \".concat(result.error));\n                    }\n                } catch (error) {\n                    console.error(\"❌ خطأ أثناء حذف المادة المؤقتة: \".concat(error));\n                    alert('حدث خطأ أثناء حذف المادة المؤقتة');\n                }\n                return;\n            }\n            // حفظ الصف المرئي قبل التحديث\n            saveVisibleRowIndex();\n            shouldRestoreScroll.current = true;\n            console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي عند حذف مادة\");\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                // حفظ الصف المرئي قبل إعادة التحميل\n                saveVisibleRowIndex();\n                shouldRestoreScroll.current = true;\n                console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي قبل إعادة تحميل البيانات بعد الحذف\");\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        if (!selectedWeek) return;\n        const currentDate = new Date(selectedWeek + 'T00:00:00');\n        console.log('📅 تغيير الأسبوع - البداية:', {\n            direction: direction > 0 ? 'التالي' : 'السابق',\n            currentWeek: selectedWeek,\n            currentDayOfWeek: currentDate.getDay()\n        });\n        // إضافة أو طرح 7 أيام\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        const newWeekStart = currentDate.toISOString().split('T')[0];\n        console.log('📅 تغيير الأسبوع - النتيجة:', {\n            newWeek: newWeekStart,\n            newDayOfWeek: currentDate.getDay()\n        });\n        setSelectedWeek(newWeekStart);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"⏳ \",\n                    t('schedule.loadingSchedule')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 955,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 954,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"\\uD83D\\uDCC5 \",\n                    t('schedule.selectingDate')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 964,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 963,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: t('schedule.weeklySchedule'),\n            subtitle: t('schedule.weeklySubtitle'),\n            icon: \"\\uD83D\\uDCC5\",\n            fullWidth: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    height: 'calc(100vh - 120px)',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: isRTL ? 'rtl' : 'ltr',\n                    gap: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '320px',\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            border: '1px solid #6b7280',\n                            padding: '20px',\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: '0 0 15px 0',\n                                    color: '#f3f4f6'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDA \",\n                                    t('schedule.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    border: '2px solid #f59e0b',\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            margin: '0 0 10px 0',\n                                            color: '#fbbf24',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: [\n                                            \"⚡ \",\n                                            t('schedule.addTempMedia')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.mediaName'),\n                                        value: tempMediaName,\n                                        onChange: (e)=>setTempMediaName(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: tempMediaType,\n                                        onChange: (e)=>setTempMediaType(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROGRAM\",\n                                                children: \"\\uD83D\\uDCFB Program\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SERIES\",\n                                                children: \"\\uD83D\\uDCFA Series\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"FILM\",\n                                                children: \"\\uD83C\\uDFA5 Film\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"FILLER\",\n                                                children: \"⏸️ Filler\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"STING\",\n                                                children: \"⚡ Sting\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROMO\",\n                                                children: \"\\uD83D\\uDCE2 Promo\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"NEXT\",\n                                                children: \"▶️ Next\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"NOW\",\n                                                children: \"\\uD83D\\uDD34 Now\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"سنعود\",\n                                                children: \"⏰ سنعود\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1041,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"عدنا\",\n                                                children: \"✅ عدنا\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MINI\",\n                                                children: \"\\uD83D\\uDD38 Mini\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"CROSS\",\n                                                children: \"✖️ Cross\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1044,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"LIVE\",\n                                                children: \"\\uD83D\\uDD34 برنامج هواء مباشر\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PENDING\",\n                                                children: \"\\uD83D\\uDFE1 مادة قيد التسليم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.duration'),\n                                        value: tempMediaDuration,\n                                        onChange: (e)=>setTempMediaDuration(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.notes'),\n                                        value: tempMediaNotes,\n                                        onChange: (e)=>setTempMediaNotes(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '10px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: addTempMedia,\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            background: '#ff9800',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '13px',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            marginBottom: '8px'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('schedule.add')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1083,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            console.log('🔄 تحديث الإعادات...');\n                                            scrollPositionRef.current = window.scrollY;\n                                            shouldRestoreScroll.current = true;\n                                            await fetchScheduleData();\n                                        },\n                                        style: {\n                                            width: '100%',\n                                            padding: '6px',\n                                            background: '#4caf50',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: [\n                                            \"♻️ \",\n                                            t('schedule.updateReruns')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedType,\n                                onChange: (e)=>setSelectedType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '10px',\n                                    fontSize: '14px',\n                                    backgroundColor: 'white',\n                                    color: '#333'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('schedule.allTypes')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: \"\\uD83D\\uDCFA Series\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILM\",\n                                        children: \"\\uD83C\\uDFA5 Film\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: \"\\uD83D\\uDCFB Program\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SONG\",\n                                        children: \"\\uD83C\\uDFB5 Song\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROMO\",\n                                        children: \"\\uD83D\\uDCE2 Promo\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"STING\",\n                                        children: \"⚡ Sting\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILLER\",\n                                        children: \"⏸️ Filler\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NEXT\",\n                                        children: \"▶️ Next\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NOW\",\n                                        children: \"\\uD83D\\uDD34 Now\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"سنعود\",\n                                        children: \"⏰ سنعود\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"عدنا\",\n                                        children: \"✅ عدنا\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MINI\",\n                                        children: \"\\uD83D\\uDD38 Mini\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"CROSS\",\n                                        children: \"✖️ Cross\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"\\uD83D\\uDD0D \".concat(t('schedule.searchMedia')),\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '15px',\n                                    fontSize: '14px',\n                                    color: '#333',\n                                    background: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '12px',\n                                    color: '#d1d5db',\n                                    marginBottom: '10px',\n                                    textAlign: 'center',\n                                    padding: '5px',\n                                    background: '#1f2937',\n                                    borderRadius: '4px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    t('schedule.resultsCount', {\n                                        count: filteredMedia.length,\n                                        total: allAvailableMedia.length\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '8px'\n                                },\n                                children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '20px',\n                                        color: '#666',\n                                        background: '#f8f9fa',\n                                        borderRadius: '8px',\n                                        border: '2px dashed #dee2e6'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1197,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: 'bold',\n                                                marginBottom: '5px'\n                                            },\n                                            children: t('schedule.noMedia')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: searchTerm || selectedType ? t('schedule.changeFilter') : t('schedule.addNewMedia')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1189,\n                                    columnNumber: 17\n                                }, this) : filteredMedia.map((item)=>{\n                                    // تحديد لون المادة حسب النوع\n                                    const getItemStyle = ()=>{\n                                        if (item.isTemporary) {\n                                            switch(item.type){\n                                                case 'LIVE':\n                                                    return {\n                                                        background: '#ffebee',\n                                                        border: '2px solid #f44336',\n                                                        borderLeft: '5px solid #f44336'\n                                                    };\n                                                case 'PENDING':\n                                                    return {\n                                                        background: '#fff8e1',\n                                                        border: '2px solid #ffc107',\n                                                        borderLeft: '5px solid #ffc107'\n                                                    };\n                                                default:\n                                                    return {\n                                                        background: '#f3e5f5',\n                                                        border: '2px solid #9c27b0',\n                                                        borderLeft: '5px solid #9c27b0'\n                                                    };\n                                            }\n                                        }\n                                        return {\n                                            background: '#fff',\n                                            border: '1px solid #ddd'\n                                        };\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>handleDragStart(e, item),\n                                        style: {\n                                            ...getItemStyle(),\n                                            borderRadius: '8px',\n                                            padding: '12px',\n                                            cursor: 'grab',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                            position: 'relative'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(-2px)';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(0)';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteTempMedia(item.id);\n                                                },\n                                                style: {\n                                                    position: 'absolute',\n                                                    top: '5px',\n                                                    left: '5px',\n                                                    background: '#f44336',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '50%',\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    fontSize: '12px',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                title: t('schedule.confirmDeleteTemp'),\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: 'bold',\n                                                    color: '#333',\n                                                    marginBottom: '4px'\n                                                },\n                                                children: [\n                                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '10px',\n                                                            background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                            color: 'white',\n                                                            padding: '2px 6px',\n                                                            borderRadius: '10px',\n                                                            marginLeft: '5px'\n                                                        },\n                                                        children: item.type === 'LIVE' ? \"\\uD83D\\uDD34 \".concat(t('schedule.liveProgram')) : item.type === 'PENDING' ? \"\\uD83D\\uDFE1 \".concat(t('schedule.pendingDelivery')) : \"\\uD83D\\uDFE3 \".concat(t('schedule.temporary'))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1289,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    getMediaDisplayText(item)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '12px',\n                                                    color: '#666'\n                                                },\n                                                children: [\n                                                    item.type,\n                                                    \" • \",\n                                                    item.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '11px',\n                                                    color: '#888',\n                                                    marginTop: '4px'\n                                                },\n                                                children: item.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1236,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 980,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px',\n                                    background: '#4a5568',\n                                    padding: '15px',\n                                    borderRadius: '15px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/daily-schedule',\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: [\n                                                    \"\\uD83D\\uDCCB \",\n                                                    t('schedule.broadcastSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1333,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                        const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                        if (!response.ok) {\n                                                            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                        }\n                                                        const blob = await response.blob();\n                                                        const url = window.URL.createObjectURL(blob);\n                                                        const a = document.createElement('a');\n                                                        a.href = url;\n                                                        a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                        document.body.appendChild(a);\n                                                        a.click();\n                                                        window.URL.revokeObjectURL(url);\n                                                        document.body.removeChild(a);\n                                                        console.log('✅ تم تصدير الخريطة بنجاح');\n                                                    } catch (error) {\n                                                        console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                        alert('فشل في تصدير الخريطة: ' + error.message);\n                                                    }\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: [\n                                                    \"\\uD83D\\uDCCA \",\n                                                    t('schedule.exportSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#f3f4f6',\n                                                    fontSize: '1.2rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCC5 \",\n                                                    t('schedule.selectedWeek')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1332,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(-1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: isRTL ? '← الأسبوع السابق' : '← Previous Week'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1409,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedWeek,\n                                                onChange: (e)=>{\n                                                    const selectedDate = new Date(e.target.value + 'T00:00:00');\n                                                    const dayOfWeek = selectedDate.getDay();\n                                                    console.log('📅 تغيير التاريخ من التقويم:', {\n                                                        selectedDate: e.target.value,\n                                                        dayOfWeek: dayOfWeek,\n                                                        dayName: [\n                                                            'الأحد',\n                                                            'الاثنين',\n                                                            'الثلاثاء',\n                                                            'الأربعاء',\n                                                            'الخميس',\n                                                            'الجمعة',\n                                                            'السبت'\n                                                        ][dayOfWeek]\n                                                    });\n                                                    // حساب بداية الأسبوع (الأحد)\n                                                    const sunday = new Date(selectedDate);\n                                                    sunday.setHours(0, 0, 0, 0); // تصفير الوقت\n                                                    sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                                    const weekStart = sunday.toISOString().split('T')[0];\n                                                    console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                                    console.log('📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');\n                                                    setSelectedWeek(weekStart);\n                                                },\n                                                style: {\n                                                    padding: '8px',\n                                                    border: '1px solid #6b7280',\n                                                    borderRadius: '5px',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1423,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: isRTL ? 'الأسبوع التالي →' : 'Next Week →'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1322,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: scheduleTableRef,\n                                style: {\n                                    background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',\n                                    borderRadius: '10px',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                    minHeight: '80vh'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#f8f9fa'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '12px',\n                                                            border: '1px solid #dee2e6',\n                                                            fontWeight: 'bold',\n                                                            width: '80px',\n                                                            color: '#000'\n                                                        },\n                                                        children: t('schedule.time')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1486,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '12px',\n                                                                border: '1px solid #dee2e6',\n                                                                fontWeight: 'bold',\n                                                                width: \"\".concat(100 / 7, \"%\"),\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        marginBottom: '4px',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: day\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1503,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: weekDates[index]\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1504,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1496,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1484,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hour-row\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                background: '#f8f9fa',\n                                                                fontWeight: 'bold',\n                                                                textAlign: 'center',\n                                                                padding: '8px',\n                                                                border: '1px solid #dee2e6',\n                                                                color: '#000'\n                                                            },\n                                                            children: hour\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1516,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        days.map((_, dayIndex)=>{\n                                                            const cellItems = getItemsForCell(dayIndex, hour);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                onDragOver: handleDragOver,\n                                                                onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                                style: {\n                                                                    border: '1px solid #dee2e6',\n                                                                    padding: '8px',\n                                                                    height: '150px',\n                                                                    cursor: 'pointer',\n                                                                    background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',\n                                                                    verticalAlign: 'top'\n                                                                },\n                                                                children: cellItems.map((item)=>{\n                                                                    var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        draggable: true,\n                                                                        onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                        onClick: ()=>deleteItem(item),\n                                                                        style: {\n                                                                            background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                            border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                            borderRadius: '4px',\n                                                                            padding: '8px 6px',\n                                                                            marginBottom: '4px',\n                                                                            fontSize: '1rem',\n                                                                            cursor: 'grab',\n                                                                            transition: 'all 0.2s ease',\n                                                                            minHeight: '60px',\n                                                                            display: 'flex',\n                                                                            flexDirection: 'column',\n                                                                            justifyContent: 'center'\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1.02)';\n                                                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1)';\n                                                                            e.currentTarget.style.boxShadow = 'none';\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    gap: '4px',\n                                                                                    color: '#000'\n                                                                                },\n                                                                                children: [\n                                                                                    item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#666'\n                                                                                        },\n                                                                                        children: [\n                                                                                            \"♻️ \",\n                                                                                            item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                            item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1584,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                        },\n                                                                                        children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1588,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#ff9800'\n                                                                                        },\n                                                                                        children: \"\\uD83C\\uDF1F\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1596,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#000'\n                                                                                        },\n                                                                                        children: item.mediaItem ? getMediaDisplayText(item.mediaItem) : 'غير معروف'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1598,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1582,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.8rem',\n                                                                                    color: '#000',\n                                                                                    marginTop: '4px'\n                                                                                },\n                                                                                children: [\n                                                                                    item.startTime,\n                                                                                    \" - \",\n                                                                                    item.endTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1602,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.5rem',\n                                                                                    color: '#888',\n                                                                                    fontStyle: 'italic'\n                                                                                },\n                                                                                children: t('schedule.rerunIndicator')\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1606,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, item.id, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1544,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, dayIndex, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1530,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, hourIndex, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1515,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1513,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1482,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    padding: '20px',\n                                    borderRadius: '15px',\n                                    marginTop: '20px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 20px 0',\n                                            fontSize: '1.3rem',\n                                            textAlign: 'center'\n                                        },\n                                        children: t('schedule.usageInstructions')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.addMediaTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1633,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction1')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1635,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction2')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1636,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction3')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1637,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction4')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1638,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1634,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1632,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.deleteMediaTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1642,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            t('schedule.originalMaterial'),\n                                                                            \":\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1644,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    t('schedule.deleteOriginalInfo')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1644,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            t('schedule.rerunMaterial'),\n                                                                            \":\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1645,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    t('schedule.deleteRerunInfo')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1645,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.deleteConfirmInfo')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1646,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1643,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1641,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1631,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.primeTimeTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1653,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.primeTimeSchedule1')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1655,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.primeTimeSchedule2')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1656,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    style: {\n                                                                        color: '#fbbf24'\n                                                                    },\n                                                                    children: t('schedule.primeTimeColor')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1657,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1657,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1654,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1652,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.rerunsTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1661,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: t('schedule.rerunsSchedule1')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1663,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1663,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart1Sun')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1665,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart2Sun')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1666,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1664,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: t('schedule.rerunsSchedule2')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1668,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1668,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart1Thu')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1670,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart2Thu')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1669,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    style: {\n                                                                        color: '#9ca3af'\n                                                                    },\n                                                                    children: t('schedule.rerunsColor')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1673,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1673,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1662,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1660,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1651,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #f59e0b'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: t('schedule.dateManagementTitle')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1679,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: [\n                                                    \" \",\n                                                    t('schedule.dateManagementInfo')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1680,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1678,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #3b82f6'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: t('schedule.importantNoteTitle')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1684,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: [\n                                                    \" \",\n                                                    t('schedule.importantNoteInfo')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1685,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1683,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1622,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1320,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 972,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 971,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 970,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"z+bOjI1IMp+y5Jz2wzsc+HDFldA=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ })

});