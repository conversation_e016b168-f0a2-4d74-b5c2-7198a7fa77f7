'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useTranslatedToast } from '@/hooks/useTranslatedToast';
import { useAppTranslation } from '@/hooks/useAppTranslation';

interface User {
  id: string;
  username: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  lastLogin: string | null;
  roleInfo: {
    name: string;
    description: string;
    permissions: string[];
  };
}

export default function AdminDashboard() {
  const router = useRouter();
  const { user, logout } = useAuth();
  const { showSuccessToast, showErrorToast, ToastContainer } = useTranslatedToast();
  const { t, tRole, tRoleDesc, isRTL } = useAppTranslation();

  // دالة ترجمة أسماء الأدوار
  const getRoleName = (role: string) => {
    const names: { [key: string]: string } = {
      'ADMIN': 'مدير النظام',
      'CONTENT_MANAGER': 'مدير المحتوى',
      'MEDIA_MANAGER': 'مدير قاعدة البيانات',
      'SCHEDULER': 'مجدول البرامج',
      'FULL_VIEWER': 'مستخدم رؤية كاملة',
      'DATA_ENTRY': 'مدخل بيانات',
      'MAP_SCHEDULER': 'مسؤول الخريطة والجدول',
      'VIEWER': 'مستخدم عرض'
    };
    return names[role] || role;
  };

  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddUser, setShowAddUser] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  
  const [editUserData, setEditUserData] = useState({
    id: '',
    username: '',
    password: '',
    name: '',
    email: '',
    phone: '',
    role: '',
    isActive: true
  });
  
  const [newUser, setNewUser] = useState({
    username: '',
    password: '',
    name: '',
    email: '',
    phone: '',
    role: 'VIEWER'
  });

  const roles = {
    'ADMIN': { name: t('roles.ADMIN'), color: '#dc3545', icon: '👑' },
    'CONTENT_MANAGER': { name: t('roles.CONTENT_MANAGER'), color: '#6f42c1', icon: '📊' },
    'MEDIA_MANAGER': { name: t('roles.MEDIA_MANAGER'), color: '#28a745', icon: '📝' },
    'SCHEDULER': { name: t('roles.SCHEDULER'), color: '#007bff', icon: '📅' },
    'FULL_VIEWER': { name: t('roles.FULL_VIEWER'), color: '#17a2b8', icon: '👓' },
    'DATA_ENTRY': { name: t('roles.DATA_ENTRY'), color: '#fd7e14', icon: '📋' },
    'MAP_SCHEDULER': { name: t('roles.MAP_SCHEDULER'), color: '#20c997', icon: '🗺️' },
    'VIEWER': { name: t('roles.VIEWER'), color: '#6c757d', icon: '👁️' }
  };

  useEffect(() => {
    fetchUsers();
  }, []);
  
  // تحديث بيانات التعديل عند اختيار مستخدم
  useEffect(() => {
    if (editingUser) {
      setEditUserData({
        id: editingUser.id,
        username: editingUser.username,
        password: '', // لا نعرض كلمة المرور الحالية
        name: editingUser.name,
        email: editingUser.email || '',
        phone: editingUser.phone || '',
        role: editingUser.role,
        isActive: editingUser.isActive
      });
    }
  }, [editingUser]);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users');
      const result = await response.json();
      
      if (result.success) {
        setUsers(result.users);
      } else {
        showErrorToast('serverConnection');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      showErrorToast('serverConnection');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newUser.username || !newUser.password || !newUser.name) {
      showErrorToast('invalidData');
      return;
    }

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUser)
      });

      const result = await response.json();
      
      if (result.success) {
        showSuccessToast('userCreated');
        setUsers([...users, result.user]);
        setShowAddUser(false);
        setNewUser({ username: '', password: '', name: '', email: '', phone: '', role: 'VIEWER' });
      } else {
        showErrorToast('unknownError');
      }
    } catch (error) {
      console.error('Error adding user:', error);
      showErrorToast('unknownError');
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm(t('admin.confirmDelete'))) return;

    try {
      const response = await fetch(`/api/users?id=${userId}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      
      if (result.success) {
        showSuccessToast('userDeleted');
        setUsers(users.filter(u => u.id !== userId));
      } else {
        showErrorToast('unknownError');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      showErrorToast('unknownError');
    }
  };
  
  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!editUserData.username || !editUserData.name) {
      showErrorToast('invalidData');
      return;
    }
    
    try {
      // إذا كانت كلمة المرور فارغة، لا نرسلها للتحديث
      const userData = { ...editUserData };
      if (!userData.password) {
        delete userData.password;
      }
      
      const response = await fetch(`/api/users?id=${userData.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });
      
      const result = await response.json();
      
      if (result.success) {
        showSuccessToast('userUpdated');
        setEditingUser(null);
        fetchUsers();
      } else {
        showErrorToast('unknownError');
      }
    } catch (error) {
      console.error('Error updating user:', error);
      showErrorToast('unknownError');
    }
  };

  const inputStyle = {
    width: '100%',
    padding: '12px',
    border: '2px solid #e0e0e0',
    borderRadius: '8px',
    fontSize: '1rem',
    fontFamily: 'Cairo, Arial, sans-serif',
    direction: (isRTL ? 'rtl' : 'ltr') as const,
    outline: 'none'
  };

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'white',
          borderRadius: '20px',
          padding: '40px',
          textAlign: 'center',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}>
          <h2>⏳ {t('admin.loadingData')}</h2>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard requiredRole="ADMIN">
      <DashboardLayout title={t('admin.title')} subtitle={t('admin.subtitle')} icon="👥">

        {/* إحصائيات سريعة */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '20px',
          marginBottom: '20px'
        }}>
          {Object.entries(roles).map(([roleKey, roleInfo]) => {
            const count = users.filter(u => u.role === roleKey).length;
            return (
              <div key={roleKey} style={{
                background: '#4a5568',
                borderRadius: '15px',
                padding: '20px',
                border: '1px solid #6b7280',
                textAlign: 'center'
              }}>
                <div style={{
                  fontSize: '2rem',
                  marginBottom: '10px'
                }}>
                  {roleInfo.icon}
                </div>
                <h3 style={{
                  color: '#f3f4f6',
                  margin: '0 0 5px 0',
                  fontSize: '1rem'
                }}>
                  {roleInfo.name}
                </h3>
                <div style={{
                  fontSize: '1.8rem',
                  fontWeight: 'bold',
                  color: roleInfo.color
                }}>
                  {count}
                </div>
                <p style={{
                  color: '#d1d5db',
                  fontSize: '0.8rem',
                  margin: 0
                }}>
                  {t('common.user')}
                </p>
              </div>
            );
          })}
        </div>

        {/* قسم إدارة المستخدمين */}
        <div style={{
          background: '#4a5568',
          borderRadius: '15px',
          padding: '30px',
          border: '1px solid #6b7280'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '25px'
          }}>
            <h2 style={{
              color: '#f3f4f6',
              fontSize: '1.5rem',
              margin: 0
            }}>
              👥 {t('admin.userManagement')} ({users.length})
            </h2>
            <button
              onClick={() => setShowAddUser(true)}
              style={{
                background: 'linear-gradient(45deg, #10b981, #059669)',
                color: 'white',
                border: 'none',
                borderRadius: '10px',
                padding: '12px 20px',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              ➕ {t('admin.addNewUser')}
            </button>
          </div>

          {/* نموذج إضافة مستخدم */}
          {showAddUser && (
            <div style={{
              background: '#1f2937',
              borderRadius: '15px',
              padding: '25px',
              marginBottom: '25px',
              border: '1px solid #6b7280'
            }}>
              <h3 style={{ color: '#f3f4f6', marginBottom: '20px' }}>➕ {t('admin.addNewUser')}</h3>
              <form onSubmit={handleAddUser}>
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '15px',
                  marginBottom: '20px'
                }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.username')} *
                    </label>
                    <input
                      type="text"
                      placeholder={t('admin.username')}
                      value={newUser.username}
                      onChange={(e) => setNewUser({...newUser, username: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.password')} *
                    </label>
                    <input
                      type="password"
                      placeholder={t('admin.password')}
                      value={newUser.password}
                      onChange={(e) => setNewUser({...newUser, password: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.fullName')} *
                    </label>
                    <input
                      type="text"
                      placeholder={t('admin.fullName')}
                      value={newUser.name}
                      onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.email')}
                    </label>
                    <input
                      type="email"
                      placeholder={t('admin.email')}
                      value={newUser.email}
                      onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.phone')}
                    </label>
                    <input
                      type="tel"
                      placeholder={t('admin.phone')}
                      value={newUser.phone}
                      onChange={(e) => setNewUser({...newUser, phone: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.role')} *
                    </label>
                    <select
                      value={newUser.role}
                      onChange={(e) => setNewUser({...newUser, role: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    >
                      {Object.entries(roles).map(([key, role]) => (
                        <option key={key} value={key}>{role.name}</option>
                      ))}
                    </select>
                  </div>
                </div>
                <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
                  <button
                    type="submit"
                    style={{
                      background: 'linear-gradient(45deg, #10b981, #059669)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '12px 25px',
                      cursor: 'pointer',
                      fontSize: '1rem',
                      fontWeight: 'bold'
                    }}
                  >
                    ✅ {t('admin.createUser')}
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowAddUser(false);
                      setNewUser({ username: '', password: '', name: '', email: '', phone: '', role: 'VIEWER' });
                    }}
                    style={{
                      background: '#6c757d',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '12px 25px',
                      cursor: 'pointer',
                      fontSize: '1rem',
                      fontWeight: 'bold'
                    }}
                  >
                    ❌ {t('admin.cancel')}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* جدول المستخدمين */}
          <div style={{ overflowX: 'auto' }}>
            <table style={{
              width: '100%',
              borderCollapse: 'collapse',
              color: '#f3f4f6',
              fontSize: '0.95rem'
            }}>
              <thead>
                <tr style={{
                  background: '#374151',
                  borderBottom: '2px solid #4b5563'
                }}>
                  <th style={{ padding: '15px 10px', textAlign: isRTL ? 'right' : 'left' }}>{t('common.user')}</th>
                  <th style={{ padding: '15px 10px', textAlign: isRTL ? 'right' : 'left' }}>{t('admin.role')}</th>
                  <th style={{ padding: '15px 10px', textAlign: isRTL ? 'right' : 'left' }}>{t('admin.lastLogin')}</th>
                  <th style={{ padding: '15px 10px', textAlign: 'center' }}>{t('admin.actions')}</th>
                </tr>
              </thead>
              <tbody>
                {users.map(user => (
                  <tr key={user.id} style={{
                    borderBottom: '1px solid #4b5563',
                    background: '#2d3748'
                  }}>
                    <td style={{ padding: '15px 10px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
                        <div style={{
                          width: '40px',
                          height: '40px',
                          borderRadius: '50%',
                          background: roles[user.role as keyof typeof roles]?.color || '#6c757d',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          fontSize: '1.2rem'
                        }}>
                          {roles[user.role as keyof typeof roles]?.icon || '👤'}
                        </div>
                        <div>
                          <div style={{ fontWeight: 'bold' }}>{user.name}</div>
                          <div style={{ fontSize: '0.85rem', color: '#9ca3af' }}>@{user.username}</div>
                        </div>
                      </div>
                    </td>
                    <td style={{ padding: '15px 10px' }}>
                      <div style={{
                        display: 'inline-block',
                        padding: '5px 10px',
                        borderRadius: '20px',
                        background: roles[user.role as keyof typeof roles]?.color || '#6c757d',
                        color: 'white',
                        fontSize: '0.85rem',
                        fontWeight: 'bold'
                      }}>
                        {getRoleName(user.role)}
                      </div>
                      <div style={{ fontSize: '0.8rem', color: '#9ca3af', marginTop: '5px' }}>
                        {getRoleDescription(user.role).substring(0, 50) + (getRoleDescription(user.role).length > 50 ? '...' : '')}
                      </div>
                    </td>
                    <td style={{ padding: '15px 10px' }}>
                      {user.lastLogin ? new Date(user.lastLogin).toLocaleString(isRTL ? 'ar-SA' : 'en-US') : t('admin.noLoginYet')}
                    </td>
                    <td style={{ padding: '15px 10px', textAlign: 'center' }}>
                      <div style={{ display: 'flex', gap: '10px', justifyContent: 'center' }}>
                        <button
                          onClick={() => {
                            // تحرير المستخدم
                            setEditingUser(user);
                          }}
                          style={{
                            background: '#3b82f6',
                            color: 'white',
                            border: 'none',
                            borderRadius: '5px',
                            padding: '8px 12px',
                            cursor: 'pointer'
                          }}
                        >
                          ✏️
                        </button>
                        <button
                          onClick={() => handleDeleteUser(user.id)}
                          disabled={user.role === 'ADMIN' && user.id === '1'}
                          style={{
                            background: user.role === 'ADMIN' && user.id === '1' ? '#6c757d' : '#ef4444',
                            color: 'white',
                            border: 'none',
                            borderRadius: '5px',
                            padding: '8px 12px',
                            cursor: user.role === 'ADMIN' && user.id === '1' ? 'not-allowed' : 'pointer',
                            opacity: user.role === 'ADMIN' && user.id === '1' ? 0.5 : 1
                          }}
                        >
                          🗑️
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {users.length === 0 && (
            <div style={{
              textAlign: 'center',
              padding: '30px',
              color: '#9ca3af'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '10px' }}>🔍</div>
              <h3>{t('admin.noUsers')}</h3>
              <p>{t('admin.addUsersMessage')}</p>
            </div>
          )}
        </div>

        {/* قسم شرح الأدوار */}
        <div style={{
          background: '#4a5568',
          borderRadius: '15px',
          padding: '30px',
          border: '1px solid #6b7280',
          marginTop: '30px'
        }}>
          <h2 style={{
            color: '#f3f4f6',
            fontSize: '1.5rem',
            marginBottom: '20px'
          }}>
            🔑 شرح الأدوار والصلاحيات
          </h2>

          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
            gap: '20px'
          }}>
            {Object.entries(roles).map(([key, role]) => (
              <div key={key} style={{
                background: '#2d3748',
                borderRadius: '10px',
                padding: '20px',
                border: '1px solid #4b5563'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px',
                  marginBottom: '15px'
                }}>
                  <div style={{
                    width: '40px',
                    height: '40px',
                    borderRadius: '50%',
                    background: role.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '1.5rem'
                  }}>
                    {role.icon}
                  </div>
                  <div style={{
                    fontWeight: 'bold',
                    fontSize: '1.2rem',
                    color: '#f3f4f6'
                  }}>
                    {getRoleName(key)}
                  </div>
                </div>

                <div style={{
                  fontSize: '0.9rem',
                  color: '#d1d5db',
                  marginBottom: '15px',
                  lineHeight: '1.5'
                }}>
                  {getRoleDescription(key)}
                </div>

                <div style={{
                  background: '#1f2937',
                  borderRadius: '8px',
                  padding: '10px',
                  fontSize: '0.85rem',
                  color: '#9ca3af'
                }}>
                  <div style={{ marginBottom: '5px', color: '#d1d5db' }}>الصلاحيات:</div>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
                    {key === 'ADMIN' && (
                      <span style={{
                        background: '#dc3545',
                        color: 'white',
                        padding: '3px 8px',
                        borderRadius: '5px',
                        fontSize: '0.75rem'
                      }}>
                        جميع الصلاحيات
                      </span>
                    )}
                    {key === 'CONTENT_MANAGER' && (
                      <>
                        <span style={{
                          background: '#28a745',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          إدارة المواد
                        </span>
                        <span style={{
                          background: '#007bff',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          إدارة الجداول
                        </span>
                      </>
                    )}
                    {key === 'MEDIA_MANAGER' && (
                      <>
                        <span style={{
                          background: '#28a745',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          إدارة المواد
                        </span>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض الجداول
                        </span>
                      </>
                    )}
                    {key === 'SCHEDULER' && (
                      <>
                        <span style={{
                          background: '#007bff',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          إدارة الجداول
                        </span>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض المواد
                        </span>
                      </>
                    )}
                    {key === 'FULL_VIEWER' && (
                      <>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض المواد
                        </span>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض الجداول
                        </span>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض الخريطة
                        </span>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض البث
                        </span>
                      </>
                    )}
                    {key === 'DATA_ENTRY' && (
                      <span style={{
                        background: '#28a745',
                        color: 'white',
                        padding: '3px 8px',
                        borderRadius: '5px',
                        fontSize: '0.75rem'
                      }}>
                        إدارة المواد
                      </span>
                    )}
                    {key === 'MAP_SCHEDULER' && (
                      <>
                        <span style={{
                          background: '#20c997',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          إدارة الخريطة
                        </span>
                        <span style={{
                          background: '#007bff',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          إدارة الجداول
                        </span>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض المواد
                        </span>
                      </>
                    )}
                    {key === 'VIEWER' && (
                      <>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض المواد
                        </span>
                        <span style={{
                          background: '#6c757d',
                          color: 'white',
                          padding: '3px 8px',
                          borderRadius: '5px',
                          fontSize: '0.75rem'
                        }}>
                          عرض الجداول
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* نموذج تعديل المستخدم */}
        {editingUser && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 1000,
            padding: '20px'
          }}>
            <div style={{
              background: '#2d3748',
              borderRadius: '15px',
              padding: '30px',
              width: '100%',
              maxWidth: '600px',
              maxHeight: '90vh',
              overflowY: 'auto',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
              position: 'relative'
            }}>
              <button
                onClick={() => setEditingUser(null)}
                style={{
                  position: 'absolute',
                  top: '15px',
                  left: '15px',
                  background: 'none',
                  border: 'none',
                  color: '#f3f4f6',
                  fontSize: '1.5rem',
                  cursor: 'pointer'
                }}
              >
                ✖️
              </button>
              
              <h2 style={{
                color: '#f3f4f6',
                textAlign: 'center',
                marginBottom: '25px',
                fontSize: '1.5rem'
              }}>
                ✏️ {t('admin.editingUser')}: {editingUser.name}
              </h2>
              
              <form onSubmit={handleUpdateUser}>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: '1fr 1fr',
                  gap: '20px',
                  marginBottom: '20px'
                }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.username')} *
                    </label>
                    <input
                      type="text"
                      value={editUserData.username}
                      onChange={(e) => setEditUserData({...editUserData, username: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    />
                  </div>
                  
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.password')} {t('admin.passwordNote')}
                    </label>
                    <input
                      type="password"
                      value={editUserData.password}
                      onChange={(e) => setEditUserData({...editUserData, password: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  </div>
                  
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.fullName')} *
                    </label>
                    <input
                      type="text"
                      value={editUserData.name}
                      onChange={(e) => setEditUserData({...editUserData, name: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    />
                  </div>
                  
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.email')}
                    </label>
                    <input
                      type="email"
                      value={editUserData.email}
                      onChange={(e) => setEditUserData({...editUserData, email: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  </div>
                  
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.phone')}
                    </label>
                    <input
                      type="tel"
                      value={editUserData.phone}
                      onChange={(e) => setEditUserData({...editUserData, phone: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                    />
                  </div>
                  
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.role')} *
                    </label>
                    <select
                      value={editUserData.role}
                      onChange={(e) => setEditUserData({...editUserData, role: e.target.value})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    >
                      {Object.entries(roles).map(([key, role]) => (
                        <option key={key} value={key}>
                          {role.name}
                        </option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontWeight: 'bold' }}>
                      {t('admin.status')} *
                    </label>
                    <select
                      value={editUserData.isActive.toString()}
                      onChange={(e) => setEditUserData({...editUserData, isActive: e.target.value === 'true'})}
                      style={{
                        ...inputStyle,
                        background: 'white',
                        color: '#333'
                      }}
                      required
                    >
                      <option value="true">{t('admin.active')}</option>
                      <option value="false">{t('admin.inactive')}</option>
                    </select>
                  </div>
                </div>
                
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  gap: '15px',
                  marginTop: '30px'
                }}>
                  <button
                    type="submit"
                    style={{
                      background: '#3b82f6',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '12px 25px',
                      cursor: 'pointer',
                      fontSize: '1rem',
                      fontWeight: 'bold'
                    }}
                  >
                    💾 {t('admin.saveChanges')}
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => setEditingUser(null)}
                    style={{
                      background: '#6c757d',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      padding: '12px 25px',
                      cursor: 'pointer',
                      fontSize: '1rem',
                      fontWeight: 'bold'
                    }}
                  >
                    ❌ {t('common.cancel')}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </DashboardLayout>
      <ToastContainer />
    </AuthGuard>
  );
}