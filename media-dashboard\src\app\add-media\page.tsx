'use client';
import { useState, useEffect } from 'react';
import { useTranslatedToast } from '@/hooks/useTranslatedToast';
import { useRouter } from 'next/navigation';
import { useAppTranslation } from '@/hooks/useAppTranslation';
import DashboardLayout from '@/components/DashboardLayout';

export default function AddMediaPage() {
  const router = useRouter();
  const { showSuccessToast, showErrorToast, ToastContainer } = useTranslatedToast();
  const { t, tMediaType, isRTL } = useAppTranslation();
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    description: '',
    channel: '',
    source: '',
    status: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: '',
    notes: '',
    episodeNumber: '',
    seasonNumber: '',
    partNumber: '',
    hardDiskNumber: '',
    showInTX: false, // إضافة حقل TX
  });

  // إضافة حالة لإظهار/إخفاء الحقول الخاصة حسب نوع المادة
  const [showEpisodeNumber, setShowEpisodeNumber] = useState(false);
  const [showSeasonNumber, setShowSeasonNumber] = useState(false);
  const [showPartNumber, setShowPartNumber] = useState(false);

  // تحديث الحقول المرئية عند تغيير نوع المادة
  useEffect(() => {
    console.log('🔍 نوع المادة المختار:', formData.type);

    if (formData.type === 'FILM') {
      // فيلم: يظهر رقم الجزء فقط
      console.log('✅ عرض حقول الفيلم: رقم الجزء فقط');
      setShowEpisodeNumber(false);
      setShowSeasonNumber(false);
      setShowPartNumber(true);
    } else if (formData.type === 'SERIES') {
      // مسلسل: يظهر رقم الحلقة ورقم الجزء
      console.log('✅ عرض حقول المسلسل: رقم الحلقة + رقم الجزء');
      setShowEpisodeNumber(true);
      setShowSeasonNumber(false);
      setShowPartNumber(true);
    } else if (formData.type === 'PROGRAM') {
      // برنامج: يظهر رقم الحلقة ورقم الموسم
      console.log('✅ عرض حقول البرنامج: رقم الحلقة + رقم الموسم');
      setShowEpisodeNumber(true);
      setShowSeasonNumber(true);
      setShowPartNumber(false);
    } else {
      // باقي الأنواع: لا تظهر حقول إضافية
      console.log('❌ إخفاء جميع الحقول الإضافية');
      setShowEpisodeNumber(false);
      setShowSeasonNumber(false);
      setShowPartNumber(false);
    }

    console.log('📊 حالة الحقول:', {
      showEpisodeNumber,
      showSeasonNumber,
      showPartNumber
    });
  }, [formData.type]);

  const [segmentCount, setSegmentCount] = useState(1);
  const [segments, setSegments] = useState([
    {
      id: 1,
      segmentCode: '',
      timeIn: '00:00:00',
      timeOut: '',
      duration: '00:00:00'
    }
  ]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'showInTX' ? value === 'true' : value
    }));
  };

  const handleSegmentChange = (segmentId: number, field: string, value: string) => {
    setSegments(prev => prev.map(segment =>
      segment.id === segmentId
        ? { ...segment, [field]: value }
        : segment
    ));

    // التحقق من كود السيجمانت عند تغييره
    if (field === 'segmentCode' && value.trim()) {
      validateSegmentCode(segmentId, value.trim());
    } else if (field === 'segmentCode' && !value.trim()) {
      // إزالة التحقق إذا كان الكود فارغ
      setSegmentCodeValidation(prev => {
        const newValidation = { ...prev };
        delete newValidation[segmentId];
        return newValidation;
      });
    }
  };

  // دالة التحقق من كود السيجمانت
  const validateSegmentCode = async (segmentId: number, code: string) => {
    // تحديث حالة التحقق
    setSegmentCodeValidation(prev => ({
      ...prev,
      [segmentId]: { isValid: true, message: '', isChecking: true }
    }));

    try {
      const response = await fetch(`/api/validate-segment-code?code=${encodeURIComponent(code)}`);
      const result = await response.json();

      if (result.success) {
        const isValid = !result.isDuplicate;
        const message = result.isDuplicate
          ? `الكود موجود في: ${result.duplicates.map((d: any) => d.mediaName).join(', ')}`
          : 'الكود متاح';

        setSegmentCodeValidation(prev => ({
          ...prev,
          [segmentId]: { isValid, message, isChecking: false }
        }));
      }
    } catch (error) {
      console.error('Error validating segment code:', error);
      setSegmentCodeValidation(prev => ({
        ...prev,
        [segmentId]: { isValid: true, message: 'خطأ في التحقق', isChecking: false }
      }));
    }
  };

  const calculateDuration = (segmentId: number) => {
    const segment = segments.find(s => s.id === segmentId);
    if (!segment || !segment.timeIn || !segment.timeOut) return;

    try {
      const timeIn = segment.timeIn.split(':').map(Number);
      const timeOut = segment.timeOut.split(':').map(Number);

      // تحويل إلى ثواني
      const inSeconds = timeIn[0] * 3600 + timeIn[1] * 60 + timeIn[2];
      const outSeconds = timeOut[0] * 3600 + timeOut[1] * 60 + timeOut[2];

      // حساب الفرق
      let durationSeconds = outSeconds - inSeconds;
      if (durationSeconds < 0) {
        durationSeconds += 24 * 3600; // إضافة يوم كامل إذا كان الوقت سالب
      }

      // تحويل إلى تنسيق HH:MM:SS
      const hours = Math.floor(durationSeconds / 3600);
      const minutes = Math.floor((durationSeconds % 3600) / 60);
      const seconds = durationSeconds % 60;

      const duration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      handleSegmentChange(segmentId, 'duration', duration);
    } catch (error) {
      console.error('Error calculating duration:', error);
    }
  };

  const addSegment = () => {
    const newId = segmentCount + 1;
    setSegmentCount(newId);
    setSegments(prev => [
      ...prev,
      {
        id: newId,
        segmentCode: '',
        timeIn: '00:00:00',
        timeOut: '00:00:00', // قيمة وهمية ستختفي عند النقر
        duration: '00:00:00'
      }
    ]);
  };

  const removeSegment = (id: number) => {
    if (segments.length <= 1) {
      showErrorToast('invalidData');
      return;
    }
    setSegments(prev => prev.filter(segment => segment.id !== id));
  };

  const setSegmentCount2 = (count: number) => {
    if (count < 1) {
      showErrorToast('invalidData');
      return;
    }

    setSegmentCount(count);
    const newSegments = [];
    for (let i = 1; i <= count; i++) {
      newSegments.push({
        id: i,
        segmentCode: '',
        timeIn: '00:00:00',
        timeOut: '00:00:00',
        duration: '00:00:00'
      });
    }
    setSegments(newSegments);
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [segmentCodeValidation, setSegmentCodeValidation] = useState<{ [key: number]: { isValid: boolean; message: string; isChecking: boolean } }>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // التحقق من الحقول المطلوبة
    const requiredFields = [];
    if (!formData.hardDiskNumber.trim()) requiredFields.push('رقم الهارد');
    if (!formData.type) requiredFields.push('نوع المادة');
    if (!formData.channel) requiredFields.push('القناة');
    if (!formData.status) requiredFields.push('الحالة');

    // التحقق من كود السيجمانت
    const segmentsWithoutCode = segments.filter(segment => !segment.segmentCode || segment.segmentCode.trim() === '');
    if (segmentsWithoutCode.length > 0) {
      requiredFields.push('كود السيجمانت (مطلوب لجميع السيجمانتات)');
    }

    // التحقق من Time Out للسيجمانت
    const invalidSegments = segments.filter(segment => !segment.timeOut || segment.timeOut === '00:00:00');
    if (invalidSegments.length > 0) {
      requiredFields.push('Time Out للسيجمانت');
    }

    if (requiredFields.length > 0) {
      showErrorToast('invalidData');
      return;
    }

    // التحقق من تكرار أكواد السيجمانت
    const segmentCodes = segments.map(s => s.segmentCode).filter(code => code && code.trim());

    try {
      const validationResponse = await fetch('/api/validate-segment-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          segmentCodes
        }),
      });

      const validationResult = await validationResponse.json();

      if (validationResult.success && validationResult.hasAnyDuplicates) {
        let errorMessage = 'تم العثور على أكواد مكررة:\n\n';

        if (validationResult.hasInternalDuplicates) {
          errorMessage += `🔴 أكواد مكررة داخل نفس المادة: ${validationResult.internalDuplicates.join(', ')}\n\n`;
        }

        if (validationResult.hasExternalDuplicates) {
          errorMessage += '🔴 أكواد موجودة في مواد أخرى:\n';
          validationResult.externalDuplicates.forEach((dup: any) => {
            errorMessage += `- الكود "${dup.segmentCode}" موجود في المادة: ${dup.mediaName} (${dup.mediaType})\n`;
          });
        }

        errorMessage += '\nيرجى تغيير الأكواد المكررة قبل الحفظ.';
        showErrorToast('invalidData');
        return;
      }
    } catch (validationError) {
      console.error('Error validating segment codes:', validationError);
      showErrorToast('invalidData');
      return;
    }

    setIsSubmitting(true);

    try {
      // تحويل التوكن إلى الصيغة المتوقعة
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const tokenWithRole = `token_${user.id || 'unknown'}_${user.role || 'unknown'}`;
      
      console.log('Sending with token:', tokenWithRole);
      console.log('User data:', user);
      
      const response = await fetch('/api/media', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${tokenWithRole}`
        },
        body: JSON.stringify({
          formData,
          segments
        }),
      });

      const result = await response.json();

      if (result.success) {
        showSuccessToast('mediaAdded');

        // مسح جميع الحقول للبدء من جديد
        setFormData({
          name: '',
          type: '',
          description: '',
          channel: '',
          source: '',
          status: '',
          startDate: new Date().toISOString().split('T')[0],
          endDate: '',
          notes: '',
          episodeNumber: '',
          seasonNumber: '',
          partNumber: '',
          hardDiskNumber: '',
          showInTX: false,
        });

        // مسح السيجمانتات والعودة لسيجمانت واحد
        setSegmentCount(1);
        setSegments([
          {
            id: 1,
            segmentCode: '',
            timeIn: '00:00:00',
            timeOut: '',
            duration: ''
          }
        ]);

        // إخفاء الحقول الخاصة
        setShowEpisodeNumber(false);
        setShowSeasonNumber(false);
        setShowPartNumber(false);

        console.log('✅ تم مسح جميع الحقول بنجاح');
      } else {
        showErrorToast('unknownError');
      }
    } catch (error) {
      console.error('Error saving media:', error);
      showErrorToast('serverConnection');
    } finally {
      setIsSubmitting(false);
    }
  };

  // أنماط CSS
  const inputStyle = {
    width: '100%',
    padding: '10px',
    borderRadius: '5px',
    marginBottom: '10px'
  };

  return (
    <DashboardLayout
      title={t('media.addNew')}
      subtitle={t('media.title')}
      icon="➕"
      requiredPermissions={['MEDIA_CREATE']}
    >

        {/* رسالة ترحيب */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderRadius: '15px',
          padding: '20px',
          marginBottom: '25px',
          border: '1px solid #6b7280',
          textAlign: 'center'
        }}>
          <h2 style={{ color: '#ffffff', marginBottom: '10px', fontSize: '1.4rem', fontWeight: 'bold' }}>
            ➕ {t('addMedia.title')}
          </h2>
          <p style={{ color: '#e2e8f0', fontSize: '0.95rem', margin: 0 }}>
            {t('addMedia.subtitle')}
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div style={{
            background: '#4a5568',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #6b7280'
          }}>
            <h2 style={{ color: '#f3f4f6', marginBottom: '20px', fontSize: '1.3rem' }}>📝 {t('addMedia.basicInfo')}</h2>

            <div style={{ display: 'grid', gap: '15px' }}>
              {/* حقل رقم الهارد */}
              <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr', gap: '15px', alignItems: 'center' }}>
                <label style={{ color: '#f3f4f6', fontWeight: 'bold', fontSize: '0.9rem' }}>
                  💾 {t('addMedia.hardDiskNumber')}: <span style={{ color: '#ef4444' }}>*</span>
                </label>
                <input
                  type="text"
                  placeholder={t('addMedia.hardDiskNumber')}
                  value={formData.hardDiskNumber}
                  onChange={(e) => handleInputChange('hardDiskNumber', e.target.value)}
                  style={{
                    ...inputStyle,
                    maxWidth: '200px',
                    background: '#1f2937',
                    color: 'white',
                    border: '1px solid #6b7280'
                  }}
                />
              </div>

              <div style={{ maxWidth: '500px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                  {t('common.name')} <span style={{ color: '#ef4444' }}>*</span>
                </label>
                <input
                  type="text"
                  placeholder={t('common.name')}
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  style={{
                    ...inputStyle,
                    background: '#1f2937',
                    color: 'white',
                    border: '1px solid #6b7280',
                  }}
                  required
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                    {t('common.type')} <span style={{ color: '#ef4444' }}>*</span>
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => handleInputChange('type', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: '#1f2937',
                      color: 'white',
                      border: '1px solid #6b7280'
                    }}
                    required
                  >
                    <option value="">{t('addMedia.selectType')}</option>
                    <option value="FILM">{t('mediaTypes.FILM')}</option>
                    <option value="SERIES">{t('mediaTypes.SERIES')}</option>
                    <option value="PROGRAM">{t('mediaTypes.PROGRAM')}</option>
                    <option value="SONG">{t('mediaTypes.SONG')}</option>
                    <option value="FILLER">{t('mediaTypes.FILLER')}</option>
                    <option value="STING">{t('mediaTypes.STING')}</option>
                    <option value="PROMO">{t('mediaTypes.PROMO')}</option>
                    <option value="NEXT">{t('mediaTypes.NEXT')}</option>
                    <option value="NOW">{t('mediaTypes.NOW')}</option>
                    <option value="سنعود">{t('mediaTypes.سنعود')}</option>
                    <option value="عدنا">{t('mediaTypes.عدنا')}</option>
                    <option value="MINI">{t('mediaTypes.MINI')}</option>
                    <option value="CROSS">{t('mediaTypes.CROSS')}</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                    {t('addMedia.channel')} <span style={{ color: '#ef4444' }}>*</span>
                  </label>
                  <select
                    value={formData.channel}
                    onChange={(e) => handleInputChange('channel', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: '#1f2937',
                      color: 'white',
                      border: '1px solid #6b7280'
                    }}
                    required
                  >
                    <option value="">{t('addMedia.selectChannel')}</option>
                    <option value="DOCUMENTARY">الوثائقية</option>
                    <option value="NEWS">الأخبار</option>
                    <option value="OTHER">أخرى</option>
                  </select>
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                    {t('common.status')} <span style={{ color: '#ef4444' }}>*</span>
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: '#1f2937',
                      color: 'white',
                      border: '1px solid #6b7280'
                    }}
                    required
                  >
                    <option value="">{t('addMedia.selectStatus')}</option>
                    <option value="VALID">صالح</option>
                    <option value="REJECTED_CENSORSHIP">مرفوض رقابياً</option>
                    <option value="REJECTED_TECHNICAL">مرفوض هندسياً</option>
                    <option value="EXPIRED">منتهى</option>
                    <option value="HOLD">Hold</option>
                  </select>
                </div>

                {/* حقل المصدر */}
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                    {t('addMedia.source')}
                  </label>
                  <input
                    type="text"
                    placeholder={t('addMedia.source')}
                    value={formData.source}
                    onChange={(e) => handleInputChange('source', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: '#1f2937',
                      color: 'white',
                      border: '1px solid #6b7280'
                    }}
                  />
                </div>
              </div>

              {/* حقول التواريخ */}
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                    {t('addMedia.startDate')}
                  </label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => handleInputChange('startDate', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: '#1f2937',
                      color: 'white',
                      border: '1px solid #6b7280'
                    }}
                  />
                </div>

                <div>
                  <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                    {t('addMedia.endDate')}
                  </label>
                  <input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => handleInputChange('endDate', e.target.value)}
                    style={{
                      ...inputStyle,
                      background: '#1f2937',
                      color: 'white',
                      border: '1px solid #6b7280'
                    }}
                  />
                </div>
              </div>

              {/* حقول خاصة حسب نوع المادة */}
              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '15px' }}>
                {/* رقم الحلقة - يظهر للبرامج والمسلسلات */}
                {showEpisodeNumber && (
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                      {t('addMedia.episodeNumber')}
                    </label>
                    <input
                      type="number"
                      placeholder={t('addMedia.episodeNumber')}
                      value={formData.episodeNumber}
                      onChange={(e) => handleInputChange('episodeNumber', e.target.value)}
                      style={{
                        ...inputStyle,
                        background: '#1f2937',
                        color: 'white',
                        border: '1px solid #6b7280'
                      }}
                    />
                  </div>
                )}

                {/* رقم الموسم - يظهر للبرامج فقط */}
                {showSeasonNumber && (
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                      {t('addMedia.seasonNumber')}
                    </label>
                    <input
                      type="number"
                      placeholder={t('addMedia.seasonNumber')}
                      value={formData.seasonNumber}
                      onChange={(e) => handleInputChange('seasonNumber', e.target.value)}
                      style={{
                        ...inputStyle,
                        background: '#1f2937',
                        color: 'white',
                        border: '1px solid #6b7280'
                      }}
                    />
                  </div>
                )}

                {/* رقم الجزء - يظهر للأفلام والمسلسلات */}
                {showPartNumber && (
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                      {t('addMedia.partNumber')}
                    </label>
                    <input
                      type="number"
                      placeholder={t('addMedia.partNumber')}
                      value={formData.partNumber}
                      onChange={(e) => handleInputChange('partNumber', e.target.value)}
                      style={{
                        ...inputStyle,
                        background: '#1f2937',
                        color: 'white',
                        border: '1px solid #6b7280'
                      }}
                    />
                  </div>
                )}
              </div>

              <div style={{ maxWidth: '600px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                  {t('addMedia.description')}
                </label>
                <textarea
                  placeholder={t('addMedia.description')}
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  style={{
                    ...inputStyle,
                    background: '#1f2937',
                    color: 'white',
                    border: '1px solid #6b7280',
                    minHeight: '80px',
                    resize: 'vertical',
                    width: '100%'
                  }}
                />
              </div>

              {/* حقل الملاحظات */}
              <div style={{ maxWidth: '600px' }}>
                <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                  {t('addMedia.notes')}
                </label>
                <textarea
                  placeholder={t('addMedia.additionalNotes')}
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  style={{
                    ...inputStyle,
                    background: '#1f2937',
                    color: 'white',
                    border: '1px solid #6b7280',
                    minHeight: '80px',
                    resize: 'vertical',
                    width: '100%'
                  }}
                />
              </div>
              
              {/* إضافة خيار TX */}
              <div style={{ marginTop: '15px' }}>
                <label style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  color: '#f3f4f6', 
                  fontSize: '0.9rem',
                  cursor: 'pointer'
                }}>
                  <input
                    type="checkbox"
                    checked={formData.showInTX}
                    onChange={(e) => handleInputChange('showInTX', e.target.checked.toString())}
                    style={{ marginLeft: '10px', width: '18px', height: '18px' }}
                  />
                  <span style={{ 
                    background: '#10b981', 
                    color: 'white', 
                    padding: '2px 8px', 
                    borderRadius: '4px', 
                    marginLeft: '10px',
                    fontWeight: 'bold'
                  }}>
                    TX
                  </span>
                  {t('addMedia.showInSchedule')}
                </label>
                <div style={{ 
                  fontSize: '0.8rem', 
                  color: '#9ca3af', 
                  marginTop: '5px', 
                  marginRight: '35px' 
                }}>
                  {t('addMedia.txDescription')}
                </div>
              </div>
            </div>
          </div>

          {/* قسم السيجمانت */}
          <div style={{
            background: '#4a5568',
            borderRadius: '15px',
            padding: '25px',
            marginBottom: '25px',
            border: '1px solid #6b7280'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ color: '#f3f4f6', fontSize: '1.3rem' }}>🎬 {t('addMedia.segments')}</h2>
              <div>
                <button
                  type="button"
                  onClick={addSegment}
                  style={{
                    background: '#3b82f6',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    padding: '8px 15px',
                    marginLeft: '10px',
                    cursor: 'pointer'
                  }}
                >
                  {t('addMedia.addSegment')}
                </button>
                <input
                  type="number"
                  min="1"
                  value={segmentCount}
                  onChange={(e) => setSegmentCount2(parseInt(e.target.value))}
                  style={{
                    width: '60px',
                    padding: '8px',
                    borderRadius: '5px',
                    border: '1px solid #6b7280',
                    background: '#1f2937',
                    color: 'white',
                    textAlign: 'center'
                  }}
                />
              </div>
            </div>

            {segments.map((segment, index) => (
              <div key={segment.id} style={{
                background: '#374151',
                borderRadius: '10px',
                padding: '15px',
                marginBottom: '15px',
                border: '1px solid #4b5563'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
                  <h3 style={{ color: '#f3f4f6', margin: 0 }}>{t('addMedia.segment')} {segment.id}</h3>
                  <button
                    type="button"
                    onClick={() => removeSegment(segment.id)}
                    style={{
                      background: '#ef4444',
                      color: 'white',
                      border: 'none',
                      borderRadius: '5px',
                      padding: '5px 10px',
                      cursor: 'pointer'
                    }}
                  >
                    {t('common.delete')}
                  </button>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                      {t('addMedia.segmentCode')}
                    </label>
                    <div style={{ position: 'relative' }}>
                      <input
                        type="text"
                        value={segment.segmentCode}
                        onChange={(e) => handleSegmentChange(segment.id, 'segmentCode', e.target.value)}
                        placeholder="مثال: DDC000055-P1-3"
                        required
                        style={{
                          ...inputStyle,
                          background: segment.segmentCode ? '#1f2937' : '#7f1d1d',
                          color: 'white',
                          border: segmentCodeValidation[segment.id]?.isValid === false
                            ? '2px solid #ef4444'
                            : segment.segmentCode
                              ? segmentCodeValidation[segment.id]?.isValid === true
                                ? '2px solid #10b981'
                                : '1px solid #6b7280'
                              : '2px solid #ef4444',
                          paddingRight: segmentCodeValidation[segment.id] ? '35px' : '12px'
                        }}
                      />
                      {segmentCodeValidation[segment.id] && (
                        <div style={{
                          position: 'absolute',
                          right: '8px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          fontSize: '16px'
                        }}>
                          {segmentCodeValidation[segment.id].isChecking ? (
                            <span style={{ color: '#fbbf24' }}>⏳</span>
                          ) : segmentCodeValidation[segment.id].isValid ? (
                            <span style={{ color: '#10b981' }}>✅</span>
                          ) : (
                            <span style={{ color: '#ef4444' }}>❌</span>
                          )}
                        </div>
                      )}
                    </div>
                    {segmentCodeValidation[segment.id] && segmentCodeValidation[segment.id].message && (
                      <div style={{
                        fontSize: '0.8rem',
                        marginTop: '4px',
                        color: segmentCodeValidation[segment.id].isValid ? '#10b981' : '#ef4444'
                      }}>
                        {segmentCodeValidation[segment.id].message}
                      </div>
                    )}
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                      Time In
                    </label>
                    <input
                      type="text"
                      value={segment.timeIn}
                      onChange={(e) => handleSegmentChange(segment.id, 'timeIn', e.target.value)}
                      onBlur={() => calculateDuration(segment.id)}
                      placeholder="00:00:00"
                      style={{
                        ...inputStyle,
                        background: '#1f2937',
                        color: 'white',
                        border: '1px solid #6b7280'
                      }}
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                      Time Out <span style={{ color: '#ef4444' }}>*</span>
                    </label>
                    <input
                      type="text"
                      value={segment.timeOut}
                      onChange={(e) => handleSegmentChange(segment.id, 'timeOut', e.target.value)}
                      onBlur={() => calculateDuration(segment.id)}
                      placeholder="00:00:00"
                      onFocus={(e) => {
                        // إزالة القيمة الوهمية عند النقر
                        if (e.target.value === '00:00:00') {
                          handleSegmentChange(segment.id, 'timeOut', '');
                        }
                      }}
                      style={{
                        ...inputStyle,
                        background: '#1f2937',
                        color: 'white',
                        border: '1px solid #6b7280'
                      }}
                      required
                    />
                  </div>

                  <div>
                    <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                      {t('addMedia.durationAutoCalculated')}
                    </label>
                    <input
                      type="text"
                      value={segment.duration}
                      readOnly
                      style={{
                        ...inputStyle,
                        background: '#1f2937',
                        color: 'white',
                        border: '1px solid #6b7280',
                        opacity: '0.7',
                        cursor: 'not-allowed'
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div style={{ display: 'flex', justifyContent: 'center', gap: '15px', marginTop: '20px', flexWrap: 'wrap' }}>
            <button
              type="submit"
              disabled={isSubmitting}
              style={{
                background: '#10b981',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                padding: '12px 24px',
                fontSize: '1rem',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                opacity: isSubmitting ? 0.7 : 1,
                fontWeight: 'bold'
              }}
            >
              {isSubmitting ? `⏳ ${t('common.saving')}` : `💾 ${t('addMedia.saveAndAddNew')}`}
            </button>

            <button
              type="button"
              onClick={() => router.push('/media-list')}
              disabled={isSubmitting}
              style={{
                background: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                padding: '12px 24px',
                fontSize: '1rem',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                opacity: isSubmitting ? 0.7 : 1,
                fontWeight: 'bold'
              }}
            >
              📋 {t('navigation.mediaList')}
            </button>

            <button
              type="button"
              onClick={() => {
                // مسح جميع الحقول يدوياً
                setFormData({
                  name: '',
                  type: '',
                  description: '',
                  channel: '',
                  source: '',
                  status: '',
                  startDate: new Date().toISOString().split('T')[0],
                  endDate: '',
                  notes: '',
                  episodeNumber: '',
                  seasonNumber: '',
                  partNumber: '',
                  hardDiskNumber: '',
                  showInTX: false,
                });

                setSegmentCount(1);
                setSegments([
                  {
                    id: 1,
                    segmentCode: '',
                    timeIn: '00:00:00',
                    timeOut: '',
                    duration: ''
                  }
                ]);

                setShowEpisodeNumber(false);
                setShowSeasonNumber(false);
                setShowPartNumber(false);

                showSuccessToast('changesSaved');
              }}
              disabled={isSubmitting}
              style={{
                background: '#f59e0b',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                padding: '12px 24px',
                fontSize: '1rem',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                opacity: isSubmitting ? 0.7 : 1,
                fontWeight: 'bold'
              }}
            >
              🗑️ {t('addMedia.clearFields')}
            </button>
          </div>
        </form>

      <ToastContainer />
    </DashboardLayout>
  );
}