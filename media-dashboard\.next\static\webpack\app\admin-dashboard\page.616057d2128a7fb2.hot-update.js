"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin-dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin-dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast)();\n    const { t, tRole, tRoleDesc, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__.useAppTranslation)();\n    // دالة ترجمة أسماء الأدوار\n    const getRoleName = (role)=>{\n        const names = {\n            'ADMIN': 'مدير النظام',\n            'CONTENT_MANAGER': 'مدير المحتوى',\n            'MEDIA_MANAGER': 'مدير قاعدة البيانات',\n            'SCHEDULER': 'مجدول البرامج',\n            'FULL_VIEWER': 'مستخدم رؤية كاملة',\n            'DATA_ENTRY': 'مدخل بيانات',\n            'MAP_SCHEDULER': 'مسؤول الخريطة والجدول',\n            'VIEWER': 'مستخدم عرض'\n        };\n        return names[role] || role;\n    };\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddUser, setShowAddUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editUserData, setEditUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '',\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        phone: '',\n        role: '',\n        isActive: true\n    });\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        phone: '',\n        role: 'VIEWER'\n    });\n    const roles = {\n        'ADMIN': {\n            name: t('roles.ADMIN'),\n            color: '#dc3545',\n            icon: '👑'\n        },\n        'CONTENT_MANAGER': {\n            name: t('roles.CONTENT_MANAGER'),\n            color: '#6f42c1',\n            icon: '📊'\n        },\n        'MEDIA_MANAGER': {\n            name: t('roles.MEDIA_MANAGER'),\n            color: '#28a745',\n            icon: '📝'\n        },\n        'SCHEDULER': {\n            name: t('roles.SCHEDULER'),\n            color: '#007bff',\n            icon: '📅'\n        },\n        'FULL_VIEWER': {\n            name: t('roles.FULL_VIEWER'),\n            color: '#17a2b8',\n            icon: '👓'\n        },\n        'DATA_ENTRY': {\n            name: t('roles.DATA_ENTRY'),\n            color: '#fd7e14',\n            icon: '📋'\n        },\n        'MAP_SCHEDULER': {\n            name: t('roles.MAP_SCHEDULER'),\n            color: '#20c997',\n            icon: '🗺️'\n        },\n        'VIEWER': {\n            name: t('roles.VIEWER'),\n            color: '#6c757d',\n            icon: '👁️'\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchUsers();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    // تحديث بيانات التعديل عند اختيار مستخدم\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (editingUser) {\n                setEditUserData({\n                    id: editingUser.id,\n                    username: editingUser.username,\n                    password: '',\n                    name: editingUser.name,\n                    email: editingUser.email || '',\n                    phone: editingUser.phone || '',\n                    role: editingUser.role,\n                    isActive: editingUser.isActive\n                });\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        editingUser\n    ]);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch('/api/users');\n            const result = await response.json();\n            if (result.success) {\n                setUsers(result.users);\n            } else {\n                showErrorToast('serverConnection');\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n            showErrorToast('serverConnection');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddUser = async (e)=>{\n        e.preventDefault();\n        if (!newUser.username || !newUser.password || !newUser.name) {\n            showErrorToast('invalidData');\n            return;\n        }\n        try {\n            const response = await fetch('/api/users', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newUser)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userCreated');\n                setUsers([\n                    ...users,\n                    result.user\n                ]);\n                setShowAddUser(false);\n                setNewUser({\n                    username: '',\n                    password: '',\n                    name: '',\n                    email: '',\n                    phone: '',\n                    role: 'VIEWER'\n                });\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error adding user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (!confirm(t('admin.confirmDelete'))) return;\n        try {\n            const response = await fetch(\"/api/users?id=\".concat(userId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userDeleted');\n                setUsers(users.filter((u)=>u.id !== userId));\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error deleting user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const handleUpdateUser = async (e)=>{\n        e.preventDefault();\n        if (!editUserData.username || !editUserData.name) {\n            showErrorToast('invalidData');\n            return;\n        }\n        try {\n            // إذا كانت كلمة المرور فارغة، لا نرسلها للتحديث\n            const userData = {\n                ...editUserData\n            };\n            if (!userData.password) {\n                delete userData.password;\n            }\n            const response = await fetch(\"/api/users?id=\".concat(userData.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(userData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userUpdated');\n                setEditingUser(null);\n                fetchUsers();\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error updating user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: isRTL ? 'rtl' : 'ltr',\n        outline: 'none'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: [\n                        \"⏳ \",\n                        t('admin.loadingData')\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredRole: \"ADMIN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: t('admin.title'),\n                subtitle: t('admin.subtitle'),\n                icon: \"\\uD83D\\uDC65\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                            gap: '20px',\n                            marginBottom: '20px'\n                        },\n                        children: Object.entries(roles).map((param)=>{\n                            let [roleKey, roleInfo] = param;\n                            const count = users.filter((u)=>u.role === roleKey).length;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    borderRadius: '15px',\n                                    padding: '20px',\n                                    border: '1px solid #6b7280',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '2rem',\n                                            marginBottom: '10px'\n                                        },\n                                        children: roleInfo.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 5px 0',\n                                            fontSize: '1rem'\n                                        },\n                                        children: roleInfo.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '1.8rem',\n                                            fontWeight: 'bold',\n                                            color: roleInfo.color\n                                        },\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#d1d5db',\n                                            fontSize: '0.8rem',\n                                            margin: 0\n                                        },\n                                        children: t('common.user')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, roleKey, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '25px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            fontSize: '1.5rem',\n                                            margin: 0\n                                        },\n                                        children: [\n                                            \"\\uD83D\\uDC65 \",\n                                            t('admin.userManagement'),\n                                            \" (\",\n                                            users.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddUser(true),\n                                        style: {\n                                            background: 'linear-gradient(45deg, #10b981, #059669)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '10px',\n                                            padding: '12px 20px',\n                                            cursor: 'pointer',\n                                            fontSize: '1rem',\n                                            fontWeight: 'bold'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('admin.addNewUser')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this),\n                            showAddUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('admin.addNewUser')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleAddUser,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    gap: '15px',\n                                                    marginBottom: '20px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.username'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t('admin.username'),\n                                                                value: newUser.username,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        username: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.password'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"password\",\n                                                                placeholder: t('admin.password'),\n                                                                value: newUser.password,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        password: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.fullName'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t('admin.fullName'),\n                                                                value: newUser.name,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: t('admin.email')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                placeholder: t('admin.email'),\n                                                                value: newUser.email,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        email: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: t('admin.phone')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"tel\",\n                                                                placeholder: t('admin.phone'),\n                                                                value: newUser.phone,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        phone: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.role'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: newUser.role,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        role: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                children: Object.entries(roles).map((param)=>{\n                                                                    let [key, role] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: key,\n                                                                        children: role.name\n                                                                    }, key, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '10px',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        style: {\n                                                            background: 'linear-gradient(45deg, #10b981, #059669)',\n                                                            color: 'white',\n                                                            border: 'none',\n                                                            borderRadius: '8px',\n                                                            padding: '12px 25px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: [\n                                                            \"✅ \",\n                                                            t('admin.createUser')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>{\n                                                            setShowAddUser(false);\n                                                            setNewUser({\n                                                                username: '',\n                                                                password: '',\n                                                                name: '',\n                                                                email: '',\n                                                                phone: '',\n                                                                role: 'VIEWER'\n                                                            });\n                                                        },\n                                                        style: {\n                                                            background: '#6c757d',\n                                                            color: 'white',\n                                                            border: 'none',\n                                                            borderRadius: '8px',\n                                                            padding: '12px 25px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: [\n                                                            \"❌ \",\n                                                            t('admin.cancel')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    overflowX: 'auto'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse',\n                                        color: '#f3f4f6',\n                                        fontSize: '0.95rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#374151',\n                                                    borderBottom: '2px solid #4b5563'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('common.user')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('admin.role')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('admin.lastLogin')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: t('admin.actions')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: users.map((user)=>{\n                                                var _roles_user_role, _roles_user_role1, _roles_user_role2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    style: {\n                                                        borderBottom: '1px solid #4b5563',\n                                                        background: '#2d3748'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    gap: '10px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            width: '40px',\n                                                                            height: '40px',\n                                                                            borderRadius: '50%',\n                                                                            background: ((_roles_user_role = roles[user.role]) === null || _roles_user_role === void 0 ? void 0 : _roles_user_role.color) || '#6c757d',\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            justifyContent: 'center',\n                                                                            fontSize: '1.2rem'\n                                                                        },\n                                                                        children: ((_roles_user_role1 = roles[user.role]) === null || _roles_user_role1 === void 0 ? void 0 : _roles_user_role1.icon) || '👤'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold'\n                                                                                },\n                                                                                children: user.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                                lineNumber: 540,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.85rem',\n                                                                                    color: '#9ca3af'\n                                                                                },\n                                                                                children: [\n                                                                                    \"@\",\n                                                                                    user.username\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: 'inline-block',\n                                                                        padding: '5px 10px',\n                                                                        borderRadius: '20px',\n                                                                        background: ((_roles_user_role2 = roles[user.role]) === null || _roles_user_role2 === void 0 ? void 0 : _roles_user_role2.color) || '#6c757d',\n                                                                        color: 'white',\n                                                                        fontSize: '0.85rem',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: getRoleName(user.role)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '0.8rem',\n                                                                        color: '#9ca3af',\n                                                                        marginTop: '5px'\n                                                                    },\n                                                                    children: tRoleDesc(user.role).substring(0, 50) + (tRoleDesc(user.role).length > 50 ? '...' : '')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: user.lastLogin ? new Date(user.lastLogin).toLocaleString(isRTL ? 'ar-SA' : 'en-US') : t('admin.noLoginYet')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px',\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    gap: '10px',\n                                                                    justifyContent: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            // تحرير المستخدم\n                                                                            setEditingUser(user);\n                                                                        },\n                                                                        style: {\n                                                                            background: '#3b82f6',\n                                                                            color: 'white',\n                                                                            border: 'none',\n                                                                            borderRadius: '5px',\n                                                                            padding: '8px 12px',\n                                                                            cursor: 'pointer'\n                                                                        },\n                                                                        children: \"✏️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteUser(user.id),\n                                                                        disabled: user.role === 'ADMIN' && user.id === '1',\n                                                                        style: {\n                                                                            background: user.role === 'ADMIN' && user.id === '1' ? '#6c757d' : '#ef4444',\n                                                                            color: 'white',\n                                                                            border: 'none',\n                                                                            borderRadius: '5px',\n                                                                            padding: '8px 12px',\n                                                                            cursor: user.role === 'ADMIN' && user.id === '1' ? 'not-allowed' : 'pointer',\n                                                                            opacity: user.role === 'ADMIN' && user.id === '1' ? 0.5 : 1\n                                                                        },\n                                                                        children: \"\\uD83D\\uDDD1️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, user.id, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 11\n                            }, this),\n                            users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '30px',\n                                    color: '#9ca3af'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '10px'\n                                        },\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: t('admin.noUsers')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: t('admin.addUsersMessage')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            border: '1px solid #6b7280',\n                            marginTop: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    fontSize: '1.5rem',\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDD11 \",\n                                    t('admin.rolesExplanation')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                                    gap: '20px'\n                                },\n                                children: Object.entries(roles).map((param)=>{\n                                    let [key, role] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: '#2d3748',\n                                            borderRadius: '10px',\n                                            padding: '20px',\n                                            border: '1px solid #4b5563'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '10px',\n                                                    marginBottom: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: '40px',\n                                                            height: '40px',\n                                                            borderRadius: '50%',\n                                                            background: role.color,\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '1.5rem'\n                                                        },\n                                                        children: role.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontWeight: 'bold',\n                                                            fontSize: '1.2rem',\n                                                            color: '#f3f4f6'\n                                                        },\n                                                        children: getRoleName(key)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '0.9rem',\n                                                    color: '#d1d5db',\n                                                    marginBottom: '15px',\n                                                    lineHeight: '1.5'\n                                                },\n                                                children: tRoleDesc(key)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: '#1f2937',\n                                                    borderRadius: '8px',\n                                                    padding: '10px',\n                                                    fontSize: '0.85rem',\n                                                    color: '#9ca3af'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '5px',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            t('admin.permissions'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            flexWrap: 'wrap',\n                                                            gap: '5px'\n                                                        },\n                                                        children: [\n                                                            key === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    background: '#dc3545',\n                                                                    color: 'white',\n                                                                    padding: '3px 8px',\n                                                                    borderRadius: '5px',\n                                                                    fontSize: '0.75rem'\n                                                                },\n                                                                children: \"جميع الصلاحيات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            key === 'CONTENT_MANAGER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#28a745',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'MEDIA_MANAGER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#28a745',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'SCHEDULER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'FULL_VIEWER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الخريطة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض البث\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'DATA_ENTRY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    background: '#28a745',\n                                                                    color: 'white',\n                                                                    padding: '3px 8px',\n                                                                    borderRadius: '5px',\n                                                                    fontSize: '0.75rem'\n                                                                },\n                                                                children: \"إدارة المواد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            key === 'MAP_SCHEDULER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#20c997',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الخريطة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'VIEWER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 9\n                    }, this),\n                    editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'fixed',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0, 0, 0, 0.8)',\n                            display: 'flex',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            zIndex: 1000,\n                            padding: '20px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '15px',\n                                padding: '30px',\n                                width: '100%',\n                                maxWidth: '600px',\n                                maxHeight: '90vh',\n                                overflowY: 'auto',\n                                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n                                position: 'relative'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setEditingUser(null),\n                                    style: {\n                                        position: 'absolute',\n                                        top: '15px',\n                                        left: '15px',\n                                        background: 'none',\n                                        border: 'none',\n                                        color: '#f3f4f6',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"✖️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        textAlign: 'center',\n                                        marginBottom: '25px',\n                                        fontSize: '1.5rem'\n                                    },\n                                    children: [\n                                        \"✏️ \",\n                                        t('admin.editingUser'),\n                                        \": \",\n                                        editingUser.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleUpdateUser,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'grid',\n                                                gridTemplateColumns: '1fr 1fr',\n                                                gap: '20px',\n                                                marginBottom: '20px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.username'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editUserData.username,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    username: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.password'),\n                                                                \" \",\n                                                                t('admin.passwordNote')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            value: editUserData.password,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    password: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.fullName'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editUserData.name,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    name: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 970,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: t('admin.email')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: editUserData.email,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    email: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: t('admin.phone')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1004,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: editUserData.phone,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    phone: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.role'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: editUserData.role,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    role: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true,\n                                                            children: Object.entries(roles).map((param)=>{\n                                                                let [key, role] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: role.name\n                                                                }, key, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1034,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1023,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1019,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.status'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: editUserData.isActive.toString(),\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    isActive: e.target.value === 'true'\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"true\",\n                                                                    children: t('admin.active')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1055,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"false\",\n                                                                    children: t('admin.inactive')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1056,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1045,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                justifyContent: 'center',\n                                                gap: '15px',\n                                                marginTop: '30px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    style: {\n                                                        background: '#3b82f6',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: [\n                                                        \"\\uD83D\\uDCBE \",\n                                                        t('admin.saveChanges')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setEditingUser(null),\n                                                    style: {\n                                                        background: '#6c757d',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: [\n                                                        \"❌ \",\n                                                        t('common.cancel')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1083,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 894,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 881,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 1105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"1Bw1/P3KxhWkAL6qJFk6Df16T+A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__.useAppTranslation\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/page.tsx\n"));

/***/ })

});