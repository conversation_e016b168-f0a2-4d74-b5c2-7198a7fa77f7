"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/media-list/page",{

/***/ "(app-pages-browser)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MediaListPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MediaListPage() {\n    _s();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast)();\n    const [mediaItems, setMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [codeSearchTerm, setCodeSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScrollToTop, setShowScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            fetchMediaItems();\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    // مراقبة التمرير لإظهار/إخفاء زر العودة لأعلى\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"MediaListPage.useEffect.handleScroll\": ()=>{\n                    setShowScrollToTop(window.scrollY > 300);\n                }\n            }[\"MediaListPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"MediaListPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"MediaListPage.useEffect\"];\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            filterAndSortItems();\n        }\n    }[\"MediaListPage.useEffect\"], [\n        mediaItems,\n        searchTerm,\n        codeSearchTerm,\n        selectedType,\n        selectedStatus,\n        sortBy\n    ]);\n    // دالة العودة لأعلى الصفحة\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    const fetchMediaItems = async ()=>{\n        try {\n            const response = await fetch('/api/media');\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(result.data);\n            } else {\n                setError(result.error);\n            }\n        } catch (error) {\n            console.error('Error fetching media items:', error);\n            setError(t('messages.networkError'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterAndSortItems = ()=>{\n        let filtered = [\n            ...mediaItems\n        ];\n        // البحث بالاسم\n        if (searchTerm) {\n            filtered = filtered.filter((item)=>item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // البحث بالكود (في السيجمانت)\n        if (codeSearchTerm) {\n            filtered = filtered.filter((item)=>{\n                // البحث في معرف المادة\n                if (item.id.toLowerCase().includes(codeSearchTerm.toLowerCase())) {\n                    return true;\n                }\n                // البحث في أكواد السيجمانت\n                if (item.segments && item.segments.length > 0) {\n                    return item.segments.some((segment)=>segment.code && segment.code.toLowerCase().includes(codeSearchTerm.toLowerCase()));\n                }\n                return false;\n            });\n        }\n        // فلترة بالنوع\n        if (selectedType !== 'ALL') {\n            filtered = filtered.filter((item)=>item.type === selectedType);\n        }\n        // فلترة بالحالة\n        if (selectedStatus !== 'ALL') {\n            filtered = filtered.filter((item)=>item.status === selectedStatus);\n        }\n        // الترتيب\n        switch(sortBy){\n            case 'newest':\n                filtered.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                break;\n            case 'oldest':\n                filtered.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n                break;\n            case 'name':\n                filtered.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n                break;\n            case 'type':\n                filtered.sort((a, b)=>a.type.localeCompare(b.type));\n                break;\n        }\n        setFilteredItems(filtered);\n    };\n    const deleteMediaItem = async (id)=>{\n        if (!confirm(t('messages.confirmDelete'))) return;\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = \"token_\".concat(user.id || 'unknown', \"_\").concat(user.role || 'unknown');\n            console.log('Sending delete request with token:', tokenWithRole);\n            const response = await fetch(\"/api/media?id=\".concat(id), {\n                method: 'DELETE',\n                headers: {\n                    'Authorization': \"Bearer \".concat(tokenWithRole)\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(mediaItems.filter((item)=>item.id !== id));\n                showSuccessToast('mediaDeleted');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error deleting media item:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const exportToExcel = async ()=>{\n        setIsExporting(true);\n        try {\n            console.log('🚀 بدء تصدير قاعدة البيانات...');\n            // إرسال الفلاتر الحالية مع طلب التصدير\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (codeSearchTerm) params.append('codeSearch', codeSearchTerm);\n            if (selectedType !== 'ALL') params.append('type', selectedType);\n            if (selectedStatus !== 'ALL') params.append('status', selectedStatus);\n            const apiUrl = \"/api/export-unified\".concat(params.toString() ? '?' + params.toString() : '');\n            console.log('📊 تصدير مع الفلاتر:', apiUrl);\n            const response = await fetch(apiUrl);\n            if (!response.ok) {\n                throw new Error(t('messages.exportError'));\n            }\n            // الحصول على الملف كـ blob\n            const blob = await response.blob();\n            // إنشاء رابط التحميل\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = downloadUrl;\n            // تحديد اسم الملف\n            const fileName = \"Media_Database_\".concat(new Date().toISOString().split('T')[0], \".xlsx\");\n            link.download = fileName;\n            // تحميل الملف\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            // تنظيف الذاكرة\n            window.URL.revokeObjectURL(downloadUrl);\n            console.log('✅ تم تصدير قاعدة البيانات بنجاح');\n            showSuccessToast('exportSuccess');\n        } catch (error) {\n            console.error('❌ خطأ في التصدير:', error);\n            showErrorToast('exportFailed');\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const getTypeLabel = (type)=>{\n        return t(\"mediaTypes.\".concat(type)) || type;\n    };\n    const getStatusLabel = (status)=>{\n        return t(\"mediaStatus.\".concat(status)) || status;\n    };\n    const getChannelLabel = (channel)=>{\n        return t(\"channels.\".concat(channel)) || channel;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"⏳ \",\n                    t('common.loading')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"❌ \",\n                    t('common.error'),\n                    \": \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'MEDIA_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: t('media.list'),\n            subtitle: t('media.title'),\n            icon: \"\\uD83C\\uDFAC\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"media-header-card\",\n                    style: {\n                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                        padding: 'clamp(15px, 3vw, 20px)',\n                        borderRadius: '12px',\n                        marginBottom: 'clamp(20px, 4vw, 25px)',\n                        color: 'white',\n                        textAlign: 'center',\n                        boxShadow: '0 6px 20px rgba(102, 126, 234, 0.3)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: '0 0 10px 0',\n                                fontSize: 'clamp(1.1rem, 3vw, 1.3rem)'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCCA \",\n                                t('media.list')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: '0 0 8px 0',\n                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                opacity: 0.9\n                            },\n                            children: t('media.mediaOverview')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: '0',\n                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)',\n                                opacity: 0.8\n                            },\n                            children: [\n                                \"✨ \",\n                                t('media.searchFilterExport')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"action-buttons\",\n                    style: {\n                        display: 'flex',\n                        gap: 'clamp(10px, 2vw, 15px)',\n                        justifyContent: 'center',\n                        marginBottom: 'clamp(20px, 4vw, 25px)',\n                        flexWrap: 'wrap'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: exportToExcel,\n                        disabled: isExporting,\n                        style: {\n                            background: isExporting ? 'linear-gradient(45deg, #6c757d, #5a6268)' : 'linear-gradient(45deg, #17a2b8, #138496)',\n                            color: 'white',\n                            padding: 'clamp(10px, 2vw, 12px) clamp(20px, 4vw, 25px)',\n                            borderRadius: '25px',\n                            border: 'none',\n                            fontWeight: 'bold',\n                            cursor: isExporting ? 'not-allowed' : 'pointer',\n                            boxShadow: '0 4px 15px rgba(23,162,184,0.3)',\n                            fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                            minHeight: '44px',\n                            transition: 'all 0.2s ease'\n                        },\n                        children: isExporting ? '⏳ ' + t('media.exporting') : '📊 ' + t('media.exportExcel')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"search-filter-section\",\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: 'clamp(20px, 4vw, 25px)',\n                        marginBottom: 'clamp(20px, 4vw, 25px)',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: '#f3f4f6',\n                                marginBottom: 'clamp(15px, 3vw, 20px)',\n                                fontSize: 'clamp(1.1rem, 3vw, 1.3rem)'\n                            },\n                            children: [\n                                \"\\uD83D\\uDD0D \",\n                                t('media.searchAndFilter')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"search-grid\",\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(min(250px, 100%), 1fr))',\n                                gap: 'clamp(12px, 2.5vw, 15px)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)'\n                                            },\n                                            children: [\n                                                t('media.searchByName'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('media.searchPlaceholder'),\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: 'clamp(8px, 2vw, 10px)',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937',\n                                                minHeight: '40px',\n                                                boxSizing: 'border-box'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)'\n                                            },\n                                            children: [\n                                                t('media.searchByCode'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('media.codePlaceholder'),\n                                            value: codeSearchTerm,\n                                            onChange: (e)=>setCodeSearchTerm(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: 'clamp(8px, 2vw, 10px)',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                                direction: 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937',\n                                                minHeight: '40px',\n                                                boxSizing: 'border-box'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.mediaType'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedType,\n                                            onChange: (e)=>setSelectedType(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ALL\",\n                                                    children: t('mediaTypes.ALL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"FILM\",\n                                                    children: t('mediaTypes.FILM')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SERIES\",\n                                                    children: t('mediaTypes.SERIES')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"PROGRAM\",\n                                                    children: t('mediaTypes.PROGRAM')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SONG\",\n                                                    children: t('mediaTypes.SONG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"FILLER\",\n                                                    children: t('mediaTypes.FILLER')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"STING\",\n                                                    children: t('mediaTypes.STING')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"PROMO\",\n                                                    children: t('mediaTypes.PROMO')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NEXT\",\n                                                    children: t('mediaTypes.NEXT')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NOW\",\n                                                    children: t('mediaTypes.NOW')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"سنعود\",\n                                                    children: t('mediaTypes.سنعود')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"عدنا\",\n                                                    children: t('mediaTypes.عدنا')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MINI\",\n                                                    children: t('mediaTypes.MINI')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CROSS\",\n                                                    children: t('mediaTypes.CROSS')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.mediaStatus'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedStatus,\n                                            onChange: (e)=>setSelectedStatus(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ALL\",\n                                                    children: t('mediaStatus.ALL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"VALID\",\n                                                    children: t('mediaStatus.VALID')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"REJECTED_CENSORSHIP\",\n                                                    children: t('mediaStatus.REJECTED_CENSORSHIP')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"REJECTED_TECHNICAL\",\n                                                    children: t('mediaStatus.REJECTED_TECHNICAL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"EXPIRED\",\n                                                    children: t('mediaStatus.EXPIRED')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"HOLD\",\n                                                    children: t('mediaStatus.HOLD')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.sortBy'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 467,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"newest\",\n                                                    children: t('media.newest')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"oldest\",\n                                                    children: t('media.oldest')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: t('media.byName')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"type\",\n                                                    children: t('media.byType')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '15px',\n                                padding: '10px',\n                                background: '#1f2937',\n                                borderRadius: '8px',\n                                textAlign: 'center',\n                                color: '#d1d5db',\n                                border: '1px solid #6b7280'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCCA \",\n                                t('media.searchStats', {\n                                    filtered: filteredItems.length,\n                                    total: mediaItems.length\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this),\n                filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: '50px',\n                        textAlign: 'center',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: '#d1d5db',\n                                fontSize: '1.5rem'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCED \",\n                                t('media.noMediaFound')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: '#a0aec0',\n                                marginTop: '10px'\n                            },\n                            children: t('media.startAdding')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 508,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gap: '20px'\n                    },\n                    children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#4a5568',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: '1fr auto',\n                                    gap: '20px',\n                                    alignItems: 'start'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    marginBottom: '15px',\n                                                    fontSize: '1.4rem'\n                                                },\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                    gap: '15px',\n                                                    marginBottom: '15px',\n                                                    color: '#d1d5db'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('common.type'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getTypeLabel(item.type)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('media.channel'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 542,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getChannelLabel(item.channel)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('common.status'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getStatusLabel(item.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('media.segmentCount'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            item.segments.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    marginBottom: '10px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t('media.description'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \" \",\n                                                    item.description\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.segments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '15px',\n                                                    color: '#d1d5db'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t('media.segments'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'grid',\n                                                            gap: '8px',\n                                                            marginTop: '8px'\n                                                        },\n                                                        children: item.segments.map((segment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: '#1f2937',\n                                                                    padding: '8px 12px',\n                                                                    borderRadius: '8px',\n                                                                    fontSize: '0.9rem',\n                                                                    color: '#d1d5db',\n                                                                    border: '1px solid #6b7280'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            \"#\",\n                                                                            segment.segmentNumber\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    \" -\",\n                                                                    segment.code && segment.code.trim() !== '' ? \" \".concat(segment.code, \" - \") : \" [\".concat(t('media.noCode'), \"] - \"),\n                                                                    segment.timeIn,\n                                                                    \" → \",\n                                                                    segment.timeOut,\n                                                                    \" (\",\n                                                                    segment.duration,\n                                                                    \")\"\n                                                                ]\n                                                            }, \"\".concat(item.id, \"_segment_\").concat(index), true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 563,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            flexDirection: 'column',\n                                            gap: '10px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    // توجيه لصفحة التعديل مع معرف المادة\n                                                    window.location.href = \"/edit-media?id=\".concat(item.id);\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem',\n                                                    marginBottom: '5px'\n                                                },\n                                                children: [\n                                                    \"✏️ \",\n                                                    t('media.edit')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>deleteMediaItem(item.id),\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDDD1️ \",\n                                                    t('media.delete')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 531,\n                                columnNumber: 17\n                            }, this)\n                        }, item.id, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 525,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 523,\n                    columnNumber: 11\n                }, this),\n                showScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: scrollToTop,\n                    style: {\n                        position: 'fixed',\n                        bottom: '30px',\n                        right: '30px',\n                        width: '60px',\n                        height: '60px',\n                        borderRadius: '50%',\n                        background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                        color: 'white',\n                        border: 'none',\n                        cursor: 'pointer',\n                        fontSize: '24px',\n                        boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',\n                        zIndex: 1000,\n                        transition: 'all 0.3s ease',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                    },\n                    onMouseEnter: (e)=>{\n                        e.currentTarget.style.transform = 'scale(1.1)';\n                        e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 123, 255, 0.4)';\n                    },\n                    onMouseLeave: (e)=>{\n                        e.currentTarget.style.transform = 'scale(1)';\n                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 123, 255, 0.3)';\n                    },\n                    title: t('media.scrollToTop'),\n                    children: \"⬆️\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 625,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 659,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_s(MediaListPage, \"w2EGwJS4qHoiqz054AuihRD/zr8=\", false, function() {\n    return [\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast\n    ];\n});\n_c = MediaListPage;\nvar _c;\n$RefreshReg$(_c, \"MediaListPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/media-list/page.tsx\n"));

/***/ })

});