"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin-dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin-dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast)();\n    const { t, tRole, tRoleDesc, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__.useAppTranslation)();\n    // دالة ترجمة أسماء الأدوار\n    const getRoleName = (role)=>{\n        const names = {\n            'ADMIN': 'مدير النظام',\n            'CONTENT_MANAGER': 'مدير المحتوى',\n            'MEDIA_MANAGER': 'مدير قاعدة البيانات',\n            'SCHEDULER': 'مجدول البرامج',\n            'FULL_VIEWER': 'مستخدم رؤية كاملة',\n            'DATA_ENTRY': 'مدخل بيانات',\n            'MAP_SCHEDULER': 'مسؤول الخريطة والجدول',\n            'VIEWER': 'مستخدم عرض'\n        };\n        return names[role] || role;\n    };\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddUser, setShowAddUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editUserData, setEditUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '',\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        phone: '',\n        role: '',\n        isActive: true\n    });\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        phone: '',\n        role: 'VIEWER'\n    });\n    const roles = {\n        'ADMIN': {\n            name: t('roles.ADMIN'),\n            color: '#dc3545',\n            icon: '👑'\n        },\n        'CONTENT_MANAGER': {\n            name: t('roles.CONTENT_MANAGER'),\n            color: '#6f42c1',\n            icon: '📊'\n        },\n        'MEDIA_MANAGER': {\n            name: t('roles.MEDIA_MANAGER'),\n            color: '#28a745',\n            icon: '📝'\n        },\n        'SCHEDULER': {\n            name: t('roles.SCHEDULER'),\n            color: '#007bff',\n            icon: '📅'\n        },\n        'FULL_VIEWER': {\n            name: t('roles.FULL_VIEWER'),\n            color: '#17a2b8',\n            icon: '👓'\n        },\n        'DATA_ENTRY': {\n            name: t('roles.DATA_ENTRY'),\n            color: '#fd7e14',\n            icon: '📋'\n        },\n        'MAP_SCHEDULER': {\n            name: t('roles.MAP_SCHEDULER'),\n            color: '#20c997',\n            icon: '🗺️'\n        },\n        'VIEWER': {\n            name: t('roles.VIEWER'),\n            color: '#6c757d',\n            icon: '👁️'\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchUsers();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    // تحديث بيانات التعديل عند اختيار مستخدم\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (editingUser) {\n                setEditUserData({\n                    id: editingUser.id,\n                    username: editingUser.username,\n                    password: '',\n                    name: editingUser.name,\n                    email: editingUser.email || '',\n                    phone: editingUser.phone || '',\n                    role: editingUser.role,\n                    isActive: editingUser.isActive\n                });\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        editingUser\n    ]);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch('/api/users');\n            const result = await response.json();\n            if (result.success) {\n                setUsers(result.users);\n            } else {\n                showErrorToast('serverConnection');\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n            showErrorToast('serverConnection');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddUser = async (e)=>{\n        e.preventDefault();\n        if (!newUser.username || !newUser.password || !newUser.name) {\n            showErrorToast('invalidData');\n            return;\n        }\n        try {\n            const response = await fetch('/api/users', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newUser)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userCreated');\n                setUsers([\n                    ...users,\n                    result.user\n                ]);\n                setShowAddUser(false);\n                setNewUser({\n                    username: '',\n                    password: '',\n                    name: '',\n                    email: '',\n                    phone: '',\n                    role: 'VIEWER'\n                });\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error adding user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (!confirm(t('admin.confirmDelete'))) return;\n        try {\n            const response = await fetch(\"/api/users?id=\".concat(userId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userDeleted');\n                setUsers(users.filter((u)=>u.id !== userId));\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error deleting user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const handleUpdateUser = async (e)=>{\n        e.preventDefault();\n        if (!editUserData.username || !editUserData.name) {\n            showErrorToast('invalidData');\n            return;\n        }\n        try {\n            // إذا كانت كلمة المرور فارغة، لا نرسلها للتحديث\n            const userData = {\n                ...editUserData\n            };\n            if (!userData.password) {\n                delete userData.password;\n            }\n            const response = await fetch(\"/api/users?id=\".concat(userData.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(userData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userUpdated');\n                setEditingUser(null);\n                fetchUsers();\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error updating user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: isRTL ? 'rtl' : 'ltr',\n        outline: 'none'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: [\n                        \"⏳ \",\n                        t('admin.loadingData')\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredRole: \"ADMIN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: t('admin.title'),\n                subtitle: t('admin.subtitle'),\n                icon: \"\\uD83D\\uDC65\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                            gap: '20px',\n                            marginBottom: '20px'\n                        },\n                        children: Object.entries(roles).map((param)=>{\n                            let [roleKey, roleInfo] = param;\n                            const count = users.filter((u)=>u.role === roleKey).length;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    borderRadius: '15px',\n                                    padding: '20px',\n                                    border: '1px solid #6b7280',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '2rem',\n                                            marginBottom: '10px'\n                                        },\n                                        children: roleInfo.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 5px 0',\n                                            fontSize: '1rem'\n                                        },\n                                        children: roleInfo.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '1.8rem',\n                                            fontWeight: 'bold',\n                                            color: roleInfo.color\n                                        },\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#d1d5db',\n                                            fontSize: '0.8rem',\n                                            margin: 0\n                                        },\n                                        children: t('common.user')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, roleKey, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '25px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            fontSize: '1.5rem',\n                                            margin: 0\n                                        },\n                                        children: [\n                                            \"\\uD83D\\uDC65 \",\n                                            t('admin.userManagement'),\n                                            \" (\",\n                                            users.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddUser(true),\n                                        style: {\n                                            background: 'linear-gradient(45deg, #10b981, #059669)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '10px',\n                                            padding: '12px 20px',\n                                            cursor: 'pointer',\n                                            fontSize: '1rem',\n                                            fontWeight: 'bold'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('admin.addNewUser')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this),\n                            showAddUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('admin.addNewUser')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleAddUser,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    gap: '15px',\n                                                    marginBottom: '20px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.username'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t('admin.username'),\n                                                                value: newUser.username,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        username: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.password'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"password\",\n                                                                placeholder: t('admin.password'),\n                                                                value: newUser.password,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        password: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.fullName'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t('admin.fullName'),\n                                                                value: newUser.name,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: t('admin.email')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                placeholder: t('admin.email'),\n                                                                value: newUser.email,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        email: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: t('admin.phone')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"tel\",\n                                                                placeholder: t('admin.phone'),\n                                                                value: newUser.phone,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        phone: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.role'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: newUser.role,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        role: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                children: Object.entries(roles).map((param)=>{\n                                                                    let [key, role] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: key,\n                                                                        children: role.name\n                                                                    }, key, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '10px',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        style: {\n                                                            background: 'linear-gradient(45deg, #10b981, #059669)',\n                                                            color: 'white',\n                                                            border: 'none',\n                                                            borderRadius: '8px',\n                                                            padding: '12px 25px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: [\n                                                            \"✅ \",\n                                                            t('admin.createUser')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>{\n                                                            setShowAddUser(false);\n                                                            setNewUser({\n                                                                username: '',\n                                                                password: '',\n                                                                name: '',\n                                                                email: '',\n                                                                phone: '',\n                                                                role: 'VIEWER'\n                                                            });\n                                                        },\n                                                        style: {\n                                                            background: '#6c757d',\n                                                            color: 'white',\n                                                            border: 'none',\n                                                            borderRadius: '8px',\n                                                            padding: '12px 25px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: [\n                                                            \"❌ \",\n                                                            t('admin.cancel')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    overflowX: 'auto'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse',\n                                        color: '#f3f4f6',\n                                        fontSize: '0.95rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#374151',\n                                                    borderBottom: '2px solid #4b5563'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('common.user')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('admin.role')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('admin.lastLogin')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: t('admin.actions')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: users.map((user)=>{\n                                                var _roles_user_role, _roles_user_role1, _roles_user_role2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    style: {\n                                                        borderBottom: '1px solid #4b5563',\n                                                        background: '#2d3748'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    gap: '10px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            width: '40px',\n                                                                            height: '40px',\n                                                                            borderRadius: '50%',\n                                                                            background: ((_roles_user_role = roles[user.role]) === null || _roles_user_role === void 0 ? void 0 : _roles_user_role.color) || '#6c757d',\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            justifyContent: 'center',\n                                                                            fontSize: '1.2rem'\n                                                                        },\n                                                                        children: ((_roles_user_role1 = roles[user.role]) === null || _roles_user_role1 === void 0 ? void 0 : _roles_user_role1.icon) || '👤'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold'\n                                                                                },\n                                                                                children: user.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                                lineNumber: 540,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.85rem',\n                                                                                    color: '#9ca3af'\n                                                                                },\n                                                                                children: [\n                                                                                    \"@\",\n                                                                                    user.username\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: 'inline-block',\n                                                                        padding: '5px 10px',\n                                                                        borderRadius: '20px',\n                                                                        background: ((_roles_user_role2 = roles[user.role]) === null || _roles_user_role2 === void 0 ? void 0 : _roles_user_role2.color) || '#6c757d',\n                                                                        color: 'white',\n                                                                        fontSize: '0.85rem',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: getRoleName(user.role)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '0.8rem',\n                                                                        color: '#9ca3af',\n                                                                        marginTop: '5px'\n                                                                    },\n                                                                    children: tRoleDesc(user.role).substring(0, 50) + (tRoleDesc(user.role).length > 50 ? '...' : '')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: user.lastLogin ? new Date(user.lastLogin).toLocaleString(isRTL ? 'ar-SA' : 'en-US') : t('admin.noLoginYet')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px',\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    gap: '10px',\n                                                                    justifyContent: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            // تحرير المستخدم\n                                                                            setEditingUser(user);\n                                                                        },\n                                                                        style: {\n                                                                            background: '#3b82f6',\n                                                                            color: 'white',\n                                                                            border: 'none',\n                                                                            borderRadius: '5px',\n                                                                            padding: '8px 12px',\n                                                                            cursor: 'pointer'\n                                                                        },\n                                                                        children: \"✏️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteUser(user.id),\n                                                                        disabled: user.role === 'ADMIN' && user.id === '1',\n                                                                        style: {\n                                                                            background: user.role === 'ADMIN' && user.id === '1' ? '#6c757d' : '#ef4444',\n                                                                            color: 'white',\n                                                                            border: 'none',\n                                                                            borderRadius: '5px',\n                                                                            padding: '8px 12px',\n                                                                            cursor: user.role === 'ADMIN' && user.id === '1' ? 'not-allowed' : 'pointer',\n                                                                            opacity: user.role === 'ADMIN' && user.id === '1' ? 0.5 : 1\n                                                                        },\n                                                                        children: \"\\uD83D\\uDDD1️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, user.id, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 11\n                            }, this),\n                            users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '30px',\n                                    color: '#9ca3af'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '10px'\n                                        },\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: t('admin.noUsers')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: t('admin.addUsersMessage')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            border: '1px solid #6b7280',\n                            marginTop: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    fontSize: '1.5rem',\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDD11 \",\n                                    t('admin.rolesExplanation')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                                    gap: '20px'\n                                },\n                                children: Object.entries(roles).map((param)=>{\n                                    let [key, role] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: '#2d3748',\n                                            borderRadius: '10px',\n                                            padding: '20px',\n                                            border: '1px solid #4b5563'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '10px',\n                                                    marginBottom: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: '40px',\n                                                            height: '40px',\n                                                            borderRadius: '50%',\n                                                            background: role.color,\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '1.5rem'\n                                                        },\n                                                        children: role.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontWeight: 'bold',\n                                                            fontSize: '1.2rem',\n                                                            color: '#f3f4f6'\n                                                        },\n                                                        children: getRoleName(key)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '0.9rem',\n                                                    color: '#d1d5db',\n                                                    marginBottom: '15px',\n                                                    lineHeight: '1.5'\n                                                },\n                                                children: tRoleDesc(key)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: '#1f2937',\n                                                    borderRadius: '8px',\n                                                    padding: '10px',\n                                                    fontSize: '0.85rem',\n                                                    color: '#9ca3af'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '5px',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            t('admin.permissions'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            flexWrap: 'wrap',\n                                                            gap: '5px'\n                                                        },\n                                                        children: [\n                                                            key === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    background: '#dc3545',\n                                                                    color: 'white',\n                                                                    padding: '3px 8px',\n                                                                    borderRadius: '5px',\n                                                                    fontSize: '0.75rem'\n                                                                },\n                                                                children: t('admin.allPermissions')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            key === 'CONTENT_MANAGER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#28a745',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: t('admin.mediaManagement')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: t('admin.scheduleManagement')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'MEDIA_MANAGER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#28a745',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: t('admin.viewSchedules')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'SCHEDULER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'FULL_VIEWER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الخريطة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض البث\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'DATA_ENTRY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    background: '#28a745',\n                                                                    color: 'white',\n                                                                    padding: '3px 8px',\n                                                                    borderRadius: '5px',\n                                                                    fontSize: '0.75rem'\n                                                                },\n                                                                children: \"إدارة المواد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            key === 'MAP_SCHEDULER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#20c997',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الخريطة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'VIEWER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 9\n                    }, this),\n                    editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'fixed',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0, 0, 0, 0.8)',\n                            display: 'flex',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            zIndex: 1000,\n                            padding: '20px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '15px',\n                                padding: '30px',\n                                width: '100%',\n                                maxWidth: '600px',\n                                maxHeight: '90vh',\n                                overflowY: 'auto',\n                                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n                                position: 'relative'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setEditingUser(null),\n                                    style: {\n                                        position: 'absolute',\n                                        top: '15px',\n                                        left: '15px',\n                                        background: 'none',\n                                        border: 'none',\n                                        color: '#f3f4f6',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"✖️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        textAlign: 'center',\n                                        marginBottom: '25px',\n                                        fontSize: '1.5rem'\n                                    },\n                                    children: [\n                                        \"✏️ \",\n                                        t('admin.editingUser'),\n                                        \": \",\n                                        editingUser.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleUpdateUser,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'grid',\n                                                gridTemplateColumns: '1fr 1fr',\n                                                gap: '20px',\n                                                marginBottom: '20px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.username'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editUserData.username,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    username: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.password'),\n                                                                \" \",\n                                                                t('admin.passwordNote')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            value: editUserData.password,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    password: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.fullName'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editUserData.name,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    name: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 970,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: t('admin.email')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: editUserData.email,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    email: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: t('admin.phone')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1004,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: editUserData.phone,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    phone: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.role'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: editUserData.role,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    role: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true,\n                                                            children: Object.entries(roles).map((param)=>{\n                                                                let [key, role] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: role.name\n                                                                }, key, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1034,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1023,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1019,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.status'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: editUserData.isActive.toString(),\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    isActive: e.target.value === 'true'\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"true\",\n                                                                    children: t('admin.active')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1055,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"false\",\n                                                                    children: t('admin.inactive')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1056,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1045,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                justifyContent: 'center',\n                                                gap: '15px',\n                                                marginTop: '30px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    style: {\n                                                        background: '#3b82f6',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: [\n                                                        \"\\uD83D\\uDCBE \",\n                                                        t('admin.saveChanges')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setEditingUser(null),\n                                                    style: {\n                                                        background: '#6c757d',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: [\n                                                        \"❌ \",\n                                                        t('common.cancel')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1083,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 894,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 881,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 1105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"1Bw1/P3KxhWkAL6qJFk6Df16T+A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__.useAppTranslation\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/page.tsx\n"));

/***/ })

});