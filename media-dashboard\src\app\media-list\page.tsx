'use client';

import { useState, useEffect } from 'react';
import { AuthGuard } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useAppTranslation } from '@/hooks/useAppTranslation';
import { useTranslatedToast } from '@/hooks/useTranslatedToast';

interface Segment {
  id: string;
  segmentNumber: number;
  timeIn: string;
  timeOut: string;
  duration: string;
  code: string | null;
}

interface MediaItem {
  id: string;
  name: string;
  type: string;
  description: string | null;
  channel: string;
  source: string | null;
  status: string;
  startDate: string;
  endDate: string | null;
  notes: string | null;
  episodeNumber: number | null;
  seasonNumber: number | null;
  partNumber: number | null;
  segments: Segment[];
  createdAt: string;
}

export default function MediaListPage() {
  const { t, tMediaType, isRTL } = useAppTranslation();
  const { showSuccessToast, showErrorToast, ToastContainer } = useTranslatedToast();

  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [codeSearchTerm, setCodeSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('ALL');
  const [selectedStatus, setSelectedStatus] = useState('ALL');
  const [sortBy, setSortBy] = useState('newest');
  const [isExporting, setIsExporting] = useState(false);

  const [showScrollToTop, setShowScrollToTop] = useState(false);

  useEffect(() => {
    fetchMediaItems();
  }, []);

  // مراقبة التمرير لإظهار/إخفاء زر العودة لأعلى
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollToTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    filterAndSortItems();
  }, [mediaItems, searchTerm, codeSearchTerm, selectedType, selectedStatus, sortBy]);

  // دالة العودة لأعلى الصفحة
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const fetchMediaItems = async () => {
    try {
      const response = await fetch('/api/media');
      const result = await response.json();

      if (result.success) {
        setMediaItems(result.data);
      } else {
        setError(result.error);
      }
    } catch (error) {
      console.error('Error fetching media items:', error);
      setError(t('messages.networkError'));
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortItems = () => {
    let filtered = [...mediaItems];

    // البحث بالاسم
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // البحث بالكود (في السيجمانت)
    if (codeSearchTerm) {
      filtered = filtered.filter(item => {
        // البحث في معرف المادة
        if (item.id.toLowerCase().includes(codeSearchTerm.toLowerCase())) {
          return true;
        }

        // البحث في أكواد السيجمانت
        if (item.segments && item.segments.length > 0) {
          return item.segments.some(segment =>
            segment.code && segment.code.toLowerCase().includes(codeSearchTerm.toLowerCase())
          );
        }

        return false;
      });
    }

    // فلترة بالنوع
    if (selectedType !== 'ALL') {
      filtered = filtered.filter(item => item.type === selectedType);
    }

    // فلترة بالحالة
    if (selectedStatus !== 'ALL') {
      filtered = filtered.filter(item => item.status === selectedStatus);
    }

    // الترتيب
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'name':
        filtered.sort((a, b) => a.name.localeCompare(b.name, 'ar'));
        break;
      case 'type':
        filtered.sort((a, b) => a.type.localeCompare(b.type));
        break;
    }

    setFilteredItems(filtered);
  };

  const deleteMediaItem = async (id: string) => {
    if (!confirm(t('messages.confirmDelete'))) return;

    try {
      // تحويل التوكن إلى الصيغة المتوقعة
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const tokenWithRole = `token_${user.id || 'unknown'}_${user.role || 'unknown'}`;
      
      console.log('Sending delete request with token:', tokenWithRole);
      
      const response = await fetch(`/api/media?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${tokenWithRole}`
        }
      });

      const result = await response.json();

      if (result.success) {
        setMediaItems(mediaItems.filter(item => item.id !== id));
        showSuccessToast('mediaDeleted');
      } else {
        showErrorToast('unknownError');
      }
    } catch (error) {
      console.error('Error deleting media item:', error);
      showErrorToast('unknownError');
    }
  };

  const exportToExcel = async () => {
    setIsExporting(true);
    try {
      console.log('🚀 بدء تصدير قاعدة البيانات...');

      // إرسال الفلاتر الحالية مع طلب التصدير
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (codeSearchTerm) params.append('codeSearch', codeSearchTerm);
      if (selectedType !== 'ALL') params.append('type', selectedType);
      if (selectedStatus !== 'ALL') params.append('status', selectedStatus);

      const apiUrl = `/api/export-unified${params.toString() ? '?' + params.toString() : ''}`;
      console.log('📊 تصدير مع الفلاتر:', apiUrl);

      const response = await fetch(apiUrl);

      if (!response.ok) {
        throw new Error(t('messages.exportError'));
      }

      // الحصول على الملف كـ blob
      const blob = await response.blob();

      // إنشاء رابط التحميل
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;

      // تحديد اسم الملف
      const fileName = `Media_Database_${new Date().toISOString().split('T')[0]}.xlsx`;
      link.download = fileName;

      // تحميل الملف
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // تنظيف الذاكرة
      window.URL.revokeObjectURL(downloadUrl);

      console.log('✅ تم تصدير قاعدة البيانات بنجاح');
      showSuccessToast('exportSuccess');

    } catch (error) {
      console.error('❌ خطأ في التصدير:', error);
      showErrorToast('exportFailed');
    } finally {
      setIsExporting(false);
    }
  };



  const getTypeLabel = (type: string) => {
    return t(`mediaTypes.${type}`) || type;
  };

  const getStatusLabel = (status: string) => {
    return t(`mediaStatus.${status}`) || status;
  };

  const getChannelLabel = (channel: string) => {
    return t(`channels.${channel}`) || channel;
  };

  if (loading) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        background: '#1a1d29',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ color: 'white', fontSize: '1.5rem' }}>⏳ {t('common.loading')}</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        minHeight: '100vh', 
        background: '#1a1d29',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{ color: 'white', fontSize: '1.5rem' }}>❌ {t('common.error')}: {error}</div>
      </div>
    );
  }

  return (
    <AuthGuard requiredPermissions={['MEDIA_READ']}>
      <DashboardLayout title={t('media.list')} subtitle={t('media.title')} icon="🎬">
        {/* رسالة توضيحية */}
        <div style={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: '20px',
          borderRadius: '12px',
          marginBottom: '25px',
          color: 'white',
          textAlign: 'center',
          boxShadow: '0 6px 20px rgba(102, 126, 234, 0.3)'
        }}>
          <h3 style={{ margin: '0 0 10px 0', fontSize: '1.3rem' }}>📊 {t('media.list')}</h3>
          <p style={{ margin: '0 0 8px 0', fontSize: '1rem', opacity: 0.9 }}>
            {t('media.mediaOverview')}
          </p>
          <p style={{ margin: '0', fontSize: '0.9rem', opacity: 0.8 }}>
            ✨ {t('media.searchFilterExport')}
          </p>
        </div>

        {/* أزرار الإجراءات */}
        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', marginBottom: '25px', flexWrap: 'wrap' }}>
          <button
            onClick={exportToExcel}
            disabled={isExporting}
            style={{
              background: isExporting
                ? 'linear-gradient(45deg, #6c757d, #5a6268)'
                : 'linear-gradient(45deg, #17a2b8, #138496)',
              color: 'white',
              padding: '12px 25px',
              borderRadius: '25px',
              border: 'none',
              fontWeight: 'bold',
              cursor: isExporting ? 'not-allowed' : 'pointer',
              boxShadow: '0 4px 15px rgba(23,162,184,0.3)',
              fontSize: '1rem'
            }}
          >
            {isExporting ? '⏳ ' + t('media.exporting') : '📊 ' + t('media.exportExcel')}
          </button>




        </div>

        {/* البحث والفلترة */}
        <div style={{
          background: '#4a5568',
          borderRadius: '15px',
          padding: '25px',
          marginBottom: '25px',
          border: '1px solid #6b7280'
        }}>
          <h2 style={{ color: '#f3f4f6', marginBottom: '20px', fontSize: '1.3rem' }}>
            🔍 {t('media.searchAndFilter')}
          </h2>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '15px' }}>
            {/* البحث بالاسم */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                {t('media.searchByName')}:
              </label>
              <input
                type="text"
                placeholder={t('media.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #6b7280',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: 'rtl',
                  color: 'white',
                  background: '#1f2937'
                }}
              />
            </div>

            {/* البحث بالكود */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                {t('media.searchByCode')}:
              </label>
              <input
                type="text"
                placeholder={t('media.codePlaceholder')}
                value={codeSearchTerm}
                onChange={(e) => setCodeSearchTerm(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #6b7280',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: 'rtl',
                  color: 'white',
                  background: '#1f2937'
                }}
              />
            </div>

            {/* فلترة بالنوع */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                {t('media.mediaType')}:
              </label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #6b7280',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: isRTL ? 'rtl' : 'ltr',
                  color: 'white',
                  background: '#1f2937'
                }}
              >
                <option value="ALL">{t('mediaTypes.ALL')}</option>
                <option value="FILM">{t('mediaTypes.FILM')}</option>
                <option value="SERIES">{t('mediaTypes.SERIES')}</option>
                <option value="PROGRAM">{t('mediaTypes.PROGRAM')}</option>
                <option value="SONG">{t('mediaTypes.SONG')}</option>
                <option value="FILLER">{t('mediaTypes.FILLER')}</option>
                <option value="STING">{t('mediaTypes.STING')}</option>
                <option value="PROMO">{t('mediaTypes.PROMO')}</option>
                <option value="NEXT">{t('mediaTypes.NEXT')}</option>
                <option value="NOW">{t('mediaTypes.NOW')}</option>
                <option value="سنعود">{t('mediaTypes.سنعود')}</option>
                <option value="عدنا">{t('mediaTypes.عدنا')}</option>
                <option value="MINI">{t('mediaTypes.MINI')}</option>
                <option value="CROSS">{t('mediaTypes.CROSS')}</option>
              </select>
            </div>

            {/* فلترة بالحالة */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                {t('media.mediaStatus')}:
              </label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #6b7280',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: isRTL ? 'rtl' : 'ltr',
                  color: 'white',
                  background: '#1f2937'
                }}
              >
                <option value="ALL">{t('mediaStatus.ALL')}</option>
                <option value="VALID">{t('mediaStatus.VALID')}</option>
                <option value="REJECTED_CENSORSHIP">{t('mediaStatus.REJECTED_CENSORSHIP')}</option>
                <option value="REJECTED_TECHNICAL">{t('mediaStatus.REJECTED_TECHNICAL')}</option>
                <option value="EXPIRED">{t('mediaStatus.EXPIRED')}</option>
                <option value="HOLD">{t('mediaStatus.HOLD')}</option>
              </select>
            </div>

            {/* الترتيب */}
            <div>
              <label style={{ display: 'block', marginBottom: '5px', color: '#f3f4f6', fontSize: '0.9rem' }}>
                {t('media.sortBy')}:
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                style={{
                  width: '100%',
                  padding: '10px',
                  border: '1px solid #6b7280',
                  borderRadius: '8px',
                  fontSize: '1rem',
                  direction: isRTL ? 'rtl' : 'ltr',
                  color: 'white',
                  background: '#1f2937'
                }}
              >
                <option value="newest">{t('media.newest')}</option>
                <option value="oldest">{t('media.oldest')}</option>
                <option value="name">{t('media.byName')}</option>
                <option value="type">{t('media.byType')}</option>
              </select>
            </div>
          </div>

          {/* إحصائيات البحث */}
          <div style={{
            marginTop: '15px',
            padding: '10px',
            background: '#1f2937',
            borderRadius: '8px',
            textAlign: 'center',
            color: '#d1d5db',
            border: '1px solid #6b7280'
          }}>
            📊 {t('media.searchStats', { filtered: filteredItems.length, total: mediaItems.length })}
          </div>
        </div>

        {/* Media Items */}
        {filteredItems.length === 0 ? (
          <div style={{
            background: '#4a5568',
            borderRadius: '15px',
            padding: '50px',
            textAlign: 'center',
            border: '1px solid #6b7280'
          }}>
            <h2 style={{ color: '#d1d5db', fontSize: '1.5rem' }}>
              📭 {t('media.noMediaFound')}
            </h2>
            <p style={{ color: '#a0aec0', marginTop: '10px' }}>
              {t('media.startAdding')}
            </p>
          </div>
        ) : (
          <div style={{ display: 'grid', gap: '20px' }}>
            {filteredItems.map((item) => (
              <div key={item.id} style={{
                background: '#4a5568',
                borderRadius: '15px',
                padding: '25px',
                border: '1px solid #6b7280'
              }}>
                <div style={{ display: 'grid', gridTemplateColumns: '1fr auto', gap: '20px', alignItems: 'start' }}>
                  <div>
                    <h3 style={{ color: '#f3f4f6', marginBottom: '15px', fontSize: '1.4rem' }}>
                      {item.name}
                    </h3>
                    
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px', marginBottom: '15px', color: '#d1d5db' }}>
                      <div>
                        <strong>{t('common.type')}:</strong> {getTypeLabel(item.type)}
                      </div>
                      <div>
                        <strong>{t('media.channel')}:</strong> {getChannelLabel(item.channel)}
                      </div>
                      <div>
                        <strong>{t('common.status')}:</strong> {getStatusLabel(item.status)}
                      </div>
                      <div>
                        <strong>{t('media.segmentCount')}:</strong> {item.segments.length}
                      </div>
                    </div>

                    {item.description && (
                      <p style={{ color: '#a0aec0', marginBottom: '10px' }}>
                        <strong>{t('media.description')}:</strong> {item.description}
                      </p>
                    )}

                    {item.segments.length > 0 && (
                      <div style={{ marginTop: '15px', color: '#d1d5db' }}>
                        <strong>{t('media.segments')}:</strong>
                        <div style={{ display: 'grid', gap: '8px', marginTop: '8px' }}>
                          {item.segments.map((segment, index) => (
                            <div key={`${item.id}_segment_${index}`} style={{
                              background: '#1f2937',
                              padding: '8px 12px',
                              borderRadius: '8px',
                              fontSize: '0.9rem',
                              color: '#d1d5db',
                              border: '1px solid #6b7280'
                            }}>
                              <strong>#{segment.segmentNumber}</strong> -
                              {segment.code && segment.code.trim() !== '' ? ` ${segment.code} - ` : ` [${t('media.noCode')}] - `}
                              {segment.timeIn} → {segment.timeOut} ({segment.duration})

                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
                    <button
                      onClick={() => {
                        // توجيه لصفحة التعديل مع معرف المادة
                        window.location.href = `/edit-media?id=${item.id}`;
                      }}
                      style={{
                        background: 'linear-gradient(45deg, #007bff, #0056b3)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '8px',
                        padding: '8px 16px',
                        cursor: 'pointer',
                        fontSize: '0.9rem',
                        marginBottom: '5px'
                      }}
                    >
                      ✏️ {t('media.edit')}
                    </button>

                    <button
                      onClick={() => deleteMediaItem(item.id)}
                      style={{
                        background: 'linear-gradient(45deg, #dc3545, #c82333)',
                        color: 'white',
                        border: 'none',
                        borderRadius: '8px',
                        padding: '8px 16px',
                        cursor: 'pointer',
                        fontSize: '0.9rem'
                      }}
                    >
                      🗑️ {t('media.delete')}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* زر العودة لأعلى */}
        {showScrollToTop && (
          <button
            onClick={scrollToTop}
            style={{
              position: 'fixed',
              bottom: '30px',
              right: '30px',
              width: '60px',
              height: '60px',
              borderRadius: '50%',
              background: 'linear-gradient(45deg, #007bff, #0056b3)',
              color: 'white',
              border: 'none',
              cursor: 'pointer',
              fontSize: '24px',
              boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
              zIndex: 1000,
              transition: 'all 0.3s ease',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'scale(1.1)';
              e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 123, 255, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'scale(1)';
              e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 123, 255, 0.3)';
            }}
            title={t('media.scrollToTop')}
          >
            ⬆️
          </button>
        )}
        <ToastContainer />
      </DashboardLayout>
    </AuthGuard>
  );
}
