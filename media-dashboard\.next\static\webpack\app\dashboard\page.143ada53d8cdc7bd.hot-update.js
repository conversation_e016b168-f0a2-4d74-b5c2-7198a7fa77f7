"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/hooks/useAppTranslation.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAppTranslation.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppTranslation: () => (/* binding */ useAppTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _utils_translations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/translations */ \"(app-pages-browser)/./src/utils/translations.ts\");\n\n\n// Hook مخصص للترجمة يضمن الاتساق\nconst useAppTranslation = ()=>{\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const currentLang = i18n.language || 'ar';\n    // دالة الترجمة الأساسية\n    const t = (key)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getTranslation)(key, currentLang);\n    };\n    // دالة ترجمة أنواع المواد\n    const tMediaType = (type)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getMediaTypeLabel)(type, currentLang);\n    };\n    // دالة ترجمة الأدوار\n    const tRole = (role)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getRoleLabel)(role, currentLang);\n    };\n    // دالة ترجمة أوصاف الأدوار\n    const tRoleDesc = (role)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getRoleDescription)(role, currentLang);\n    };\n    return {\n        t,\n        tMediaType,\n        tRole,\n        tRoleDesc,\n        currentLang,\n        isRTL: currentLang === 'ar',\n        i18n\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAppTranslation.ts\n"));

/***/ })

});