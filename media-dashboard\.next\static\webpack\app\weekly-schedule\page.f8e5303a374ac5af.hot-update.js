"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/weekly-schedule/page",{

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WeeklySchedulePage() {\n    _s();\n    const { isViewer } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [readOnlyMode, setReadOnlyMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // مرجع للجدول لتثبيت موضع التمرير\n    const scheduleTableRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const visibleRowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const visibleRowIndexRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(-1);\n    // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (isViewer) {\n                setReadOnlyMode(true);\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        isViewer\n    ]);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع - يتم تحديثها عند تغيير اللغة\n    const days = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"WeeklySchedulePage.useMemo[days]\": ()=>[\n                t('common.sunday'),\n                t('common.monday'),\n                t('common.tuesday'),\n                t('common.wednesday'),\n                t('common.thursday'),\n                t('common.friday'),\n                t('common.saturday')\n            ]\n    }[\"WeeklySchedulePage.useMemo[days]\"], [\n        t\n    ]);\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        // التأكد من أن selectedWeek يمثل يوم الأحد\n        const inputDate = new Date(selectedWeek + 'T00:00:00'); // استخدام منتصف الليل\n        console.log('📅 التاريخ المدخل:', selectedWeek);\n        console.log('📅 يوم الأسبوع للتاريخ المدخل:', inputDate.getDay(), '(0=أحد)');\n        // التأكد من أن نبدأ من يوم الأحد\n        const sundayDate = new Date(inputDate);\n        const dayOfWeek = inputDate.getDay();\n        if (dayOfWeek !== 0) {\n            // إذا لم يكن الأحد، نحسب الأحد السابق\n            sundayDate.setDate(inputDate.getDate() - dayOfWeek);\n        }\n        console.log('📅 يوم الأحد المحسوب:', sundayDate.toISOString().split('T')[0]);\n        const dates = [];\n        for(let i = 0; i < 7; i++){\n            // إنشاء تاريخ جديد لكل يوم بدءاً من الأحد\n            const currentDate = new Date(sundayDate);\n            currentDate.setDate(sundayDate.getDate() + i);\n            // استخدام التنسيق العربي للتاريخ (يوم/شهر/سنة)\n            const day = currentDate.getDate();\n            const month = currentDate.getMonth() + 1; // الشهور تبدأ من 0\n            const year = currentDate.getFullYear();\n            // تنسيق التاريخ بالشكل dd/mm/yyyy\n            const dateStr = \"\".concat(day.toString().padStart(2, '0'), \"/\").concat(month.toString().padStart(2, '0'), \"/\").concat(year);\n            dates.push(dateStr);\n            // التحقق من صحة اليوم\n            const actualDayOfWeek = currentDate.getDay();\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(currentDate.toISOString().split('T')[0], \" → \").concat(dateStr, \" [يوم الأسبوع: \").concat(actualDayOfWeek, \"]\"));\n            // تحذير إذا كان اليوم لا يتطابق مع المتوقع\n            if (actualDayOfWeek !== i) {\n                console.warn(\"⚠️ عدم تطابق: متوقع يوم \".concat(i, \" لكن حصلنا على \").concat(actualDayOfWeek));\n            }\n        }\n        return dates;\n    };\n    // استخدام useEffect لتحديث التواريخ عند تغيير الأسبوع المحدد\n    const [weekDates, setWeekDates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--'\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const dates = getWeekDates();\n            console.log('🔄 تحديث التواريخ:', dates);\n            setWeekDates(dates);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || t('schedule.unknown');\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.season'), \" \").concat(item.seasonNumber, \" \").concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.season'), \" \").concat(item.seasonNumber, \" \").concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - \".concat(t('schedule.part'), \" \").concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            // استخدام التاريخ المحلي مع تجنب مشاكل المنطقة الزمنية\n            const today = new Date();\n            // تحويل إلى تاريخ محلي بدون وقت\n            const year = today.getFullYear();\n            const month = today.getMonth();\n            const day = today.getDate();\n            const localDate = new Date(year, month, day);\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم الفعلي:', \"\".concat(year, \"-\").concat((month + 1).toString().padStart(2, '0'), \"-\").concat(day.toString().padStart(2, '0')));\n            console.log('  📊 يوم الأسبوع:', localDate.getDay(), '(0=أحد)');\n            // حساب يوم الأحد لهذا الأسبوع\n            const sunday = new Date(localDate);\n            sunday.setDate(localDate.getDate() - localDate.getDay());\n            // تحويل إلى string بطريقة محلية\n            const weekStart = \"\".concat(sunday.getFullYear(), \"-\").concat((sunday.getMonth() + 1).toString().padStart(2, '0'), \"-\").concat(sunday.getDate().toString().padStart(2, '0'));\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            console.log('  📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // حفظ الصف المرئي قبل تحديث البيانات\n    const saveVisibleRowIndex = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\": ()=>{\n            if (!scheduleTableRef.current) {\n                console.log('⚠️ لم يتم العثور على مرجع الجدول');\n                return;\n            }\n            // الحصول على جميع صفوف الساعات في الجدول\n            const hourRows = scheduleTableRef.current.querySelectorAll('.hour-row');\n            if (!hourRows.length) {\n                console.log('⚠️ لم يتم العثور على صفوف الساعات');\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D تم العثور على \".concat(hourRows.length, \" صف ساعة\"));\n            // تحديد الصف المرئي حاليًا في منتصف الشاشة\n            const viewportHeight = window.innerHeight;\n            const viewportMiddle = window.scrollY + viewportHeight / 2;\n            console.log(\"\\uD83D\\uDCCF منتصف الشاشة: \".concat(viewportMiddle, \"px (ارتفاع الشاشة: \").concat(viewportHeight, \"px, موضع التمرير: \").concat(window.scrollY, \"px)\"));\n            let closestRow = null;\n            let closestDistance = Infinity;\n            let closestIndex = -1;\n            // البحث عن أقرب صف للمنتصف\n            hourRows.forEach({\n                \"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\": (row, index)=>{\n                    const rect = row.getBoundingClientRect();\n                    const rowTop = window.scrollY + rect.top;\n                    const rowBottom = rowTop + rect.height;\n                    const rowMiddle = rowTop + rect.height / 2;\n                    const distance = Math.abs(viewportMiddle - rowMiddle);\n                    console.log(\"  صف \".concat(index, \" (\").concat(hours[index], \"): العلوي=\").concat(rect.top.toFixed(0), \", الارتفاع=\").concat(rect.height.toFixed(0), \", المسافة=\").concat(distance.toFixed(0)));\n                    if (distance < closestDistance) {\n                        closestDistance = distance;\n                        closestRow = row;\n                        closestIndex = index;\n                    }\n                }\n            }[\"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\"]);\n            if (closestRow) {\n                visibleRowRef.current = closestRow;\n                visibleRowIndexRef.current = closestIndex;\n                console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي: الساعة \".concat(hours[closestIndex], \", الفهرس \").concat(closestIndex, \", المسافة=\").concat(closestDistance.toFixed(0), \"px\"));\n            } else {\n                console.log('⚠️ لم يتم العثور على صف مرئي');\n            }\n        }\n    }[\"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\"], [\n        hours\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات - نسخة مبسطة وأكثر فعالية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current !== undefined) {\n                console.log(\"\\uD83D\\uDD04 استعادة موضع التمرير: \".concat(scrollPositionRef.current, \"px\"));\n                // تأخير للتأكد من اكتمال الرندر\n                const timer = setTimeout({\n                    \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                        const targetPosition = scrollPositionRef.current;\n                        // استعادة الموضع مباشرة\n                        window.scrollTo({\n                            top: targetPosition,\n                            behavior: 'instant'\n                        });\n                        console.log(\"\\uD83D\\uDCCD تم استعادة الموضع إلى: \".concat(targetPosition, \"px\"));\n                        // التحقق من نجاح العملية والتصحيح إذا لزم الأمر\n                        setTimeout({\n                            \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                                const currentPosition = window.scrollY;\n                                const difference = Math.abs(currentPosition - targetPosition);\n                                if (difference > 5) {\n                                    console.log(\"\\uD83D\\uDCCD تصحيح الموضع: \".concat(currentPosition, \"px → \").concat(targetPosition, \"px (فرق: \").concat(difference, \"px)\"));\n                                    window.scrollTo({\n                                        top: targetPosition,\n                                        behavior: 'instant'\n                                    });\n                                } else {\n                                    console.log(\"✅ تم تثبيت موضع التمرير بنجاح\");\n                                }\n                            }\n                        }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 100);\n                        shouldRestoreScroll.current = false;\n                    }\n                }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 200);\n                return ({\n                    \"WeeklySchedulePage.useLayoutEffect\": ()=>clearTimeout(timer)\n                })[\"WeeklySchedulePage.useLayoutEffect\"];\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            // التأكد من حفظ موضع التمرير إذا لم يكن محفوظاً\n            if (!shouldRestoreScroll.current) {\n                const currentScrollPosition = window.scrollY;\n                scrollPositionRef.current = currentScrollPosition;\n                shouldRestoreScroll.current = true;\n                console.log(\"\\uD83D\\uDCCD حفظ تلقائي لموضع التمرير: \".concat(currentScrollPosition, \"px\"));\n            }\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"\".concat(t('messages.updateError'), \": \").concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"\".concat(t('messages.networkError'), \": \").concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert(t('schedule.enterMediaName'));\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || t('messages.saveFailed'));\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert(t('messages.saveFailed'));\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm(t('schedule.confirmDeleteTemp'))) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || t('messages.deleteFailed'));\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert(t('messages.deleteFailed'));\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي بدقة\n            const currentScrollPosition = window.scrollY;\n            scrollPositionRef.current = currentScrollPosition;\n            // حفظ الصف المرئي قبل التحديث\n            saveVisibleRowIndex();\n            shouldRestoreScroll.current = true;\n            console.log(\"\\uD83D\\uDCCD تم حفظ موضع التمرير: \".concat(currentScrollPosition, \"px والصف المرئي عند إضافة مادة\"));\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ ' + t('schedule.timeConflict'));\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert(t('messages.invalidData'));\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تحديث محلي بدلاً من إعادة تحميل كامل\n                const newScheduleItem = {\n                    id: result.data.id,\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime: startTime,\n                    endTime: endTime,\n                    weekStart: selectedWeek,\n                    mediaItem: mediaItem,\n                    isTemporary: mediaItem.isTemporary || false,\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                };\n                // إضافة المادة للجدول محلياً\n                setScheduleItems((prev)=>[\n                        ...prev,\n                        newScheduleItem\n                    ]);\n                console.log('✅ تم إضافة المادة للجدول محلياً بدون إعادة تحميل');\n            } else {\n                alert(result.error);\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || t('schedule.unknown');\n        const itemType = item.isRerun ? t('schedule.rerunMaterial') : item.isTemporary ? t('schedule.tempMaterial') : t('schedule.originalMaterial');\n        const warningMessage = item.isRerun ? t('schedule.deleteWarningRerun') : item.isTemporary ? t('schedule.deleteWarningTemp') : t('schedule.deleteWarningOriginal');\n        const confirmed = window.confirm(\"\".concat(t('schedule.confirmDelete', {\n            type: itemType,\n            name: itemName\n        }), \"\\n\\n\") + \"\".concat(t('schedule.time'), \": \").concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + warningMessage);\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف من الخادم أيضًا\n            if (item.isTemporary) {\n                console.log(\"\\uD83D\\uDDD1️ بدء حذف المادة المؤقتة: \".concat(itemName, \" (\").concat(item.id, \")\"));\n                try {\n                    var _item_mediaItem1, _item_mediaItem2;\n                    // حذف المادة المؤقتة من الخادم\n                    console.log(\"\\uD83D\\uDD0D إرسال طلب حذف المادة المؤقتة: \".concat(item.id, \" (\").concat(itemName, \")\"));\n                    console.log(\"\\uD83D\\uDCCA بيانات المادة المؤقتة:\", {\n                        id: item.id,\n                        mediaItemId: item.mediaItemId,\n                        dayOfWeek: item.dayOfWeek,\n                        startTime: item.startTime,\n                        endTime: item.endTime,\n                        isTemporary: item.isTemporary,\n                        isRerun: item.isRerun,\n                        mediaItemName: (_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.name\n                    });\n                    const response = await fetch('/api/weekly-schedule', {\n                        method: 'PATCH',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            action: 'deleteTempItem',\n                            tempItemId: item.id,\n                            mediaItemId: item.mediaItemId,\n                            dayOfWeek: item.dayOfWeek,\n                            startTime: item.startTime,\n                            endTime: item.endTime,\n                            weekStart: selectedWeek,\n                            mediaItemName: (_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.name\n                        })\n                    });\n                    const result = await response.json();\n                    if (result.success) {\n                        console.log(\"✅ تم حذف المادة المؤقتة من الخادم: \".concat(itemName));\n                        // حذف محلي بعد نجاح الحذف من الخادم\n                        if (item.isRerun) {\n                            // حذف إعادة مؤقتة فقط\n                            setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                            console.log(\"✅ تم حذف إعادة مؤقتة محليًا: \".concat(itemName));\n                        } else {\n                            // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                            setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isRerun && scheduleItem.originalId === item.id)));\n                            console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها محليًا: \".concat(itemName));\n                        }\n                        // إعادة تحميل البيانات للتأكد من التزامن\n                        // حفظ الصف المرئي قبل إعادة التحميل\n                        saveVisibleRowIndex();\n                        shouldRestoreScroll.current = true;\n                        console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي قبل إعادة تحميل البيانات\");\n                        await fetchScheduleData();\n                    } else {\n                        console.error(\"❌ فشل حذف المادة المؤقتة من الخادم: \".concat(result.error));\n                        alert(\"فشل حذف المادة المؤقتة: \".concat(result.error));\n                    }\n                } catch (error) {\n                    console.error(\"❌ خطأ أثناء حذف المادة المؤقتة: \".concat(error));\n                    alert('حدث خطأ أثناء حذف المادة المؤقتة');\n                }\n                return;\n            }\n            // حفظ الصف المرئي قبل التحديث\n            saveVisibleRowIndex();\n            shouldRestoreScroll.current = true;\n            console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي عند حذف مادة\");\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                // حفظ الصف المرئي قبل إعادة التحميل\n                saveVisibleRowIndex();\n                shouldRestoreScroll.current = true;\n                console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي قبل إعادة تحميل البيانات بعد الحذف\");\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        if (!selectedWeek) return;\n        const currentDate = new Date(selectedWeek + 'T00:00:00');\n        console.log('📅 تغيير الأسبوع - البداية:', {\n            direction: direction > 0 ? 'التالي' : 'السابق',\n            currentWeek: selectedWeek,\n            currentDayOfWeek: currentDate.getDay()\n        });\n        // إضافة أو طرح 7 أيام\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        const newWeekStart = currentDate.toISOString().split('T')[0];\n        console.log('📅 تغيير الأسبوع - النتيجة:', {\n            newWeek: newWeekStart,\n            newDayOfWeek: currentDate.getDay()\n        });\n        setSelectedWeek(newWeekStart);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"⏳ \",\n                    t('schedule.loadingSchedule')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 955,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 954,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"\\uD83D\\uDCC5 \",\n                    t('schedule.selectingDate')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 964,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 963,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: t('schedule.weeklySchedule'),\n            subtitle: t('schedule.weeklySubtitle'),\n            icon: \"\\uD83D\\uDCC5\",\n            fullWidth: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    height: 'calc(100vh - 120px)',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: isRTL ? 'rtl' : 'ltr',\n                    gap: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '320px',\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            border: '1px solid #6b7280',\n                            padding: '20px',\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: '0 0 15px 0',\n                                    color: '#f3f4f6'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDA \",\n                                    t('schedule.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    border: '2px solid #f59e0b',\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            margin: '0 0 10px 0',\n                                            color: '#fbbf24',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: [\n                                            \"⚡ \",\n                                            t('schedule.addTempMedia')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.mediaName'),\n                                        value: tempMediaName,\n                                        onChange: (e)=>setTempMediaName(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: tempMediaType,\n                                        onChange: (e)=>setTempMediaType(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROGRAM\",\n                                                children: [\n                                                    \"\\uD83D\\uDCFB \",\n                                                    tMediaType('PROGRAM')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SERIES\",\n                                                children: [\n                                                    \"\\uD83D\\uDCFA \",\n                                                    tMediaType('SERIES')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"FILM\",\n                                                children: [\n                                                    \"\\uD83C\\uDFA5 \",\n                                                    tMediaType('FILM')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"FILLER\",\n                                                children: [\n                                                    \"⏸️ \",\n                                                    tMediaType('FILLER')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"STING\",\n                                                children: [\n                                                    \"⚡ \",\n                                                    tMediaType('STING')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROMO\",\n                                                children: [\n                                                    \"\\uD83D\\uDCE2 \",\n                                                    tMediaType('PROMO')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"NEXT\",\n                                                children: [\n                                                    \"▶️ \",\n                                                    tMediaType('NEXT')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"NOW\",\n                                                children: [\n                                                    \"\\uD83D\\uDD34 \",\n                                                    tMediaType('NOW')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"سنعود\",\n                                                children: [\n                                                    \"⏰ \",\n                                                    tMediaType('سنعود')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1041,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"عدنا\",\n                                                children: [\n                                                    \"✅ \",\n                                                    tMediaType('عدنا')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MINI\",\n                                                children: [\n                                                    \"\\uD83D\\uDD38 \",\n                                                    tMediaType('MINI')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"CROSS\",\n                                                children: [\n                                                    \"✖️ \",\n                                                    tMediaType('CROSS')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1044,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"LIVE\",\n                                                children: [\n                                                    \"\\uD83D\\uDD34 \",\n                                                    t('schedule.liveProgram')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PENDING\",\n                                                children: [\n                                                    \"\\uD83D\\uDFE1 \",\n                                                    t('schedule.pendingDelivery')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.duration'),\n                                        value: tempMediaDuration,\n                                        onChange: (e)=>setTempMediaDuration(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.notes'),\n                                        value: tempMediaNotes,\n                                        onChange: (e)=>setTempMediaNotes(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '10px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: addTempMedia,\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            background: '#ff9800',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '13px',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            marginBottom: '8px'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('schedule.add')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1083,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            console.log('🔄 تحديث الإعادات...');\n                                            scrollPositionRef.current = window.scrollY;\n                                            shouldRestoreScroll.current = true;\n                                            await fetchScheduleData();\n                                        },\n                                        style: {\n                                            width: '100%',\n                                            padding: '6px',\n                                            background: '#4caf50',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: [\n                                            \"♻️ \",\n                                            t('schedule.updateReruns')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedType,\n                                onChange: (e)=>setSelectedType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '10px',\n                                    fontSize: '14px',\n                                    backgroundColor: 'white',\n                                    color: '#333'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('schedule.allTypes')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: [\n                                            \"\\uD83D\\uDCFA \",\n                                            tMediaType('SERIES')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILM\",\n                                        children: [\n                                            \"\\uD83C\\uDFA5 \",\n                                            tMediaType('FILM')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: [\n                                            \"\\uD83D\\uDCFB \",\n                                            tMediaType('PROGRAM')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SONG\",\n                                        children: [\n                                            \"\\uD83C\\uDFB5 \",\n                                            tMediaType('SONG')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROMO\",\n                                        children: [\n                                            \"\\uD83D\\uDCE2 \",\n                                            tMediaType('PROMO')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"STING\",\n                                        children: [\n                                            \"⚡ \",\n                                            tMediaType('STING')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILLER\",\n                                        children: [\n                                            \"⏸️ \",\n                                            tMediaType('FILLER')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NEXT\",\n                                        children: [\n                                            \"▶️ \",\n                                            tMediaType('NEXT')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NOW\",\n                                        children: [\n                                            \"\\uD83D\\uDD34 \",\n                                            tMediaType('NOW')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"سنعود\",\n                                        children: [\n                                            \"⏰ \",\n                                            tMediaType('سنعود')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"عدنا\",\n                                        children: [\n                                            \"✅ \",\n                                            tMediaType('عدنا')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MINI\",\n                                        children: [\n                                            \"\\uD83D\\uDD38 \",\n                                            tMediaType('MINI')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"CROSS\",\n                                        children: [\n                                            \"✖️ \",\n                                            tMediaType('CROSS')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"\\uD83D\\uDD0D \".concat(t('schedule.searchMedia')),\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '15px',\n                                    fontSize: '14px',\n                                    color: '#333',\n                                    background: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '12px',\n                                    color: '#d1d5db',\n                                    marginBottom: '10px',\n                                    textAlign: 'center',\n                                    padding: '5px',\n                                    background: '#1f2937',\n                                    borderRadius: '4px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    t('schedule.resultsCount', {\n                                        count: filteredMedia.length,\n                                        total: allAvailableMedia.length\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '8px'\n                                },\n                                children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '20px',\n                                        color: '#666',\n                                        background: '#f8f9fa',\n                                        borderRadius: '8px',\n                                        border: '2px dashed #dee2e6'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1197,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: 'bold',\n                                                marginBottom: '5px'\n                                            },\n                                            children: t('schedule.noMedia')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: searchTerm || selectedType ? t('schedule.changeFilter') : t('schedule.addNewMedia')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1189,\n                                    columnNumber: 17\n                                }, this) : filteredMedia.map((item)=>{\n                                    // تحديد لون المادة حسب النوع\n                                    const getItemStyle = ()=>{\n                                        if (item.isTemporary) {\n                                            switch(item.type){\n                                                case 'LIVE':\n                                                    return {\n                                                        background: '#ffebee',\n                                                        border: '2px solid #f44336',\n                                                        borderLeft: '5px solid #f44336'\n                                                    };\n                                                case 'PENDING':\n                                                    return {\n                                                        background: '#fff8e1',\n                                                        border: '2px solid #ffc107',\n                                                        borderLeft: '5px solid #ffc107'\n                                                    };\n                                                default:\n                                                    return {\n                                                        background: '#f3e5f5',\n                                                        border: '2px solid #9c27b0',\n                                                        borderLeft: '5px solid #9c27b0'\n                                                    };\n                                            }\n                                        }\n                                        return {\n                                            background: '#fff',\n                                            border: '1px solid #ddd'\n                                        };\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>handleDragStart(e, item),\n                                        style: {\n                                            ...getItemStyle(),\n                                            borderRadius: '8px',\n                                            padding: '12px',\n                                            cursor: 'grab',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                            position: 'relative'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(-2px)';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(0)';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteTempMedia(item.id);\n                                                },\n                                                style: {\n                                                    position: 'absolute',\n                                                    top: '5px',\n                                                    left: '5px',\n                                                    background: '#f44336',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '50%',\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    fontSize: '12px',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                title: t('schedule.confirmDeleteTemp'),\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: 'bold',\n                                                    color: '#333',\n                                                    marginBottom: '4px'\n                                                },\n                                                children: [\n                                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '10px',\n                                                            background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                            color: 'white',\n                                                            padding: '2px 6px',\n                                                            borderRadius: '10px',\n                                                            marginLeft: '5px'\n                                                        },\n                                                        children: item.type === 'LIVE' ? \"\\uD83D\\uDD34 \".concat(t('schedule.liveProgram')) : item.type === 'PENDING' ? \"\\uD83D\\uDFE1 \".concat(t('schedule.pendingDelivery')) : \"\\uD83D\\uDFE3 \".concat(t('schedule.temporary'))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1289,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    getMediaDisplayText(item)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '12px',\n                                                    color: '#666'\n                                                },\n                                                children: [\n                                                    item.type,\n                                                    \" • \",\n                                                    item.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '11px',\n                                                    color: '#888',\n                                                    marginTop: '4px'\n                                                },\n                                                children: item.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1236,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 980,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px',\n                                    background: '#4a5568',\n                                    padding: '15px',\n                                    borderRadius: '15px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/daily-schedule',\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: [\n                                                    \"\\uD83D\\uDCCB \",\n                                                    t('schedule.broadcastSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1333,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                        const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                        if (!response.ok) {\n                                                            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                        }\n                                                        const blob = await response.blob();\n                                                        const url = window.URL.createObjectURL(blob);\n                                                        const a = document.createElement('a');\n                                                        a.href = url;\n                                                        a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                        document.body.appendChild(a);\n                                                        a.click();\n                                                        window.URL.revokeObjectURL(url);\n                                                        document.body.removeChild(a);\n                                                        console.log('✅ تم تصدير الخريطة بنجاح');\n                                                    } catch (error) {\n                                                        console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                        alert(\"\".concat(t('messages.exportError'), \": \").concat(error.message));\n                                                    }\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: [\n                                                    \"\\uD83D\\uDCCA \",\n                                                    t('schedule.exportSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#f3f4f6',\n                                                    fontSize: '1.2rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCC5 \",\n                                                    t('schedule.selectedWeek')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1332,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(-1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    \"← \",\n                                                    t('schedule.previousWeek')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1409,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedWeek,\n                                                onChange: (e)=>{\n                                                    const selectedDate = new Date(e.target.value + 'T00:00:00');\n                                                    const dayOfWeek = selectedDate.getDay();\n                                                    console.log('📅 تغيير التاريخ من التقويم:', {\n                                                        selectedDate: e.target.value,\n                                                        dayOfWeek: dayOfWeek,\n                                                        dayName: [\n                                                            'الأحد',\n                                                            'الاثنين',\n                                                            'الثلاثاء',\n                                                            'الأربعاء',\n                                                            'الخميس',\n                                                            'الجمعة',\n                                                            'السبت'\n                                                        ][dayOfWeek]\n                                                    });\n                                                    // حساب بداية الأسبوع (الأحد)\n                                                    const sunday = new Date(selectedDate);\n                                                    sunday.setHours(0, 0, 0, 0); // تصفير الوقت\n                                                    sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                                    const weekStart = sunday.toISOString().split('T')[0];\n                                                    console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                                    console.log('📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');\n                                                    setSelectedWeek(weekStart);\n                                                },\n                                                style: {\n                                                    padding: '8px',\n                                                    border: '1px solid #6b7280',\n                                                    borderRadius: '5px',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1423,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    t('schedule.nextWeek'),\n                                                    \" →\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1322,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: scheduleTableRef,\n                                style: {\n                                    background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',\n                                    borderRadius: '10px',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                    minHeight: '80vh'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#f8f9fa'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '12px',\n                                                            border: '1px solid #dee2e6',\n                                                            fontWeight: 'bold',\n                                                            width: '80px',\n                                                            color: '#000'\n                                                        },\n                                                        children: t('schedule.time')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1486,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '12px',\n                                                                border: '1px solid #dee2e6',\n                                                                fontWeight: 'bold',\n                                                                width: \"\".concat(100 / 7, \"%\"),\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        marginBottom: '4px',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: day\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1503,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: weekDates[index]\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1504,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1496,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1484,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hour-row\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                background: '#f8f9fa',\n                                                                fontWeight: 'bold',\n                                                                textAlign: 'center',\n                                                                padding: '8px',\n                                                                border: '1px solid #dee2e6',\n                                                                color: '#000'\n                                                            },\n                                                            children: hour\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1516,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        days.map((_, dayIndex)=>{\n                                                            const cellItems = getItemsForCell(dayIndex, hour);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                onDragOver: handleDragOver,\n                                                                onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                                style: {\n                                                                    border: '1px solid #dee2e6',\n                                                                    padding: '8px',\n                                                                    height: '150px',\n                                                                    cursor: 'pointer',\n                                                                    background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',\n                                                                    verticalAlign: 'top'\n                                                                },\n                                                                children: cellItems.map((item)=>{\n                                                                    var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        draggable: true,\n                                                                        onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                        onClick: ()=>deleteItem(item),\n                                                                        style: {\n                                                                            background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                            border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                            borderRadius: '4px',\n                                                                            padding: '8px 6px',\n                                                                            marginBottom: '4px',\n                                                                            fontSize: '1rem',\n                                                                            cursor: 'grab',\n                                                                            transition: 'all 0.2s ease',\n                                                                            minHeight: '60px',\n                                                                            display: 'flex',\n                                                                            flexDirection: 'column',\n                                                                            justifyContent: 'center'\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1.02)';\n                                                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1)';\n                                                                            e.currentTarget.style.boxShadow = 'none';\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    gap: '4px',\n                                                                                    color: '#000'\n                                                                                },\n                                                                                children: [\n                                                                                    item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#666'\n                                                                                        },\n                                                                                        children: [\n                                                                                            \"♻️ \",\n                                                                                            item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                            item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1584,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                        },\n                                                                                        children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1588,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#ff9800'\n                                                                                        },\n                                                                                        children: \"\\uD83C\\uDF1F\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1596,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#000'\n                                                                                        },\n                                                                                        children: item.mediaItem ? getMediaDisplayText(item.mediaItem) : t('schedule.unknown')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1598,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1582,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.8rem',\n                                                                                    color: '#000',\n                                                                                    marginTop: '4px'\n                                                                                },\n                                                                                children: [\n                                                                                    item.startTime,\n                                                                                    \" - \",\n                                                                                    item.endTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1602,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.5rem',\n                                                                                    color: '#888',\n                                                                                    fontStyle: 'italic'\n                                                                                },\n                                                                                children: t('schedule.rerunIndicator')\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1606,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, item.id, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1544,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, dayIndex, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1530,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, hourIndex, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1515,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1513,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1482,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    padding: '20px',\n                                    borderRadius: '15px',\n                                    marginTop: '20px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 20px 0',\n                                            fontSize: '1.3rem',\n                                            textAlign: 'center'\n                                        },\n                                        children: t('schedule.usageInstructions')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.addMediaTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1633,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction1')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1635,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction2')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1636,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction3')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1637,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction4')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1638,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1634,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1632,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.deleteMediaTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1642,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            t('schedule.originalMaterial'),\n                                                                            \":\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1644,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    t('schedule.deleteOriginalInfo')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1644,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            t('schedule.rerunMaterial'),\n                                                                            \":\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1645,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    t('schedule.deleteRerunInfo')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1645,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.deleteConfirmInfo')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1646,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1643,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1641,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1631,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.primeTimeTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1653,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.primeTimeSchedule1')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1655,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.primeTimeSchedule2')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1656,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    style: {\n                                                                        color: '#fbbf24'\n                                                                    },\n                                                                    children: t('schedule.primeTimeColor')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1657,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1657,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1654,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1652,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.rerunsTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1661,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: t('schedule.rerunsSchedule1')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1663,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1663,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart1Sun')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1665,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart2Sun')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1666,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1664,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: t('schedule.rerunsSchedule2')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1668,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1668,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart1Thu')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1670,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart2Thu')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1669,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    style: {\n                                                                        color: '#9ca3af'\n                                                                    },\n                                                                    children: t('schedule.rerunsColor')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1673,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1673,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1662,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1660,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1651,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #f59e0b'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: t('schedule.dateManagementTitle')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1679,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: [\n                                                    \" \",\n                                                    t('schedule.dateManagementInfo')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1680,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1678,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #3b82f6'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: t('schedule.importantNoteTitle')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1684,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: [\n                                                    \" \",\n                                                    t('schedule.importantNoteInfo')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1685,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1683,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1622,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1320,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 972,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 971,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 970,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"1qqquTfuJ9oWNzcE/gIP+85G1gw=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ })

});