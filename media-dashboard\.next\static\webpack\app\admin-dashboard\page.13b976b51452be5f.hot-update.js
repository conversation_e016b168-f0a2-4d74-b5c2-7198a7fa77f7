"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/page",{

/***/ "(app-pages-browser)/./src/app/admin-dashboard/page.tsx":
/*!******************************************!*\
  !*** ./src/app/admin-dashboard/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdminDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction AdminDashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast)();\n    const { t, tRole, tRoleDesc, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__.useAppTranslation)();\n    // دالة ترجمة أسماء الأدوار\n    const getRoleName = (role)=>{\n        const names = {\n            'ADMIN': 'مدير النظام',\n            'CONTENT_MANAGER': 'مدير المحتوى',\n            'MEDIA_MANAGER': 'مدير قاعدة البيانات',\n            'SCHEDULER': 'مجدول البرامج',\n            'FULL_VIEWER': 'مستخدم رؤية كاملة',\n            'DATA_ENTRY': 'مدخل بيانات',\n            'MAP_SCHEDULER': 'مسؤول الخريطة والجدول',\n            'VIEWER': 'مستخدم عرض'\n        };\n        return names[role] || role;\n    };\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAddUser, setShowAddUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingUser, setEditingUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editUserData, setEditUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        id: '',\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        phone: '',\n        role: '',\n        isActive: true\n    });\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: '',\n        password: '',\n        name: '',\n        email: '',\n        phone: '',\n        role: 'VIEWER'\n    });\n    const roles = {\n        'ADMIN': {\n            name: t('roles.ADMIN'),\n            color: '#dc3545',\n            icon: '👑'\n        },\n        'CONTENT_MANAGER': {\n            name: t('roles.CONTENT_MANAGER'),\n            color: '#6f42c1',\n            icon: '📊'\n        },\n        'MEDIA_MANAGER': {\n            name: t('roles.MEDIA_MANAGER'),\n            color: '#28a745',\n            icon: '📝'\n        },\n        'SCHEDULER': {\n            name: t('roles.SCHEDULER'),\n            color: '#007bff',\n            icon: '📅'\n        },\n        'FULL_VIEWER': {\n            name: t('roles.FULL_VIEWER'),\n            color: '#17a2b8',\n            icon: '👓'\n        },\n        'DATA_ENTRY': {\n            name: t('roles.DATA_ENTRY'),\n            color: '#fd7e14',\n            icon: '📋'\n        },\n        'MAP_SCHEDULER': {\n            name: t('roles.MAP_SCHEDULER'),\n            color: '#20c997',\n            icon: '🗺️'\n        },\n        'VIEWER': {\n            name: t('roles.VIEWER'),\n            color: '#6c757d',\n            icon: '👁️'\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            fetchUsers();\n        }\n    }[\"AdminDashboard.useEffect\"], []);\n    // تحديث بيانات التعديل عند اختيار مستخدم\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdminDashboard.useEffect\": ()=>{\n            if (editingUser) {\n                setEditUserData({\n                    id: editingUser.id,\n                    username: editingUser.username,\n                    password: '',\n                    name: editingUser.name,\n                    email: editingUser.email || '',\n                    phone: editingUser.phone || '',\n                    role: editingUser.role,\n                    isActive: editingUser.isActive\n                });\n            }\n        }\n    }[\"AdminDashboard.useEffect\"], [\n        editingUser\n    ]);\n    const fetchUsers = async ()=>{\n        try {\n            const response = await fetch('/api/users');\n            const result = await response.json();\n            if (result.success) {\n                setUsers(result.users);\n            } else {\n                showErrorToast('serverConnection');\n            }\n        } catch (error) {\n            console.error('Error fetching users:', error);\n            showErrorToast('serverConnection');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAddUser = async (e)=>{\n        e.preventDefault();\n        if (!newUser.username || !newUser.password || !newUser.name) {\n            showErrorToast('invalidData');\n            return;\n        }\n        try {\n            const response = await fetch('/api/users', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(newUser)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userCreated');\n                setUsers([\n                    ...users,\n                    result.user\n                ]);\n                setShowAddUser(false);\n                setNewUser({\n                    username: '',\n                    password: '',\n                    name: '',\n                    email: '',\n                    phone: '',\n                    role: 'VIEWER'\n                });\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error adding user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (!confirm(t('admin.confirmDelete'))) return;\n        try {\n            const response = await fetch(\"/api/users?id=\".concat(userId), {\n                method: 'DELETE'\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userDeleted');\n                setUsers(users.filter((u)=>u.id !== userId));\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error deleting user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const handleUpdateUser = async (e)=>{\n        e.preventDefault();\n        if (!editUserData.username || !editUserData.name) {\n            showErrorToast('invalidData');\n            return;\n        }\n        try {\n            // إذا كانت كلمة المرور فارغة، لا نرسلها للتحديث\n            const userData = {\n                ...editUserData\n            };\n            if (!userData.password) {\n                delete userData.password;\n            }\n            const response = await fetch(\"/api/users?id=\".concat(userData.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(userData)\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('userUpdated');\n                setEditingUser(null);\n                fetchUsers();\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error updating user:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const inputStyle = {\n        width: '100%',\n        padding: '12px',\n        border: '2px solid #e0e0e0',\n        borderRadius: '8px',\n        fontSize: '1rem',\n        fontFamily: 'Cairo, Arial, sans-serif',\n        direction: isRTL ? 'rtl' : 'ltr',\n        outline: 'none'\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: [\n                        \"⏳ \",\n                        t('admin.loadingData')\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n            lineNumber: 226,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredRole: \"ADMIN\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                title: t('admin.title'),\n                subtitle: t('admin.subtitle'),\n                icon: \"\\uD83D\\uDC65\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'grid',\n                            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                            gap: '20px',\n                            marginBottom: '20px'\n                        },\n                        children: Object.entries(roles).map((param)=>{\n                            let [roleKey, roleInfo] = param;\n                            const count = users.filter((u)=>u.role === roleKey).length;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    borderRadius: '15px',\n                                    padding: '20px',\n                                    border: '1px solid #6b7280',\n                                    textAlign: 'center'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '2rem',\n                                            marginBottom: '10px'\n                                        },\n                                        children: roleInfo.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 5px 0',\n                                            fontSize: '1rem'\n                                        },\n                                        children: roleInfo.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '1.8rem',\n                                            fontWeight: 'bold',\n                                            color: roleInfo.color\n                                        },\n                                        children: count\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        style: {\n                                            color: '#d1d5db',\n                                            fontSize: '0.8rem',\n                                            margin: 0\n                                        },\n                                        children: t('common.user')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, roleKey, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '25px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            fontSize: '1.5rem',\n                                            margin: 0\n                                        },\n                                        children: [\n                                            \"\\uD83D\\uDC65 \",\n                                            t('admin.userManagement'),\n                                            \" (\",\n                                            users.length,\n                                            \")\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowAddUser(true),\n                                        style: {\n                                            background: 'linear-gradient(45deg, #10b981, #059669)',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '10px',\n                                            padding: '12px 20px',\n                                            cursor: 'pointer',\n                                            fontSize: '1rem',\n                                            fontWeight: 'bold'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('admin.addNewUser')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this),\n                            showAddUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    borderRadius: '15px',\n                                    padding: '25px',\n                                    marginBottom: '25px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('admin.addNewUser')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleAddUser,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    flexDirection: 'column',\n                                                    gap: '15px',\n                                                    marginBottom: '20px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.username'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t('admin.username'),\n                                                                value: newUser.username,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        username: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.password'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"password\",\n                                                                placeholder: t('admin.password'),\n                                                                value: newUser.password,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        password: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.fullName'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                placeholder: t('admin.fullName'),\n                                                                value: newUser.name,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        name: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: t('admin.email')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 408,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"email\",\n                                                                placeholder: t('admin.email'),\n                                                                value: newUser.email,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        email: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: t('admin.phone')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"tel\",\n                                                                placeholder: t('admin.phone'),\n                                                                value: newUser.phone,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        phone: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                style: {\n                                                                    display: 'block',\n                                                                    marginBottom: '5px',\n                                                                    color: '#f3f4f6',\n                                                                    fontWeight: 'bold'\n                                                                },\n                                                                children: [\n                                                                    t('admin.role'),\n                                                                    \" *\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: newUser.role,\n                                                                onChange: (e)=>setNewUser({\n                                                                        ...newUser,\n                                                                        role: e.target.value\n                                                                    }),\n                                                                style: {\n                                                                    ...inputStyle,\n                                                                    background: 'white',\n                                                                    color: '#333'\n                                                                },\n                                                                children: Object.entries(roles).map((param)=>{\n                                                                    let [key, role] = param;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: key,\n                                                                        children: role.name\n                                                                    }, key, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 25\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    gap: '10px',\n                                                    justifyContent: 'center'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        style: {\n                                                            background: 'linear-gradient(45deg, #10b981, #059669)',\n                                                            color: 'white',\n                                                            border: 'none',\n                                                            borderRadius: '8px',\n                                                            padding: '12px 25px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: [\n                                                            \"✅ \",\n                                                            t('admin.createUser')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>{\n                                                            setShowAddUser(false);\n                                                            setNewUser({\n                                                                username: '',\n                                                                password: '',\n                                                                name: '',\n                                                                email: '',\n                                                                phone: '',\n                                                                role: 'VIEWER'\n                                                            });\n                                                        },\n                                                        style: {\n                                                            background: '#6c757d',\n                                                            color: 'white',\n                                                            border: 'none',\n                                                            borderRadius: '8px',\n                                                            padding: '12px 25px',\n                                                            cursor: 'pointer',\n                                                            fontSize: '1rem',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: [\n                                                            \"❌ \",\n                                                            t('admin.cancel')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    overflowX: 'auto'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse',\n                                        color: '#f3f4f6',\n                                        fontSize: '0.95rem'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#374151',\n                                                    borderBottom: '2px solid #4b5563'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('common.user')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('admin.role')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: isRTL ? 'right' : 'left'\n                                                        },\n                                                        children: t('admin.lastLogin')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '15px 10px',\n                                                            textAlign: 'center'\n                                                        },\n                                                        children: t('admin.actions')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 508,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: users.map((user)=>{\n                                                var _roles_user_role, _roles_user_role1, _roles_user_role2;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    style: {\n                                                        borderBottom: '1px solid #4b5563',\n                                                        background: '#2d3748'\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    alignItems: 'center',\n                                                                    gap: '10px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        style: {\n                                                                            width: '40px',\n                                                                            height: '40px',\n                                                                            borderRadius: '50%',\n                                                                            background: ((_roles_user_role = roles[user.role]) === null || _roles_user_role === void 0 ? void 0 : _roles_user_role.color) || '#6c757d',\n                                                                            display: 'flex',\n                                                                            alignItems: 'center',\n                                                                            justifyContent: 'center',\n                                                                            fontSize: '1.2rem'\n                                                                        },\n                                                                        children: ((_roles_user_role1 = roles[user.role]) === null || _roles_user_role1 === void 0 ? void 0 : _roles_user_role1.icon) || '👤'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 527,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold'\n                                                                                },\n                                                                                children: user.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                                lineNumber: 540,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.85rem',\n                                                                                    color: '#9ca3af'\n                                                                                },\n                                                                                children: [\n                                                                                    \"@\",\n                                                                                    user.username\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        display: 'inline-block',\n                                                                        padding: '5px 10px',\n                                                                        borderRadius: '20px',\n                                                                        background: ((_roles_user_role2 = roles[user.role]) === null || _roles_user_role2 === void 0 ? void 0 : _roles_user_role2.color) || '#6c757d',\n                                                                        color: 'white',\n                                                                        fontSize: '0.85rem',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: getRoleName(user.role)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '0.8rem',\n                                                                        color: '#9ca3af',\n                                                                        marginTop: '5px'\n                                                                    },\n                                                                    children: tRoleDesc(user.role).substring(0, 50) + (tRoleDesc(user.role).length > 50 ? '...' : '')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 545,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px'\n                                                            },\n                                                            children: user.lastLogin ? new Date(user.lastLogin).toLocaleString(isRTL ? 'ar-SA' : 'en-US') : t('admin.noLoginYet')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                padding: '15px 10px',\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    display: 'flex',\n                                                                    gap: '10px',\n                                                                    justifyContent: 'center'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            // تحرير المستخدم\n                                                                            setEditingUser(user);\n                                                                        },\n                                                                        style: {\n                                                                            background: '#3b82f6',\n                                                                            color: 'white',\n                                                                            border: 'none',\n                                                                            borderRadius: '5px',\n                                                                            padding: '8px 12px',\n                                                                            cursor: 'pointer'\n                                                                        },\n                                                                        children: \"✏️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDeleteUser(user.id),\n                                                                        disabled: user.role === 'ADMIN' && user.id === '1',\n                                                                        style: {\n                                                                            background: user.role === 'ADMIN' && user.id === '1' ? '#6c757d' : '#ef4444',\n                                                                            color: 'white',\n                                                                            border: 'none',\n                                                                            borderRadius: '5px',\n                                                                            padding: '8px 12px',\n                                                                            cursor: user.role === 'ADMIN' && user.id === '1' ? 'not-allowed' : 'pointer',\n                                                                            opacity: user.role === 'ADMIN' && user.id === '1' ? 0.5 : 1\n                                                                        },\n                                                                        children: \"\\uD83D\\uDDD1️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 582,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 565,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, user.id, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 19\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 11\n                            }, this),\n                            users.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    textAlign: 'center',\n                                    padding: '30px',\n                                    color: '#9ca3af'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            fontSize: '3rem',\n                                            marginBottom: '10px'\n                                        },\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 611,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: t('admin.noUsers')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: t('admin.addUsersMessage')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '30px',\n                            border: '1px solid #6b7280',\n                            marginTop: '30px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    fontSize: '1.5rem',\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDD11 \",\n                                    t('admin.rolesExplanation')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',\n                                    gap: '20px'\n                                },\n                                children: Object.entries(roles).map((param)=>{\n                                    let [key, role] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            background: '#2d3748',\n                                            borderRadius: '10px',\n                                            padding: '20px',\n                                            border: '1px solid #4b5563'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    gap: '10px',\n                                                    marginBottom: '15px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            width: '40px',\n                                                            height: '40px',\n                                                            borderRadius: '50%',\n                                                            background: role.color,\n                                                            display: 'flex',\n                                                            alignItems: 'center',\n                                                            justifyContent: 'center',\n                                                            fontSize: '1.5rem'\n                                                        },\n                                                        children: role.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            fontWeight: 'bold',\n                                                            fontSize: '1.2rem',\n                                                            color: '#f3f4f6'\n                                                        },\n                                                        children: getRoleName(key)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '0.9rem',\n                                                    color: '#d1d5db',\n                                                    marginBottom: '15px',\n                                                    lineHeight: '1.5'\n                                                },\n                                                children: tRoleDesc(key)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 673,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    background: '#1f2937',\n                                                    borderRadius: '8px',\n                                                    padding: '10px',\n                                                    fontSize: '0.85rem',\n                                                    color: '#9ca3af'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            marginBottom: '5px',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            t('admin.permissions'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 689,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'flex',\n                                                            flexWrap: 'wrap',\n                                                            gap: '5px'\n                                                        },\n                                                        children: [\n                                                            key === 'ADMIN' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    background: '#dc3545',\n                                                                    color: 'white',\n                                                                    padding: '3px 8px',\n                                                                    borderRadius: '5px',\n                                                                    fontSize: '0.75rem'\n                                                                },\n                                                                children: t('admin.allPermissions')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            key === 'CONTENT_MANAGER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#28a745',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: t('admin.mediaManagement')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 704,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: t('admin.scheduleManagement')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 713,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'MEDIA_MANAGER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#28a745',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: t('admin.viewSchedules')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 735,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'SCHEDULER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 748,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: t('admin.viewMedia')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 757,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'FULL_VIEWER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 770,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 779,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الخريطة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 788,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض البث\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 797,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'DATA_ENTRY' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    background: '#28a745',\n                                                                    color: 'white',\n                                                                    padding: '3px 8px',\n                                                                    borderRadius: '5px',\n                                                                    fontSize: '0.75rem'\n                                                                },\n                                                                children: \"إدارة المواد\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                lineNumber: 809,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            key === 'MAP_SCHEDULER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#20c997',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الخريطة\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 821,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#007bff',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"إدارة الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 830,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true),\n                                                            key === 'VIEWER' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض المواد\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 852,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            background: '#6c757d',\n                                                                            color: 'white',\n                                                                            padding: '3px 8px',\n                                                                            borderRadius: '5px',\n                                                                            fontSize: '0.75rem'\n                                                                        },\n                                                                        children: \"عرض الجداول\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                        lineNumber: 690,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                lineNumber: 634,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 9\n                    }, this),\n                    editingUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: 'fixed',\n                            top: 0,\n                            left: 0,\n                            right: 0,\n                            bottom: 0,\n                            background: 'rgba(0, 0, 0, 0.8)',\n                            display: 'flex',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            zIndex: 1000,\n                            padding: '20px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '15px',\n                                padding: '30px',\n                                width: '100%',\n                                maxWidth: '600px',\n                                maxHeight: '90vh',\n                                overflowY: 'auto',\n                                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',\n                                position: 'relative'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setEditingUser(null),\n                                    style: {\n                                        position: 'absolute',\n                                        top: '15px',\n                                        left: '15px',\n                                        background: 'none',\n                                        border: 'none',\n                                        color: '#f3f4f6',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer'\n                                    },\n                                    children: \"✖️\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        color: '#f3f4f6',\n                                        textAlign: 'center',\n                                        marginBottom: '25px',\n                                        fontSize: '1.5rem'\n                                    },\n                                    children: [\n                                        \"✏️ \",\n                                        t('admin.editingUser'),\n                                        \": \",\n                                        editingUser.name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 921,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleUpdateUser,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'grid',\n                                                gridTemplateColumns: '1fr 1fr',\n                                                gap: '20px',\n                                                marginBottom: '20px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.username'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 938,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editUserData.username,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    username: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 941,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 937,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.password'),\n                                                                \" \",\n                                                                t('admin.passwordNote')\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"password\",\n                                                            value: editUserData.password,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    password: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 958,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 954,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.fullName'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 971,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editUserData.name,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    name: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 974,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 970,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: t('admin.email')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            value: editUserData.email,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    email: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 987,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: t('admin.phone')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1004,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"tel\",\n                                                            value: editUserData.phone,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    phone: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1007,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1003,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.role'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: editUserData.role,\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    role: e.target.value\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true,\n                                                            children: Object.entries(roles).map((param)=>{\n                                                                let [key, role] = param;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: key,\n                                                                    children: role.name\n                                                                }, key, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1034,\n                                                                    columnNumber: 25\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1023,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1019,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontWeight: 'bold'\n                                                            },\n                                                            children: [\n                                                                t('admin.status'),\n                                                                \" *\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: editUserData.isActive.toString(),\n                                                            onChange: (e)=>setEditUserData({\n                                                                    ...editUserData,\n                                                                    isActive: e.target.value === 'true'\n                                                                }),\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: 'white',\n                                                                color: '#333'\n                                                            },\n                                                            required: true,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"true\",\n                                                                    children: t('admin.active')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1055,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: \"false\",\n                                                                    children: t('admin.inactive')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                                    lineNumber: 1056,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                            lineNumber: 1045,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                justifyContent: 'center',\n                                                gap: '15px',\n                                                marginTop: '30px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    style: {\n                                                        background: '#3b82f6',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: [\n                                                        \"\\uD83D\\uDCBE \",\n                                                        t('admin.saveChanges')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>setEditingUser(null),\n                                                    style: {\n                                                        background: '#6c757d',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '8px',\n                                                        padding: '12px 25px',\n                                                        cursor: 'pointer',\n                                                        fontSize: '1rem',\n                                                        fontWeight: 'bold'\n                                                    },\n                                                    children: [\n                                                        \"❌ \",\n                                                        t('common.cancel')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                                    lineNumber: 1083,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                            lineNumber: 1061,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                            lineNumber: 894,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                        lineNumber: 881,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n                lineNumber: 1105,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\admin-dashboard\\\\page.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminDashboard, \"1Bw1/P3KxhWkAL6qJFk6Df16T+A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_6__.useAppTranslation\n    ];\n});\n_c = AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin-dashboard/page.tsx\n"));

/***/ })

});