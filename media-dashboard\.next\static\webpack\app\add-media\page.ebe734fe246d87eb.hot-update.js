"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast)();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        description: '',\n        channel: '',\n        source: '',\n        status: '',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: '',\n        showInTX: false\n    });\n    // إضافة حالة لإظهار/إخفاء الحقول الخاصة حسب نوع المادة\n    const [showEpisodeNumber, setShowEpisodeNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSeasonNumber, setShowSeasonNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPartNumber, setShowPartNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث الحقول المرئية عند تغيير نوع المادة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddMediaPage.useEffect\": ()=>{\n            console.log('🔍 نوع المادة المختار:', formData.type);\n            if (formData.type === 'FILM') {\n                // فيلم: يظهر رقم الجزء فقط\n                console.log('✅ عرض حقول الفيلم: رقم الجزء فقط');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'SERIES') {\n                // مسلسل: يظهر رقم الحلقة ورقم الجزء\n                console.log('✅ عرض حقول المسلسل: رقم الحلقة + رقم الجزء');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'PROGRAM') {\n                // برنامج: يظهر رقم الحلقة ورقم الموسم\n                console.log('✅ عرض حقول البرنامج: رقم الحلقة + رقم الموسم');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(true);\n                setShowPartNumber(false);\n            } else {\n                // باقي الأنواع: لا تظهر حقول إضافية\n                console.log('❌ إخفاء جميع الحقول الإضافية');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n            }\n            console.log('📊 حالة الحقول:', {\n                showEpisodeNumber,\n                showSeasonNumber,\n                showPartNumber\n            });\n        }\n    }[\"AddMediaPage.useEffect\"], [\n        formData.type\n    ]);\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: '',\n            timeIn: '00:00:00',\n            timeOut: '',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: field === 'showInTX' ? value === 'true' : value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n        // التحقق من كود السيجمانت عند تغييره\n        if (field === 'segmentCode' && value.trim()) {\n            validateSegmentCode(segmentId, value.trim());\n        } else if (field === 'segmentCode' && !value.trim()) {\n            // إزالة التحقق إذا كان الكود فارغ\n            setSegmentCodeValidation((prev)=>{\n                const newValidation = {\n                    ...prev\n                };\n                delete newValidation[segmentId];\n                return newValidation;\n            });\n        }\n    };\n    // دالة التحقق من كود السيجمانت\n    const validateSegmentCode = async (segmentId, code)=>{\n        // تحديث حالة التحقق\n        setSegmentCodeValidation((prev)=>({\n                ...prev,\n                [segmentId]: {\n                    isValid: true,\n                    message: '',\n                    isChecking: true\n                }\n            }));\n        try {\n            const response = await fetch(\"/api/validate-segment-code?code=\".concat(encodeURIComponent(code)));\n            const result = await response.json();\n            if (result.success) {\n                const isValid = !result.isDuplicate;\n                const message = result.isDuplicate ? \"الكود موجود في: \".concat(result.duplicates.map((d)=>d.mediaName).join(', ')) : 'الكود متاح';\n                setSegmentCodeValidation((prev)=>({\n                        ...prev,\n                        [segmentId]: {\n                            isValid,\n                            message,\n                            isChecking: false\n                        }\n                    }));\n            }\n        } catch (error) {\n            console.error('Error validating segment code:', error);\n            setSegmentCodeValidation((prev)=>({\n                    ...prev,\n                    [segmentId]: {\n                        isValid: true,\n                        message: 'خطأ في التحقق',\n                        isChecking: false\n                    }\n                }));\n        }\n    };\n    const calculateDuration = (segmentId)=>{\n        const segment = segments.find((s)=>s.id === segmentId);\n        if (!segment || !segment.timeIn || !segment.timeOut) return;\n        try {\n            const timeIn = segment.timeIn.split(':').map(Number);\n            const timeOut = segment.timeOut.split(':').map(Number);\n            // تحويل إلى ثواني\n            const inSeconds = timeIn[0] * 3600 + timeIn[1] * 60 + timeIn[2];\n            const outSeconds = timeOut[0] * 3600 + timeOut[1] * 60 + timeOut[2];\n            // حساب الفرق\n            let durationSeconds = outSeconds - inSeconds;\n            if (durationSeconds < 0) {\n                durationSeconds += 24 * 3600; // إضافة يوم كامل إذا كان الوقت سالب\n            }\n            // تحويل إلى تنسيق HH:MM:SS\n            const hours = Math.floor(durationSeconds / 3600);\n            const minutes = Math.floor(durationSeconds % 3600 / 60);\n            const seconds = durationSeconds % 60;\n            const duration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n            handleSegmentChange(segmentId, 'duration', duration);\n        } catch (error) {\n            console.error('Error calculating duration:', error);\n        }\n    };\n    const addSegment = ()=>{\n        const newId = segmentCount + 1;\n        setSegmentCount(newId);\n        setSegments((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    segmentCode: '',\n                    timeIn: '00:00:00',\n                    timeOut: '00:00:00',\n                    duration: '00:00:00'\n                }\n            ]);\n    };\n    const removeSegment = (id)=>{\n        if (segments.length <= 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegments((prev)=>prev.filter((segment)=>segment.id !== id));\n    };\n    const setSegmentCount2 = (count)=>{\n        if (count < 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            newSegments.push({\n                id: i,\n                segmentCode: '',\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [segmentCodeValidation, setSegmentCodeValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // التحقق من الحقول المطلوبة\n        const requiredFields = [];\n        if (!formData.hardDiskNumber.trim()) requiredFields.push('رقم الهارد');\n        if (!formData.type) requiredFields.push('نوع المادة');\n        if (!formData.channel) requiredFields.push('القناة');\n        if (!formData.status) requiredFields.push('الحالة');\n        // التحقق من كود السيجمانت\n        const segmentsWithoutCode = segments.filter((segment)=>!segment.segmentCode || segment.segmentCode.trim() === '');\n        if (segmentsWithoutCode.length > 0) {\n            requiredFields.push('كود السيجمانت (مطلوب لجميع السيجمانتات)');\n        }\n        // التحقق من Time Out للسيجمانت\n        const invalidSegments = segments.filter((segment)=>!segment.timeOut || segment.timeOut === '00:00:00');\n        if (invalidSegments.length > 0) {\n            requiredFields.push('Time Out للسيجمانت');\n        }\n        if (requiredFields.length > 0) {\n            showErrorToast('invalidData');\n            return;\n        }\n        // التحقق من تكرار أكواد السيجمانت\n        const segmentCodes = segments.map((s)=>s.segmentCode).filter((code)=>code && code.trim());\n        try {\n            const validationResponse = await fetch('/api/validate-segment-code', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    segmentCodes\n                })\n            });\n            const validationResult = await validationResponse.json();\n            if (validationResult.success && validationResult.hasAnyDuplicates) {\n                let errorMessage = 'تم العثور على أكواد مكررة:\\n\\n';\n                if (validationResult.hasInternalDuplicates) {\n                    errorMessage += \"\\uD83D\\uDD34 أكواد مكررة داخل نفس المادة: \".concat(validationResult.internalDuplicates.join(', '), \"\\n\\n\");\n                }\n                if (validationResult.hasExternalDuplicates) {\n                    errorMessage += '🔴 أكواد موجودة في مواد أخرى:\\n';\n                    validationResult.externalDuplicates.forEach((dup)=>{\n                        errorMessage += '- الكود \"'.concat(dup.segmentCode, '\" موجود في المادة: ').concat(dup.mediaName, \" (\").concat(dup.mediaType, \")\\n\");\n                    });\n                }\n                errorMessage += '\\nيرجى تغيير الأكواد المكررة قبل الحفظ.';\n                showErrorToast('invalidData');\n                return;\n            }\n        } catch (validationError) {\n            console.error('Error validating segment codes:', validationError);\n            showErrorToast('invalidData');\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = \"token_\".concat(user.id || 'unknown', \"_\").concat(user.role || 'unknown');\n            console.log('Sending with token:', tokenWithRole);\n            console.log('User data:', user);\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(tokenWithRole)\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('mediaAdded');\n                // مسح جميع الحقول للبدء من جديد\n                setFormData({\n                    name: '',\n                    type: '',\n                    description: '',\n                    channel: '',\n                    source: '',\n                    status: '',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: '',\n                    showInTX: false\n                });\n                // مسح السيجمانتات والعودة لسيجمانت واحد\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: '',\n                        timeIn: '00:00:00',\n                        timeOut: '',\n                        duration: ''\n                    }\n                ]);\n                // إخفاء الحقول الخاصة\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n                console.log('✅ تم مسح جميع الحقول بنجاح');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error saving media:', error);\n            showErrorToast('serverConnection');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // أنماط CSS\n    const inputStyle = {\n        width: '100%',\n        padding: '10px',\n        borderRadius: '5px',\n        marginBottom: '10px'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: t('media.addNew'),\n        subtitle: t('media.title'),\n        icon: \"➕\",\n        requiredPermissions: [\n            'MEDIA_CREATE'\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '15px',\n                    padding: '20px',\n                    marginBottom: '25px',\n                    border: '1px solid #6b7280',\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#ffffff',\n                            marginBottom: '10px',\n                            fontSize: '1.4rem',\n                            fontWeight: 'bold'\n                        },\n                        children: [\n                            \"➕ \",\n                            t('addMedia.title')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#e2e8f0',\n                            fontSize: '0.95rem',\n                            margin: 0\n                        },\n                        children: t('addMedia.subtitle')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 376,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDD \",\n                                    t('addMedia.basicInfo')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 1fr',\n                                            gap: '15px',\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    fontWeight: 'bold',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCBE \",\n                                                    t('addMedia.hardDiskNumber'),\n                                                    \": \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 54\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('addMedia.hardDiskNumber'),\n                                                value: formData.hardDiskNumber,\n                                                onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    maxWidth: '200px',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '500px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    t('common.name'),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 38\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('common.name'),\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                },\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('common.type'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 40\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectType')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILM\",\n                                                                children: t('mediaTypes.FILM')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: t('mediaTypes.SERIES')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: t('mediaTypes.PROGRAM')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: t('mediaTypes.SONG')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: t('mediaTypes.FILLER')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: t('mediaTypes.STING')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: t('mediaTypes.PROMO')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEXT\",\n                                                                children: t('mediaTypes.NEXT')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NOW\",\n                                                                children: t('mediaTypes.NOW')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"سنعود\",\n                                                                children: t('mediaTypes.سنعود')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"عدنا\",\n                                                                children: t('mediaTypes.عدنا')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MINI\",\n                                                                children: t('mediaTypes.MINI')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CROSS\",\n                                                                children: t('mediaTypes.CROSS')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('addMedia.channel'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectChannel')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: t('channels.DOCUMENTARY')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: t('channels.NEWS')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: t('channels.OTHER')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('common.status'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectStatus')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: t('mediaStatus.VALID')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_CENSORSHIP\",\n                                                                children: t('mediaStatus.REJECTED_CENSORSHIP')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_TECHNICAL\",\n                                                                children: t('mediaStatus.REJECTED_TECHNICAL')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: t('mediaStatus.EXPIRED')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"HOLD\",\n                                                                children: t('mediaStatus.HOLD')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.source')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: t('addMedia.source'),\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.startDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.startDate,\n                                                        onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.endDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.endDate,\n                                                        onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            showEpisodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.episodeNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.episodeNumber'),\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, this),\n                                            showSeasonNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.seasonNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.seasonNumber'),\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 19\n                                            }, this),\n                                            showPartNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.partNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.partNumber'),\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.description')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.description'),\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.notes')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.additionalNotes'),\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.showInTX,\n                                                        onChange: (e)=>handleInputChange('showInTX', e.target.checked.toString()),\n                                                        style: {\n                                                            marginLeft: '10px',\n                                                            width: '18px',\n                                                            height: '18px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            background: '#10b981',\n                                                            color: 'white',\n                                                            padding: '2px 8px',\n                                                            borderRadius: '4px',\n                                                            marginLeft: '10px',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"TX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t('addMedia.showInSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '0.8rem',\n                                                    color: '#9ca3af',\n                                                    marginTop: '5px',\n                                                    marginRight: '35px'\n                                                },\n                                                children: t('addMedia.txDescription')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('addMedia.segments')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: '#3b82f6',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    padding: '8px 15px',\n                                                    marginLeft: '10px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: t('addMedia.addSegment')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                value: segmentCount,\n                                                onChange: (e)=>setSegmentCount2(parseInt(e.target.value)),\n                                                style: {\n                                                    width: '60px',\n                                                    padding: '8px',\n                                                    borderRadius: '5px',\n                                                    border: '1px solid #6b7280',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    textAlign: 'center'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 13\n                            }, this),\n                            segments.map((segment, index)=>{\n                                var _segmentCodeValidation_segment_id, _segmentCodeValidation_segment_id1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#374151',\n                                        borderRadius: '10px',\n                                        padding: '15px',\n                                        marginBottom: '15px',\n                                        border: '1px solid #4b5563'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                justifyContent: 'space-between',\n                                                alignItems: 'center',\n                                                marginBottom: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#f3f4f6',\n                                                        margin: 0\n                                                    },\n                                                    children: [\n                                                        t('addMedia.segment'),\n                                                        \" \",\n                                                        segment.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeSegment(segment.id),\n                                                    style: {\n                                                        background: '#ef4444',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '5px',\n                                                        padding: '5px 10px',\n                                                        cursor: 'pointer'\n                                                    },\n                                                    children: t('common.delete')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'grid',\n                                                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                gap: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.segmentCode')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: 'relative'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: segment.segmentCode,\n                                                                    onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                    placeholder: \"مثال: DDC000055-P1-3\",\n                                                                    required: true,\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: segment.segmentCode ? '#1f2937' : '#7f1d1d',\n                                                                        color: 'white',\n                                                                        border: ((_segmentCodeValidation_segment_id = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id === void 0 ? void 0 : _segmentCodeValidation_segment_id.isValid) === false ? '2px solid #ef4444' : segment.segmentCode ? ((_segmentCodeValidation_segment_id1 = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id1 === void 0 ? void 0 : _segmentCodeValidation_segment_id1.isValid) === true ? '2px solid #10b981' : '1px solid #6b7280' : '2px solid #ef4444',\n                                                                        paddingRight: segmentCodeValidation[segment.id] ? '35px' : '12px'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                segmentCodeValidation[segment.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: 'absolute',\n                                                                        right: '8px',\n                                                                        top: '50%',\n                                                                        transform: 'translateY(-50%)',\n                                                                        fontSize: '16px'\n                                                                    },\n                                                                    children: segmentCodeValidation[segment.id].isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#fbbf24'\n                                                                        },\n                                                                        children: \"⏳\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 829,\n                                                                        columnNumber: 29\n                                                                    }, this) : segmentCodeValidation[segment.id].isValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#10b981'\n                                                                        },\n                                                                        children: \"✅\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#ef4444'\n                                                                        },\n                                                                        children: \"❌\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        segmentCodeValidation[segment.id] && segmentCodeValidation[segment.id].message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                marginTop: '4px',\n                                                                color: segmentCodeValidation[segment.id].isValid ? '#10b981' : '#ef4444'\n                                                            },\n                                                            children: segmentCodeValidation[segment.id].message\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: \"Time In\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: [\n                                                                \"Time Out \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        color: '#ef4444'\n                                                                    },\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 869,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            onFocus: (e)=>{\n                                                                // إزالة القيمة الوهمية عند النقر\n                                                                if (e.target.value === '00:00:00') {\n                                                                    handleSegmentChange(segment.id, 'timeOut', '');\n                                                                }\n                                                            },\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.durationAutoCalculated')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.duration,\n                                                            readOnly: true,\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280',\n                                                                opacity: '0.7',\n                                                                cursor: 'not-allowed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, segment.id, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'center',\n                            gap: '15px',\n                            marginTop: '20px',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#10b981',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: isSubmitting ? \"⏳ \".concat(t('common.saving')) : \"\\uD83D\\uDCBE \".concat(t('addMedia.saveAndAddNew'))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>router.push('/media-list'),\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#3b82f6',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCB \",\n                                    t('navigation.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    // مسح جميع الحقول يدوياً\n                                    setFormData({\n                                        name: '',\n                                        type: '',\n                                        description: '',\n                                        channel: '',\n                                        source: '',\n                                        status: '',\n                                        startDate: new Date().toISOString().split('T')[0],\n                                        endDate: '',\n                                        notes: '',\n                                        episodeNumber: '',\n                                        seasonNumber: '',\n                                        partNumber: '',\n                                        hardDiskNumber: '',\n                                        showInTX: false\n                                    });\n                                    setSegmentCount(1);\n                                    setSegments([\n                                        {\n                                            id: 1,\n                                            segmentCode: '',\n                                            timeIn: '00:00:00',\n                                            timeOut: '',\n                                            duration: ''\n                                        }\n                                    ]);\n                                    setShowEpisodeNumber(false);\n                                    setShowSeasonNumber(false);\n                                    setShowPartNumber(false);\n                                    showSuccessToast('changesSaved');\n                                },\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#f59e0b',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDDD1️ \",\n                                    t('addMedia.clearFields')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 955,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 392,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 1011,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"zD8bHefytEBpMmtUNRBoYQorTRw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});