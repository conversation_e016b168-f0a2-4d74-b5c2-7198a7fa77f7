/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxwcm9qZWN0IHNwb3J0XFxcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcXFxtZWRpYS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\dashboard\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQTZIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxwcm9qZWN0IHNwb3J0XFxcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcXFxtZWRpYS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./public/locales/ar/common.json":
/*!***************************************!*\
  !*** ./public/locales/ar/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"navigation":{"dashboard":"لوحة التحكم","mediaList":"المواد الإعلامية","addMedia":"إضافة مادة","weeklySchedule":"الخريطة البرامجية","dailySchedule":"جدول الإذاعة اليومي","reports":"تقارير البث","unifiedSystem":"استيراد/تصدير","adminDashboard":"المستخدمين","statistics":"الإحصائيات"},"common":{"welcome":"مرحباً","loading":"جاري التحميل...","loadingSchedule":"جاري تحميل الجدول الأسبوعي...","loadingData":"جاري تحميل البيانات...","save":"حفظ","cancel":"إلغاء","delete":"حذف","edit":"تعديل","add":"إضافة","search":"بحث","filter":"فلترة","export":"تصدير","import":"استيراد","yes":"نعم","no":"لا","ok":"موافق","close":"إغلاق","back":"رجوع","next":"التالي","previous":"السابق","submit":"إرسال","reset":"إعادة تعيين","clear":"مسح","select":"اختيار","selectDate":"اختر التاريخ","selectTime":"اختر الوقت","actions":"الإجراءات","status":"الحالة","type":"النوع","name":"الاسم","description":"الوصف","duration":"المدة","startTime":"وقت البداية","endTime":"وقت النهاية","date":"التاريخ","time":"الوقت","content":"المحتوى","code":"الكود","episode":"الحلقة","season":"الموسم","part":"الجزء","segments":"السيجمنتات","available":"متاح","unavailable":"غير متاح","active":"نشط","inactive":"غير نشط","valid":"صالح","invalid":"غير صالح","expired":"منتهي الصلاحية","pending":"في الانتظار","approved":"موافق عليه","rejected":"مرفوض","total":"الإجمالي","count":"العدد","items":"عنصر","user":"مستخدم","noData":"لا توجد بيانات","noResults":"لا توجد نتائج","error":"خطأ","success":"نجح","warning":"تحذير","info":"معلومات"},"mediaTypes":{"ALL":"جميع الأنواع","PROGRAM":"برنامج","SERIES":"مسلسل","FILM":"فيلم","SONG":"أغنية","PROMO":"إعلان ترويجي","STING":"فاصل","FILLER":"مادة مالئة","NEXT":"التالي","NOW":"الآن","MINI":"Mini","CROSS":"Cross","سنعود":"سنعود","عدنا":"عدنا"},"mediaStatus":{"ALL":"جميع الحالات","VALID":"صالح","REJECTED_CENSORSHIP":"مرفوض رقابياً","REJECTED_TECHNICAL":"مرفوض هندسياً","EXPIRED":"منتهي الصلاحية","HOLD":"معلق"},"channels":{"DOCUMENTARY":"الوثائقية","NEWS":"الأخبار","OTHER":"أخرى"},"dashboard":{"title":"لوحة التحكم","subtitle":"نظام إدارة المواد الإعلامية","totalMedia":"إجمالي المواد","activeSchedules":"الجداول النشطة","todayBroadcast":"إذاعة اليوم","systemUsers":"مستخدمي النظام","recentActivity":"النشاط الأخير","quickActions":"إجراءات سريعة","statistics":"الإحصائيات","overview":"نظرة عامة"},"media":{"title":"إدارة المواد","addNew":"إضافة مادة جديدة","list":"قائمة المواد الإعلامية","details":"تفاصيل المادة","segments":"السيجمنتات","addSegment":"إضافة سيجمنت","segmentCode":"كود السيجمنت","timeIn":"وقت الدخول","timeOut":"وقت الخروج","hardDrive":"القرص الصلب","server":"الخادم","notes":"ملاحظات","startDate":"تاريخ البداية","endDate":"تاريخ النهاية","showInTX":"عرض في TX","episodeNumber":"رقم الحلقة","seasonNumber":"رقم الموسم","partNumber":"رقم الجزء","totalSegments":"إجمالي السيجمنتات","validMedia":"مواد صالحة","expiredMedia":"مواد منتهية الصلاحية","pendingMedia":"مواد في الانتظار","searchByName":"البحث بالاسم أو الوصف","searchByCode":"البحث بكود المادة أو السيجمنت","searchPlaceholder":"ابحث عن مادة إعلامية...","codePlaceholder":"ابحث بالكود...","mediaType":"نوع المادة","mediaStatus":"الحالة","sortBy":"ترتيب حسب","newest":"الأحدث أولاً","oldest":"الأقدم أولاً","byName":"الاسم (أ-ي)","byType":"النوع","searchStats":"عرض {{filtered}} من أصل {{total}} مادة إعلامية","noMediaFound":"لا توجد مواد إعلامية محفوظة","startAdding":"ابدأ بإضافة مادة إعلامية جديدة","exportExcel":"تصدير Excel","exporting":"جاري التصدير...","searchAndFilter":"البحث والفلترة","mediaOverview":"عرض وإدارة المحتوى الإعلامي","searchFilterExport":"يمكنك البحث والفلترة وتصدير البيانات","channel":"القناة","segmentCount":"عدد السيجمنت","description":"الوصف","noCode":"[لا يوجد كود]","edit":"تعديل","delete":"حذف","scrollToTop":"العودة لأعلى الصفحة"},"schedule":{"title":"إدارة الجداول","weekly":"الخريطة البرامجية الأسبوعية","daily":"الجدول الإذاعي اليومي","import":"استيراد جدول","export":"تصدير جدول","broadcast":"الإذاعة","rerun":"إعادة","prime":"برايم","filler":"مالئ","empty":"فارغ","addRow":"إضافة صف","deleteRow":"حذف صف","moveUp":"تحريك لأعلى","moveDown":"تحريك لأسفل","showMap":"عرض الخريطة","hideMap":"إخفاء الخريطة","saveChanges":"حفظ التعديلات","discardChanges":"تجاهل التعديلات","importFromTime":"استيراد من هذا الوقت","broadcastTime":"وقت الإذاعة","programMap":"خريطة البرامج","scheduleItems":"عناصر الجدول","availableMedia":"المواد المتاحة","scheduledMedia":"البرامج المجدولة اليوم","weeklySchedule":"الخريطة البرامجية الأسبوعية","weeklySubtitle":"جدولة البرامج الأسبوعية","importTitle":"استيراد الجدول الإذاعي","importSubtitle":"استيراد الجدول من وقت محدد","importInstructions":"اختر التاريخ → حدد الوقت → استيراد → تعديل المواد → تصدير Excel","importSchedule":"استيراد الجدول","importInstructionsLong":"اختر التاريخ والوقت ثم اضغط \\"استيراد من هذا الوقت\\" لعرض الجدول","loadingSchedule":"جاري تحميل الجدول الأسبوعي...","selectingDate":"جاري تحديد التاريخ...","mediaList":"قائمة المواد","addTempMedia":"إضافة مادة مؤقتة","mediaName":"اسم المادة...","duration":"المدة (مثل: 01:30:00)","notes":"ملاحظات (اختياري)...","add":"إضافة","updateReruns":"تحديث الإعادات","allTypes":"جميع الأنواع","searchMedia":"البحث في المواد...","resultsCount":"{{count}} من {{total}} مادة","noMedia":"لا توجد مواد","changeFilter":"جرب تغيير الفلتر أو البحث","addNewMedia":"أضف مواد جديدة من صفحة المستخدم","deleteTempMedia":"حذف المادة المؤقتة","liveProgram":"هواء","pendingDelivery":"قيد التسليم","temporary":"مؤقت","broadcastSchedule":"الجدول الإذاعي","exportSchedule":"تصدير الخريطة","selectedWeek":"الأسبوع المحدد","previousWeek":"← الأسبوع السابق","nextWeek":"الأسبوع التالي →","time":"الوقت","rerunIndicator":"إعادة - يمكن الحذف للتعديل","hideSchedule":"إخفاء الخريطة","showSchedule":"عرض الخريطة","unknown":"غير معروف","season":"الموسم","episode":"الحلقة","part":"الجزء","confirmDelete":"هل أنت متأكد من حذف {{type}}: \\"{{name}}\\"?","deleteWarningOriginal":"تحذير: حذف المادة الأصلية سيحذف جميع إعاداتها","deleteWarningRerun":"تحذير: حذف الإعادة لن يؤثر على المادة الأصلية","deleteWarningTemp":"سيتم حذف المادة المؤقتة من الجدول","originalMaterial":"مادة أصلية","rerunMaterial":"إعادة","tempMaterial":"مادة مؤقتة","timeConflict":"يوجد تداخل في الأوقات! اختر وقت آخر.","enterMediaName":"يرجى إدخال اسم المادة","confirmDeleteTemp":"هل تريد حذف هذه المادة المؤقتة؟","deleteMediaTitle":"🗑️ حذف المواد:","deleteOriginalInfo":"المواد الأصلية: حذف نهائي مع جميع إعاداتها","deleteRerunInfo":"الإعادات: حذف مع ترك الحقل فارغ للتعديل","deleteConfirmInfo":"سيظهر تأكيد قبل الحذف","confirmDeleteSegment":"هل أنت متأكد من حذف هذا السيجمنت؟","usageInstructions":"📋 تعليمات الاستخدام:","addMediaTitle":"🎯 إضافة المواد:","addMediaInstruction1":"اسحب المواد من القائمة اليمنى إلى الجدول","addMediaInstruction2":"🔄 اسحب المواد من الجدول نفسه لنسخها لمواعيد أخرى","addMediaInstruction3":"🎬 استخدم فلتر النوع للتصفية حسب نوع المادة","addMediaInstruction4":"🔍 استخدم البحث للعثور على المواد بسرعة","primeTimeTitle":"🌟 المواد الأصلية (البرايم تايم):","primeTimeSchedule1":"الأحد-الأربعاء: 18:00-00:00","primeTimeSchedule2":"الخميس-السبت: 18:00-02:00","primeTimeColor":"🟡 لون ذهبي في الجدول","rerunsTitle":"♻️ الإعادات التلقائية (جزئين):","rerunsSchedule1":"الأحد-الأربعاء:","rerunsSchedule2":"الخميس-السبت:","rerunsPart1Sun":"ج1: نفس العمود 00:00-07:59","rerunsPart2Sun":"ج2: العمود التالي 08:00-17:59","rerunsPart1Thu":"ج1: نفس العمود 02:00-07:59","rerunsPart2Thu":"ج2: العمود التالي 08:00-17:59","rerunsColor":"🔘 لون رمادي - يمكن حذفها للتعديل","dateManagementTitle":"📅 إدارة التواريخ:","dateManagementInfo":"استخدم الكالندر والأزرار للتنقل بين الأسابيع • كل أسبوع يُحفظ بشكل منفصل","importantNoteTitle":"💡 ملاحظة مهمة:","importantNoteInfo":"عند إضافة مواد قليلة (١-٣ مواد) في البرايم، ستظهر مع فواصل زمنية في الإعادات لتجنب التكرار المفرط. أضف المزيد من المواد للحصول على تنوع أكبر.","weeklyScheduleTitle":"الخريطة البرامجية","noWeeklyData":"لا توجد بيانات للخريطة البرامجية","types":{"program":"برنامج","series":"مسلسل","film":"فيلم","song":"أغنية","sting":"استنج","fillIn":"فيل إن","filler":"فيلر","promo":"برومو","next":"نكست","now":"ناو","snawod":"سنعود","odna":"عدنا","mini":"Mini","cross":"Cross"},"startTime":"وقت البداية"},"days":{"sunday":"الأحد","monday":"الاثنين","tuesday":"الثلاثاء","wednesday":"الأربعاء","thursday":"الخميس","friday":"الجمعة","saturday":"السبت"},"months":{"january":"يناير","february":"فبراير","march":"مارس","april":"أبريل","may":"مايو","june":"يونيو","july":"يوليو","august":"أغسطس","september":"سبتمبر","october":"أكتوبر","november":"نوفمبر","december":"ديسمبر"},"auth":{"login":"تسجيل الدخول","logout":"تسجيل الخروج","username":"اسم المستخدم","password":"كلمة المرور","loginButton":"دخول","loginError":"خطأ في تسجيل الدخول","accessDenied":"تم رفض الوصول","createUserError":"خطأ في إنشاء المستخدم","deleteUserSuccess":"تم حذف المستخدم بنجاح!","deleteUserError":"خطأ في حذف المستخدم","fillRequiredFields":"يرجى ملء جميع الحقول المطلوبة","updateUserSuccess":"تم تحديث المستخدم بنجاح!","updateUserError":"خطأ في تحديث المستخدم","fullName":"الاسم الكامل","email":"البريد الإلكتروني","phone":"رقم الهاتف","role":"الدور","permissions":"الصلاحيات","allPermissions":"جميع الصلاحيات","manageMedia":"إدارة المواد","manageSchedules":"إدارة الجداول","viewMedia":"عرض المواد","viewSchedules":"عرض الجداول","viewMap":"عرض الخريطة","viewBroadcast":"عرض البث","manageMap":"إدارة الخريطة","roles":{"admin":"مدير النظام","contentManager":"مدير المحتوى","mediaManager":"مدير قاعدة البيانات","scheduler":"مجدول البرامج","fullViewer":"مستخدم رؤية كاملة","dataEntry":"مدخل بيانات","mapScheduler":"مسؤول الخريطة والجدول","viewer":"مستخدم عرض","adminDesc":"صلاحيات كاملة لجميع أجزاء النظام + إدارة المستخدمين","contentManagerDesc":"إدارة كاملة للمواد والجداول (بدون إدارة المستخدمين)","mediaManagerDesc":"إدارة المواد الإعلامية فقط (إضافة، تعديل، حذف)","schedulerDesc":"إدارة الجداول الإذاعية والخريطة البرامجية فقط","fullViewerDesc":"رؤية التطبيق كامل بدون إمكانية التعديل أو الإضافة","dataEntryDesc":"إدخال البيانات والتعديل عليها فقط دون رؤية باقي التطبيق","mapSchedulerDesc":"إدارة الخريطة وجدول البث اليومي مع إمكانية رؤية قاعدة البيانات دون التعديل عليها","viewerDesc":"عرض المحتوى فقط بدون إمكانية التعديل أو الإضافة"},"invalidCredentials":"بيانات الدخول غير صحيحة","welcomeBack":"مرحباً بعودتك","pleaseLogin":"يرجى تسجيل الدخول للمتابعة","userRoles":"أدوار المستخدمين","adminDesc":"صلاحيات كاملة","contentManagerDesc":"إدارة المواد الإعلامية","schedulerDesc":"إدارة الجداول الإذاعية","viewerDesc":"تصفح فقط","sessionExpired":"انتهت صلاحية الجلسة","insufficientPermissions":"صلاحيات غير كافية","status":{"valid":"صالح","rejectedCensorship":"مرفوض رقابي","rejectedTechnical":"مرفوض هندسي","waiting":"في الانتظار"},"segments":"سيجمانت"},"admin":{"title":"إدارة المستخدمين","subtitle":"إضافة وتعديل المستخدمين","users":"المستخدمون","permissions":"الصلاحيات","addUser":"إضافة مستخدم","editUser":"تعديل مستخدم","deleteUser":"حذف مستخدم","userRole":"دور المستخدم","userStatus":"حالة المستخدم","lastLogin":"آخر دخول","createdAt":"تاريخ الإنشاء","updatedAt":"تاريخ التحديث","activeUsers":"المستخدمون النشطون","inactiveUsers":"المستخدمون غير النشطين","totalUsers":"إجمالي المستخدمين","loadingData":"جاري تحميل البيانات...","userManagement":"إدارة المستخدمين","addNewUser":"إضافة مستخدم جديد","username":"اسم المستخدم","password":"كلمة المرور","fullName":"الاسم الكامل","email":"البريد الإلكتروني","phone":"رقم الهاتف","role":"الدور","status":"الحالة","actions":"الإجراءات","active":"نشط","inactive":"غير نشط","createUser":"إنشاء المستخدم","cancel":"إلغاء","saveChanges":"حفظ التغييرات","edit":"تعديل","delete":"حذف","noUsers":"لا يوجد مستخدمين","addUsersMessage":"قم بإضافة مستخدمين جدد باستخدام زر \\"إضافة مستخدم جديد\\"","rolesExplanation":"شرح الأدوار والصلاحيات","allPermissions":"جميع الصلاحيات","mediaManagement":"إدارة المواد","scheduleManagement":"إدارة الجداول","viewMedia":"عرض المواد","viewSchedules":"عرض الجداول","viewMap":"عرض الخريطة","viewBroadcast":"عرض البث","mapManagement":"إدارة الخريطة","permissionsLabel":"الصلاحيات:","noLoginYet":"لم يسجل الدخول بعد","editingUser":"تعديل المستخدم","passwordNote":"(اتركها فارغة للاحتفاظ بالحالية)","confirmDelete":"هل أنت متأكد من حذف هذا المستخدم؟","userCreated":"تم إنشاء المستخدم بنجاح!","userUpdated":"تم تحديث المستخدم بنجاح!","userDeleted":"تم حذف المستخدم بنجاح!","fillRequired":"يرجى ملء جميع الحقول المطلوبة","createError":"خطأ في إنشاء المستخدم","updateError":"خطأ في تحديث المستخدم","deleteError":"خطأ في حذف المستخدم","fetchError":"خطأ في جلب بيانات المستخدمين","serverError":"خطأ في الاتصال بالخادم"},"permissions":{"MEDIA_READ":"قراءة المواد","MEDIA_CREATE":"إنشاء المواد","MEDIA_UPDATE":"تحديث المواد","MEDIA_DELETE":"حذف المواد","SCHEDULE_READ":"قراءة الجداول","SCHEDULE_CREATE":"إنشاء الجداول","SCHEDULE_UPDATE":"تحديث الجداول","SCHEDULE_DELETE":"حذف الجداول","USER_MANAGEMENT":"إدارة المستخدمين","SYSTEM_ADMIN":"إدارة النظام"},"stats":{"totalMedia":"إجمالي المواد","validMedia":"المواد الصالحة","maintenanceMedia":"قيد الصيانة","activeUsers":"المستخدمين النشطين","efficiency":"الكفاءة العامة","operationalCost":"التكلفة التشغيلية","processingTime":"متوسط وقت المعالجة","activeOperations":"العمليات النشطة","growthRate":"معدل النمو","healthRate":"معدل الصحة","issueRate":"معدل المشاكل","activityRate":"معدل النشاط","improvement":"تحسن","dailyAverage":"المتوسط اليومي","loadingDummyData":"تحميل البيانات الوهمية"},"messages":{"success":{"mediaAdded":"تم إضافة المادة بنجاح","mediaUpdated":"تم تحديث المادة بنجاح","mediaDeleted":"تم حذف المادة بنجاح","exportSuccess":"تم التصدير بنجاح","importSuccess":"تم الاستيراد بنجاح","scheduleUpdated":"تم تحديث الجدول بنجاح","userCreated":"تم إنشاء المستخدم بنجاح","userUpdated":"تم تحديث المستخدم بنجاح","userDeleted":"تم حذف المستخدم بنجاح","changesSaved":"تم حفظ التغييرات بنجاح"},"error":{"serverConnection":"خطأ في الاتصال بالخادم","mediaNotFound":"المادة غير موجودة","invalidData":"بيانات غير صحيحة","permissionDenied":"ليس لديك صلاحية","exportFailed":"فشل في التصدير","importFailed":"فشل في الاستيراد","unknownError":"حدث خطأ غير معروف","timeFormatError":"خطأ في تنسيق الوقت","calculationError":"خطأ في الحساب"},"info":{"loading":"جاري التحميل...","saving":"جاري الحفظ...","processing":"جاري المعالجة...","exporting":"جاري التصدير...","importing":"جاري الاستيراد..."}},"home":{"title":"Prime-X","subtitle":"نظام إدارة المواد الإعلامية","loading":"جاري التحميل...","autoRedirect":"أو انتظر للتوجه التلقائي للوحة التحكم","quickNavigation":"التنقل السريع","dashboard":"لوحة التحكم","dailySchedule":"الجدول الإذاعي اليومي","weeklySchedule":"الخريطة البرامجية","mediaList":"المواد الإعلامية","addMedia":"إضافة مادة","adminPanel":"إدارة المستخدمين"},"reports":{"title":"تقارير البث","subtitle":"بحث وإحصاء المواد المذاعة","searchFilters":"فلاتر البحث","mediaType":"نوع المادة","allTypes":"جميع الأنواع","mediaName":"اسم المادة","mediaCode":"كود المادة","source":"المصدر","both":"كلاهما","weekly":"الخريطة الأسبوعية","daily":"الجدول اليومي","dateFrom":"من تاريخ","dateTo":"إلى تاريخ","search":"بحث","exportExcel":"تصدير Excel","showStatistics":"عرض الإحصائيات","hideStatistics":"إخفاء الإحصائيات","searchResults":"نتائج البحث","noResults":"لا توجد نتائج","searchMessage":"استخدم الفلاتر أعلاه للبحث عن المواد المذاعة","resultsCount":"{{count}} نتيجة","statistics":"إحصائيات المواد","totalItems":"إجمالي المواد","totalDuration":"إجمالي المدة","count":"العدد","duration":"المدة","percentage":"النسبة المئوية","loadingMedia":"جاري تحميل المواد...","searchError":"خطأ في البحث","exportError":"خطأ في التصدير","exportSuccess":"تم التصدير بنجاح","date":"التاريخ","time":"الوقت","type":"النوع","name":"الاسم","code":"الكود","rerun":"إعادة","temporary":"مؤقت","original":"أصلي","programsFilmsSeries":"البرامج والأفلام والمسلسلات","promosFillersSting":"البرومو والفيلر والستنج","detailedStatistics":"إحصائيات مفصلة"},"unified":{"title":"النظام الموحد للاستيراد والتصدير","subtitle":"إدارة استيراد وتصدير بيانات المواد الإعلامية","importExport":"استيراد/تصدير","selectFile":"يرجى اختيار ملف","uploadFile":"رفع الملف","exportData":"تصدير البيانات","fileSelected":"تم اختيار الملف","processing":"جاري المعالجة...","success":"تمت العملية بنجاح","error":"حدث خطأ","noFileSelected":"لم يتم اختيار ملف","invalidFileType":"نوع الملف غير صحيح","mediaLibrary":"مكتبة المواد","totalMedia":"إجمالي المواد","searchMedia":"البحث في المواد...","filterByType":"فلترة حسب النوع","loadingMedia":"جاري تحميل المواد...","importSuccess":"تم الاستيراد بنجاح","itemsImported":"مادة تم استيرادها","importError":"حدث خطأ في الاستيراد","exportSuccess":"تم تصدير الملف بنجاح","exportError":"خطأ في التصدير","noMedia":"لا توجد مواد متاحة","result":"النتيجة","searchDescription":"ابحث في الخريطة البرامجية وجدول الإذاعة اليومي بسهولة","noResultsMessage":"لا توجد نتائج للعرض. يرجى تحديد معايير البحث والضغط على زر \\"بحث وإحصاء\\".","searchAndStats":"بحث وإحصاء","dailyScheduleHint":"اختر \\"الجدول اليومي\\" للبحث عن البرومو والفواصل والستينغ","broadcastDayHint":"يوم البث يبدأ في الساعة 08:00 صباحاً","broadcastDayEndHint":"يوم البث ينتهي في الساعة 07:59 صباحاً من اليوم التالي","date":"التاريخ","time":"الوقت","type":"النوع","name":"الاسم","code":"الكود","rerun":"إعادة","temporary":"مؤقت"},"statistics":{"title":"إحصائيات النظام","subtitle":"تقارير وإحصائيات مفصلة","loadingStats":"جاري تحميل الإحصائيات...","totalMedia":"إجمالي المواد","allRegisteredMedia":"جميع المواد المسجلة","totalSegments":"إجمالي السيجمانت","allSegments":"جميع السيجمانت","differentTypes":"أنواع مختلفة","mediaTypes":"أنواع المواد","averageSegments":"متوسط السيجمانت","perMedia":"لكل مادة","distributionByType":"توزيع المواد حسب النوع","distributionByStatus":"توزيع المواد حسب الحالة","recentlyAdded":"المواد المضافة حديثاً","status":{"valid":"صالح للبث","rejectedCensorship":"مرفوض رقابياً - يحتاج مراجعة المحتوى","rejectedTechnical":"مرفوض هندسياً - مشاكل تقنية في الجودة","waiting":"في انتظار المراجعة"}},"addMedia":{"title":"إضافة مادة إعلامية جديدة","subtitle":"بعد حفظ المادة، ستبقى في هذه الصفحة لإضافة مادة جديدة","basicInfo":"المعلومات الأساسية","hardDiskNumber":"رقم الهارد","selectType":"اختر النوع","channel":"القناة","selectChannel":"اختر القناة","selectStatus":"اختر الحالة","source":"المصدر","startDate":"تاريخ البداية","endDate":"تاريخ الانتهاء","segments":"السيجمانت","addSegment":"إضافة سيجمانت","segment":"سيجمانت","segmentCode":"كود السيجمانت","saveAndAddNew":"حفظ وإضافة مادة جديدة","clearFields":"مسح الحقول","description":"وصف المادة","notes":"ملاحظات","additionalNotes":"ملاحظات إضافية","showInSchedule":"إظهار في قائمة الخريطة وجدول الإذاعة","txDescription":"عند تفعيل هذا الخيار، ستظهر المادة في القائمة الجانبية لجدول الخريطة وجدول الإذاعة","episodeNumber":"رقم الحلقة","seasonNumber":"رقم الموسم","partNumber":"رقم الجزء","durationAutoCalculated":"المدة (تحسب تلقائياً)"}}');

/***/ }),

/***/ "(ssr)/./public/locales/en/common.json":
/*!***************************************!*\
  !*** ./public/locales/en/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"navigation":{"dashboard":"Dashboard","mediaList":"Media List","addMedia":"Add Media","weeklySchedule":"Weekly Schedule","dailySchedule":"Daily Schedule","reports":"Reports","unifiedSystem":"Import/Export","adminDashboard":"Users","statistics":"Statistics"},"common":{"welcome":"Welcome","loading":"Loading...","loadingSchedule":"Loading weekly schedule...","loadingData":"Loading data...","save":"Save","cancel":"Cancel","delete":"Delete","edit":"Edit","add":"Add","search":"Search","filter":"Filter","export":"Export","import":"Import","yes":"Yes","no":"No","ok":"OK","close":"Close","back":"Back","next":"Next","previous":"Previous","submit":"Submit","reset":"Reset","clear":"Clear","select":"Select","selectDate":"Select Date","selectTime":"Select Time","actions":"Actions","status":"Status","type":"Type","name":"Name","description":"Description","duration":"Duration","startTime":"Start Time","endTime":"End Time","date":"Date","time":"Time","content":"Content","code":"Code","episode":"Episode","season":"Season","part":"Part","segments":"Segments","available":"Available","unavailable":"Unavailable","active":"Active","inactive":"Inactive","valid":"Valid","invalid":"Invalid","expired":"Expired","pending":"Pending","approved":"Approved","rejected":"Rejected","total":"Total","count":"Count","items":"Items","user":"User","noData":"No Data Available","noResults":"No Results Found","error":"Error","success":"Success","warning":"Warning","info":"Information"},"mediaTypes":{"ALL":"All Types","PROGRAM":"Program","SERIES":"Series","FILM":"Film","SONG":"Song","PROMO":"Promo","STING":"Sting","FILLER":"Filler","NEXT":"Next","NOW":"Now","MINI":"Mini","CROSS":"Cross","سنعود":"We\'ll Be Back","عدنا":"We\'re Back"},"roles":{"ADMIN":"System Administrator","CONTENT_MANAGER":"Content Manager","MEDIA_MANAGER":"Database Manager","SCHEDULER":"Program Scheduler","FULL_VIEWER":"Full View User","DATA_ENTRY":"Data Entry","MAP_SCHEDULER":"Map & Schedule Manager","VIEWER":"Viewer","EDITOR":"Editor","OPERATOR":"Operator"},"roleDescriptions":{"ADMIN":"Full system administration and user management permissions","CONTENT_MANAGER":"Manage content and media materials","MEDIA_MANAGER":"Manage media database","SCHEDULER":"Create and edit program schedules","FULL_VIEWER":"View all data and reports","DATA_ENTRY":"Enter and edit basic data","MAP_SCHEDULER":"Manage program map and schedules","VIEWER":"View basic data only"},"mediaStatus":{"ALL":"All Status","VALID":"Valid","REJECTED_CENSORSHIP":"Rejected - Censorship","REJECTED_TECHNICAL":"Rejected - Technical","EXPIRED":"Expired","HOLD":"On Hold"},"channels":{"DOCUMENTARY":"Documentary","NEWS":"News","OTHER":"Other"},"dashboard":{"title":"Dashboard","subtitle":"Media Management System","totalMedia":"Total Media","activeSchedules":"Active Schedules","todayBroadcast":"Today\'s Broadcast","systemUsers":"System Users","recentActivity":"Recent Activity","quickActions":"Quick Actions","statistics":"Statistics","overview":"Overview"},"media":{"title":"Media Management","addNew":"Add New Media","list":"Media List","details":"Media Details","segments":"Segments","addSegment":"Add Segment","segmentCode":"Segment Code","timeIn":"Time In","timeOut":"Time Out","hardDrive":"Hard Drive","server":"Server","notes":"Notes","startDate":"Start Date","endDate":"End Date","showInTX":"Show in TX","episodeNumber":"Episode Number","seasonNumber":"Season Number","partNumber":"Part Number","totalSegments":"Total Segments","validMedia":"Valid Media","expiredMedia":"Expired Media","pendingMedia":"Pending Media","searchByName":"Search by name or description","searchByCode":"Search by media or segment code","searchPlaceholder":"Search for media...","codePlaceholder":"Search by code...","mediaType":"Media Type","mediaStatus":"Status","sortBy":"Sort By","newest":"Newest First","oldest":"Oldest First","byName":"Name (A-Z)","byType":"Type","searchStats":"Showing {{filtered}} of {{total}} media items","noMediaFound":"No media items found","startAdding":"Start by adding a new media item","exportExcel":"Export Excel","exporting":"Exporting...","searchAndFilter":"Search & Filter","mediaOverview":"View and manage media content","searchFilterExport":"You can search, filter and export data","channel":"Channel","segmentCount":"Segment Count","description":"Description","noCode":"[No Code]","edit":"Edit","delete":"Delete","scrollToTop":"Back to top"},"schedule":{"title":"Schedule Management","weekly":"Weekly Program Schedule","daily":"Daily Broadcast Schedule","import":"Import Schedule","export":"Export Schedule","broadcast":"Broadcast","rerun":"Rerun","prime":"Prime","filler":"Filler","empty":"Empty","addRow":"Add Row","deleteRow":"Delete Row","moveUp":"Move Up","moveDown":"Move Down","showMap":"Show Map","hideMap":"Hide Map","saveChanges":"Save Changes","discardChanges":"Discard Changes","importFromTime":"Import from this time","broadcastTime":"Broadcast Time","programMap":"Program Map","scheduleItems":"Schedule Items","availableMedia":"Available Media","scheduledMedia":"Today\'s Scheduled Programs","weeklySchedule":"Weekly Program Schedule","weeklySubtitle":"Weekly program scheduling","importTitle":"Import Broadcast Schedule","importSubtitle":"Import schedule from specific time","importInstructions":"Select Date → Set Time → Import → Edit Media → Export Excel","importSchedule":"Import Schedule","importInstructionsLong":"Select date and time then click \\"Import from this time\\" to display the schedule","loadingSchedule":"Loading weekly schedule...","selectingDate":"Selecting date...","mediaList":"Media List","addTempMedia":"Add Temporary Media","mediaName":"Media name...","duration":"Duration (e.g.: 01:30:00)","notes":"Notes (optional)...","add":"Add","updateReruns":"Update Reruns","allTypes":"All Types","searchMedia":"Search media...","resultsCount":"{{count}} of {{total}} media","noMedia":"No media found","changeFilter":"Try changing filter or search","addNewMedia":"Add new media from user page","deleteTempMedia":"Delete temporary media","liveProgram":"Live","pendingDelivery":"Pending Delivery","temporary":"Temporary","broadcastSchedule":"Broadcast Schedule","exportSchedule":"Export Schedule","selectedWeek":"Selected Week","previousWeek":"← Previous Week","nextWeek":"Next Week →","time":"Time","rerunIndicator":"Rerun - can be deleted for editing","hideSchedule":"Hide Schedule","showSchedule":"Show Schedule","unknown":"Unknown","season":"Season","episode":"Episode","part":"Part","confirmDelete":"Are you sure you want to delete {{type}}: \\"{{name}}\\"?","deleteWarningOriginal":"Warning: Deleting original media will delete all its reruns","deleteWarningRerun":"Warning: Deleting rerun will not affect original media","deleteWarningTemp":"Temporary media will be deleted from schedule","originalMaterial":"Original Material","rerunMaterial":"Rerun","tempMaterial":"Temporary Media","timeConflict":"Time conflict detected! Choose another time.","enterMediaName":"Please enter media name","confirmDeleteTemp":"Do you want to delete this temporary media?","deleteMediaTitle":"🗑️ Delete Media:","deleteOriginalInfo":"Original Media: Permanent deletion with all reruns","deleteRerunInfo":"Reruns: Delete leaving field empty for editing","deleteConfirmInfo":"Confirmation will appear before deletion","confirmDeleteSegment":"Are you sure you want to delete this segment?","usageInstructions":"📋 Usage Instructions:","addMediaTitle":"🎯 Adding Media:","addMediaInstruction1":"Drag media from the right panel to the schedule","addMediaInstruction2":"🔄 Drag media within the schedule to copy to other time slots","addMediaInstruction3":"🎬 Use type filter to filter by media type","addMediaInstruction4":"🔍 Use search to find media quickly","primeTimeTitle":"🌟 Original Media (Prime Time):","primeTimeSchedule1":"Sunday-Wednesday: 18:00-00:00","primeTimeSchedule2":"Thursday-Saturday: 18:00-02:00","primeTimeColor":"🟡 Golden color in schedule","rerunsTitle":"♻️ Automatic Reruns (Two Parts):","rerunsSchedule1":"Sunday-Wednesday:","rerunsSchedule2":"Thursday-Saturday:","rerunsPart1Sun":"Part 1: Same column 00:00-07:59","rerunsPart2Sun":"Part 2: Next column 08:00-17:59","rerunsPart1Thu":"Part 1: Same column 02:00-07:59","rerunsPart2Thu":"Part 2: Next column 08:00-17:59","rerunsColor":"🔘 Gray color - can be deleted for editing","dateManagementTitle":"📅 Date Management:","dateManagementInfo":"Use calendar and buttons to navigate between weeks • Each week is saved separately","importantNoteTitle":"💡 Important Note:","importantNoteInfo":"When adding few media items (1-3 items) in prime time, they will appear with time gaps in reruns to avoid excessive repetition. Add more media for greater variety.","weeklyScheduleTitle":"Weekly Schedule","noWeeklyData":"No data available for weekly schedule","types":{"program":"Program","series":"Series","film":"Film","song":"Song","sting":"Sting","fillIn":"Fill In","filler":"Filler","promo":"Promo","next":"Next","now":"Now","snawod":"We\'ll Be Back","odna":"We\'re Back","mini":"Mini","cross":"Cross"},"startTime":"Start Time"},"days":{"sunday":"Sunday","monday":"Monday","tuesday":"Tuesday","wednesday":"Wednesday","thursday":"Thursday","friday":"Friday","saturday":"Saturday"},"months":{"january":"January","february":"February","march":"March","april":"April","may":"May","june":"June","july":"July","august":"August","september":"September","october":"October","november":"November","december":"December"},"auth":{"login":"Login","logout":"Logout","username":"Username","password":"Password","loginButton":"Sign In","loginError":"Login Error","accessDenied":"Access denied","createUserError":"Error creating user","deleteUserSuccess":"User deleted successfully!","deleteUserError":"Error deleting user","fillRequiredFields":"Please fill all required fields","updateUserSuccess":"User updated successfully!","updateUserError":"Error updating user","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","permissions":"Permissions","allPermissions":"All Permissions","manageMedia":"Manage Media","manageSchedules":"Manage Schedules","viewMedia":"View Media","viewSchedules":"View Schedules","viewMap":"View Map","viewBroadcast":"View Broadcast","manageMap":"Manage Map","roles":{"admin":"System Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Scheduler","fullViewer":"Full View User","dataEntry":"Data Entry","mapScheduler":"Map & Schedule Manager","viewer":"Viewer","adminDesc":"Full permissions for all system parts + user management","contentManagerDesc":"Full media and schedule management (without user management)","mediaManagerDesc":"Media management only (add, edit, delete)","schedulerDesc":"Broadcast schedules and program map management only","fullViewerDesc":"View entire application without editing or adding capabilities","dataEntryDesc":"Data entry and editing only without viewing other parts of the application","mapSchedulerDesc":"Map and daily broadcast schedule management with database viewing without editing","viewerDesc":"View content only without editing or adding capabilities"},"invalidCredentials":"Invalid credentials","welcomeBack":"Welcome back","pleaseLogin":"Please login to continue","userRoles":"User Roles","adminDesc":"Full permissions","contentManagerDesc":"Media management","schedulerDesc":"Schedule management","viewerDesc":"View only","sessionExpired":"Session expired","insufficientPermissions":"Insufficient permissions","status":{"valid":"Valid","rejectedCensorship":"Rejected - Censorship","rejectedTechnical":"Rejected - Technical","waiting":"Waiting"},"segments":"segments"},"admin":{"title":"User Management","subtitle":"Add and edit users","users":"Users","permissions":"Permissions","addUser":"Add User","editUser":"Edit User","deleteUser":"Delete User","userRole":"User Role","userStatus":"User Status","lastLogin":"Last Login","createdAt":"Created At","updatedAt":"Updated At","activeUsers":"Active Users","inactiveUsers":"Inactive Users","totalUsers":"Total Users","loadingData":"Loading data...","userManagement":"User Management","addNewUser":"Add New User","username":"Username","password":"Password","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","status":"Status","actions":"Actions","active":"Active","inactive":"Inactive","createUser":"Create User","cancel":"Cancel","saveChanges":"Save Changes","edit":"Edit","delete":"Delete","noUsers":"No users found","addUsersMessage":"Add new users using the \\"Add New User\\" button","rolesExplanation":"Roles and Permissions Explanation","allPermissions":"All Permissions","mediaManagement":"Media Management","scheduleManagement":"Schedule Management","viewMedia":"View Media","viewSchedules":"View Schedules","viewMap":"View Map","viewBroadcast":"View Broadcast","mapManagement":"Map Management","permissionsLabel":"Permissions:","noLoginYet":"No login yet","editingUser":"Edit User","passwordNote":"(leave empty to keep current)","confirmDelete":"Are you sure you want to delete this user?","userCreated":"User created successfully!","userUpdated":"User updated successfully!","userDeleted":"User deleted successfully!","fillRequired":"Please fill all required fields","createError":"Error creating user","updateError":"Error updating user","deleteError":"Error deleting user","fetchError":"Error fetching user data","serverError":"Server connection error","roles":{"admin":"System Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Program Scheduler","fullViewer":"Full Viewer","dataEntry":"Data Entry","mapScheduler":"Map & Schedule Manager","viewer":"Viewer","adminDesc":"Full system administration and user management permissions","contentManagerDesc":"Manage content and media materials","mediaManagerDesc":"Manage media database","schedulerDesc":"Create and edit program schedules","fullViewerDesc":"View all data and reports","dataEntryDesc":"Enter and edit basic data","mapSchedulerDesc":"Manage program map and schedules","viewerDesc":"View basic data only"}},"permissions":{"MEDIA_READ":"Read Media","MEDIA_CREATE":"Create Media","MEDIA_UPDATE":"Update Media","MEDIA_DELETE":"Delete Media","SCHEDULE_READ":"Read Schedules","SCHEDULE_CREATE":"Create Schedules","SCHEDULE_UPDATE":"Update Schedules","SCHEDULE_DELETE":"Delete Schedules","USER_MANAGEMENT":"User Management","SYSTEM_ADMIN":"System Administration"},"stats":{"totalMedia":"Total Media","validMedia":"Valid Media","maintenanceMedia":"Under Maintenance","activeUsers":"Active Users","efficiency":"Overall Efficiency","operationalCost":"Operational Cost","processingTime":"Average Processing Time","activeOperations":"Active Operations","growthRate":"Growth Rate","healthRate":"Health Rate","issueRate":"Issue Rate","activityRate":"Activity Rate","improvement":"Improvement","dailyAverage":"Daily Average","loadingDummyData":"Loading dummy data"},"messages":{"success":{"mediaAdded":"Media added successfully","mediaUpdated":"Media updated successfully","mediaDeleted":"Media deleted successfully","exportSuccess":"Export completed successfully","importSuccess":"Import completed successfully","scheduleUpdated":"Schedule updated successfully","userCreated":"User created successfully","userUpdated":"User updated successfully","userDeleted":"User deleted successfully","changesSaved":"Changes saved successfully"},"error":{"serverConnection":"Server connection error","mediaNotFound":"Media not found","invalidData":"Invalid data","permissionDenied":"Permission denied","exportFailed":"Export failed","importFailed":"Import failed","unknownError":"Unknown error occurred","timeFormatError":"Time format error","calculationError":"Calculation error"},"info":{"loading":"Loading...","saving":"Saving...","processing":"Processing...","exporting":"Exporting...","importing":"Importing..."},"admin":{"title":"User Management","subtitle":"Manage system users and permissions","loadingData":"Loading data...","userManagement":"User Management","addNewUser":"Add New User","username":"Username","password":"Password","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","createUser":"Create User","cancel":"Cancel","lastLogin":"Last Login","actions":"Actions","noLoginYet":"No login yet","confirmDelete":"Are you sure you want to delete this user?","noUsers":"No users found","addUsersMessage":"Start by adding new users to the system","rolesExplanation":"Roles and Permissions","permissions":"Permissions","allPermissions":"All Permissions","manageMedia":"Manage Media","manageSchedules":"Manage Schedules","viewSchedules":"View Schedules","viewMedia":"View Media","viewMap":"View Map","viewBroadcast":"View Broadcast","manageMap":"Manage Map","editingUser":"Editing User","passwordNote":"(leave empty to keep current)","status":"Status","active":"Active","inactive":"Inactive","saveChanges":"Save Changes","roles":{"admin":"Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Scheduler","fullViewer":"Full Viewer","dataEntry":"Data Entry","mapScheduler":"Map Scheduler","viewer":"Viewer","adminDesc":"Full system access and user management","contentManagerDesc":"Manage media content and schedules","mediaManagerDesc":"Manage media library and content","schedulerDesc":"Create and manage broadcast schedules","fullViewerDesc":"View all system data and reports","dataEntryDesc":"Add and edit media content","mapSchedulerDesc":"Manage program maps and schedules","viewerDesc":"Basic viewing permissions"}}},"home":{"title":"Prime-X","subtitle":"Media Management System","loading":"Loading...","autoRedirect":"Or wait for automatic redirect to dashboard","quickNavigation":"Quick Navigation","dashboard":"Dashboard","dailySchedule":"Daily Broadcast Schedule","weeklySchedule":"Program Schedule","mediaList":"Media List","addMedia":"Add Media","adminPanel":"User Management"},"reports":{"title":"Broadcast Reports","subtitle":"Search and analyze broadcast content","searchFilters":"Search Filters","mediaType":"Media Type","allTypes":"All Types","mediaName":"Media Name","mediaCode":"Media Code","source":"Source","both":"Both","weekly":"Weekly Schedule","daily":"Daily Schedule","dateFrom":"From Date","dateTo":"To Date","search":"Search","exportExcel":"Export Excel","showStatistics":"Show Statistics","hideStatistics":"Hide Statistics","searchResults":"Search Results","noResults":"No results found","searchMessage":"Use the filters above to search for broadcast content","resultsCount":"{{count}} results","statistics":"Media Statistics","totalItems":"Total Items","totalDuration":"Total Duration","count":"Count","duration":"Duration","percentage":"Percentage","loadingMedia":"Loading media...","searchError":"Search error","exportError":"Export error","exportSuccess":"Export successful","date":"Date","time":"Time","type":"Type","name":"Name","code":"Code","rerun":"Rerun","temporary":"Temporary","original":"Original","programsFilmsSeries":"Programs, Films & Series","promosFillersSting":"Promos, Fillers & Stings","detailedStatistics":"Detailed Statistics"},"unified":{"title":"Unified Import/Export System","subtitle":"Manage import and export of media data","importExport":"Import/Export","selectFile":"Please select a file","uploadFile":"Upload File","exportData":"Export Data","fileSelected":"File Selected","processing":"Processing...","success":"Operation Successful","error":"An Error Occurred","noFileSelected":"No File Selected","invalidFileType":"Invalid File Type","mediaLibrary":"Media Library","totalMedia":"Total Media","searchMedia":"Search media...","filterByType":"Filter by Type","loadingMedia":"Loading media...","importSuccess":"Import successful","itemsImported":"items imported","importError":"Import error occurred","exportSuccess":"File exported successfully","exportError":"Export error","noMedia":"No media available","result":"Result","searchDescription":"Search the program schedule and daily broadcast schedule easily","noResultsMessage":"No results to display. Please set search criteria and click \\"Search & Statistics\\".","searchAndStats":"Search & Statistics","dailyScheduleHint":"Choose \\"Daily Schedule\\" to search for promos, fillers, and stings","broadcastDayHint":"Broadcast day starts at 08:00 AM","broadcastDayEndHint":"Broadcast day ends at 07:59 AM next day","date":"Date","time":"Time","type":"Type","name":"Name","code":"Code","rerun":"Rerun","temporary":"Temporary"},"statistics":{"title":"System Statistics","subtitle":"Detailed reports and statistics","loadingStats":"Loading statistics...","totalMedia":"Total Media","allRegisteredMedia":"All registered media","totalSegments":"Total Segments","allSegments":"All segments","differentTypes":"Different Types","mediaTypes":"Media types","averageSegments":"Average Segments","perMedia":"Per media","distributionByType":"Distribution by Type","distributionByStatus":"Distribution by Status","recentlyAdded":"Recently Added","status":{"valid":"Valid for broadcast","rejectedCensorship":"Rejected (Censorship) - Content review needed","rejectedTechnical":"Rejected (Technical) - Quality issues","waiting":"Awaiting review"}},"addMedia":{"title":"Add New Media","subtitle":"After saving, you\'ll stay on this page to add another media","basicInfo":"Basic Information","hardDiskNumber":"Hard Disk Number","selectType":"Select Type","channel":"Channel","selectChannel":"Select Channel","selectStatus":"Select Status","source":"Source","startDate":"Start Date","endDate":"End Date","segments":"Segments","addSegment":"Add Segment","segment":"Segment","segmentCode":"Segment Code","saveAndAddNew":"Save and Add New","clearFields":"Clear Fields","description":"Media Description","notes":"Notes","additionalNotes":"Additional notes","showInSchedule":"Show in Schedule and Broadcast Lists","txDescription":"When enabled, this media will appear in the sidebar of schedule and broadcast tables","episodeNumber":"Episode Number","seasonNumber":"Season Number","partNumber":"Part Number","durationAutoCalculated":"Duration (Auto-calculated)"}}');

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NavigationCard */ \"(ssr)/./src/components/NavigationCard.tsx\");\n/* harmony import */ var _components_Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(ssr)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _styles_dashboard_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/dashboard.css */ \"(ssr)/./src/styles/dashboard.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__.useAppTranslation)();\n    // تعريف عناصر التنقل في القائمة العلوية\n    const topNavigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            active: false,\n            path: '/reports'\n        },\n        ...user?.role === 'ADMIN' && user?.username === 'admin' ? [\n            {\n                name: t('navigation.unifiedSystem'),\n                icon: '📤',\n                active: false,\n                path: '/unified-system'\n            }\n        ] : [],\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard'\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics'\n        }\n    ];\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Dashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Dashboard.useEffect.timer\"], 1000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // بيانات الإحصائيات الحقيقية\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMedia: 0,\n        validMedia: 0,\n        rejectedMedia: 0,\n        expiredMedia: 0,\n        pendingMedia: 0,\n        activeUsers: 0,\n        onlineUsers: 0,\n        todayAdded: 0\n    });\n    // حالة تنبيه المواد المنتهية\n    const [expiredAlertShown, setExpiredAlertShown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تخزين المواد المنتهية للتنبيه\n    const [expiredMediaItems, setExpiredMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showExpiredAlert, setShowExpiredAlert] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // جلب البيانات الحقيقية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchRealStats();\n            // تحديث البيانات كل 30 ثانية\n            const interval = setInterval(fetchRealStats, 30000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(interval)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchRealStats = async ()=>{\n        try {\n            setLoading(true);\n            // جلب البيانات من API مع timeout أطول\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 15000); // 15 ثانية timeout\n            const [mediaResponse, usersResponse] = await Promise.all([\n                fetch('/api/media', {\n                    signal: controller.signal,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                }),\n                fetch('/api/users', {\n                    signal: controller.signal,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                })\n            ]);\n            clearTimeout(timeoutId);\n            let mediaData = [];\n            let userData = [];\n            if (mediaResponse.ok) {\n                try {\n                    const mediaResult = await mediaResponse.json();\n                    mediaData = mediaResult.success ? mediaResult.data : [];\n                } catch (e) {\n                    console.warn('Failed to parse media response:', e);\n                }\n            }\n            if (usersResponse.ok) {\n                try {\n                    const usersResult = await usersResponse.json();\n                    userData = usersResult.success ? usersResult.users : [];\n                } catch (e) {\n                    console.warn('Failed to parse users response:', e);\n                }\n            }\n            // حساب الإحصائيات الحقيقية\n            const totalMedia = mediaData.length;\n            const validMedia = mediaData.filter((item)=>item.status === 'VALID').length;\n            const rejectedMedia = mediaData.filter((item)=>item.status === 'REJECTED_CENSORSHIP' || item.status === 'REJECTED_TECHNICAL').length;\n            // التحقق من المواد المنتهية حسب التاريخ\n            const today = new Date();\n            const expiredByDateItems = mediaData.filter((item)=>item.endDate && new Date(item.endDate) < today);\n            // المواد المنتهية (إما بالحالة أو بالتاريخ)\n            const expiredStatusItems = mediaData.filter((item)=>item.status === 'EXPIRED');\n            const allExpiredItems = [\n                ...new Set([\n                    ...expiredByDateItems,\n                    ...expiredStatusItems\n                ])\n            ];\n            const expiredMedia = allExpiredItems.length;\n            // تحديث قائمة المواد المنتهية للتنبيه\n            setExpiredMediaItems(allExpiredItems);\n            // إظهار التنبيه مرة واحدة فقط في الجلسة\n            const alertKey = `expired_alert_${new Date().toDateString()}`;\n            const alertShownToday = localStorage.getItem(alertKey);\n            if (allExpiredItems.length > 0 && !alertShownToday && !expiredAlertShown) {\n                setShowExpiredAlert(true);\n                setExpiredAlertShown(true);\n                localStorage.setItem(alertKey, 'true');\n                console.log(`⚠️ تم العثور على ${allExpiredItems.length} مادة منتهية - عرض التنبيه`);\n            } else {\n                setShowExpiredAlert(false);\n            }\n            const pendingMedia = mediaData.filter((item)=>item.status === 'PENDING').length;\n            // حساب المواد المضافة اليوم\n            const todayStr = new Date().toDateString();\n            const todayAdded = mediaData.filter((item)=>{\n                if (!item.createdAt) return false;\n                return new Date(item.createdAt).toDateString() === todayStr;\n            }).length;\n            setStats({\n                totalMedia,\n                validMedia,\n                rejectedMedia,\n                expiredMedia,\n                pendingMedia,\n                activeUsers: userData.length,\n                onlineUsers: userData.filter((u)=>u.isActive).length,\n                todayAdded\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // التحقق من نوع الخطأ\n            if (error instanceof Error && error.name === 'AbortError') {\n                console.log('Request was aborted due to timeout');\n            }\n            // بيانات افتراضية في حالة الخطأ\n            setStats({\n                totalMedia: 0,\n                validMedia: 0,\n                rejectedMedia: 0,\n                expiredMedia: 0,\n                pendingMedia: 0,\n                activeUsers: 4,\n                onlineUsers: 1,\n                todayAdded: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    color: 'white'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            marginBottom: '30px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: \"large\",\n                            style: {\n                                fontSize: '2rem'\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '3rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.5rem',\n                            marginBottom: '10px'\n                        },\n                        children: t('common.loadingData')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#a0aec0',\n                            fontSize: '1rem'\n                        },\n                        children: t('messages.pleaseWait')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: isRTL ? 'rtl' : 'ltr'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                showExpiredAlert && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: '20px',\n                        left: '20px',\n                        right: '20px',\n                        zIndex: 1000,\n                        background: '#ef4444',\n                        color: 'white',\n                        padding: '15px 20px',\n                        borderRadius: '8px',\n                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        maxHeight: '300px',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontWeight: 'bold',\n                                        fontSize: '1.1rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: [\n                                        \"⚠️ \",\n                                        t('messages.warning'),\n                                        \": \",\n                                        t('stats.expiredMedia'),\n                                        \" \",\n                                        expiredMediaItems.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            paddingRight: '20px',\n                                            margin: '5px 0'\n                                        },\n                                        children: [\n                                            expiredMediaItems.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        item.name,\n                                                        \" - \",\n                                                        item.endDate ? `منتهية بتاريخ: ${new Date(item.endDate).toLocaleDateString('ar-EG')}` : 'منتهية'\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            expiredMediaItems.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"... والمزيد (\",\n                                                    expiredMediaItems.length - 5,\n                                                    \" مادة أخرى)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setShowExpiredAlert(false);\n                                setExpiredAlertShown(true);\n                                // حفظ في localStorage لمنع ظهور التنبيه مرة أخرى اليوم\n                                const alertKey = `expired_alert_${new Date().toDateString()}`;\n                                localStorage.setItem(alertKey, 'true');\n                                console.log('🔕 تم إغلاق تنبيه المواد المنتهية نهائياً لليوم');\n                            },\n                            style: {\n                                background: 'rgba(255, 255, 255, 0.2)',\n                                border: 'none',\n                                color: 'white',\n                                borderRadius: '4px',\n                                padding: '5px 10px',\n                                cursor: 'pointer',\n                                marginRight: '10px'\n                            },\n                            children: t('common.close')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: topNavigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const newLang = currentLang === 'ar' ? 'en' : 'ar';\n                                        i18n.changeLanguage(newLang);\n                                    },\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '5px'\n                                    },\n                                    title: currentLang === 'ar' ? 'Switch to English' : 'التبديل للعربية',\n                                    children: [\n                                        \"\\uD83C\\uDF10\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: currentLang === 'ar' ? 'EN' : 'عر'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    title: t('auth.logout'),\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        ...isRTL ? {\n                            marginRight: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-right 0.3s ease'\n                        } : {\n                            marginLeft: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-left 0.3s ease'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: t('dashboard.title')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: t('dashboard.subtitle')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#68d391',\n                                                        margin: '5px 0 0 0',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        t('dashboard.overview'),\n                                                        \": \",\n                                                        stats.totalMedia,\n                                                        \" \",\n                                                        t('stats.totalMedia'),\n                                                        \" - \",\n                                                        stats.activeUsers,\n                                                        \" \",\n                                                        t('stats.activeUsers')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: t('common.loading')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"المزامنة: \",\n                                                        currentTime.toLocaleTimeString('ar-EG')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83C\\uDFAC\",\n                                    title: t('navigation.mediaList'),\n                                    subtitle: t('media.list'),\n                                    path: \"/media-list\",\n                                    permission: \"MEDIA_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"➕\",\n                                    title: t('navigation.addMedia'),\n                                    subtitle: t('media.addNew'),\n                                    path: \"/add-media\",\n                                    permission: \"MEDIA_CREATE\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    title: t('navigation.weeklySchedule'),\n                                    subtitle: t('schedule.weekly'),\n                                    path: \"/weekly-schedule\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCA\",\n                                    title: t('navigation.dailySchedule'),\n                                    subtitle: t('schedule.daily'),\n                                    path: \"/daily-schedule\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCB\",\n                                    title: t('navigation.reports'),\n                                    subtitle: t('dashboard.recentActivity'),\n                                    path: \"/reports\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 13\n                                }, this),\n                                user?.role === 'ADMIN' && user?.username === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCE4\",\n                                    title: t('navigation.unifiedSystem'),\n                                    subtitle: t('common.import') + '/' + t('common.export'),\n                                    path: \"/unified-system\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDC65\",\n                                    title: t('navigation.adminDashboard'),\n                                    subtitle: t('admin.users'),\n                                    path: \"/admin-dashboard\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC8\",\n                                    title: t('navigation.statistics'),\n                                    subtitle: t('dashboard.statistics'),\n                                    path: \"/statistics\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5b8c52149260\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjViOGM1MjE0OTI2MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/i18n */ \"(ssr)/./src/lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n // Initialize i18n\nfunction RootLayout({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RootLayout.useEffect\": ()=>{\n            // Set initial language and direction from localStorage or default to Arabic\n            const savedLang = localStorage.getItem('language') || 'ar';\n            document.documentElement.lang = savedLang;\n            document.documentElement.dir = savedLang === 'ar' ? 'rtl' : 'ltr';\n            // Add language class to body for CSS targeting\n            document.body.className = `lang-${savedLang}`;\n            // Set font family based on language\n            if (savedLang === 'ar') {\n                document.body.style.fontFamily = 'Cairo, Arial, sans-serif';\n            } else {\n                document.body.style.fontFamily = 'Inter, Arial, sans-serif';\n            }\n        }\n    }[\"RootLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"نظام إدارة المحتوى الإعلامي | Media Management System\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"نظام متكامل لإدارة المحتوى الإعلامي والخريطة البرامجية | Integrated Media Content Management System\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"black-translucent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                style: {\n                    margin: 0,\n                    padding: 0,\n                    fontFamily: 'Cairo, Inter, Arial, sans-serif'\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \n\n\nfunction AuthGuard({ children, requiredPermissions = [], requiredRole, fallbackComponent }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    // المدير له صلاحيات كاملة\n    if (user.role === 'ADMIN') {\n      console.log('✅ المستخدم هو مدير النظام - تم منح جميع الصلاحيات');\n      return true;\n    }\n\n    // التحقق من الدور المطلوب\n    if (role && user.role !== role) {\n      console.log(`❌ المستخدم ليس لديه الدور المطلوب: ${role}`);\n      return false;\n    }\n\n    // التحقق من الصلاحيات المطلوبة\n    if (permissions.length > 0) {\n      const userPermissions = getUserPermissions(user.role);\n      console.log('🔍 التحقق من الصلاحيات:', {\n        required: permissions,\n        userHas: userPermissions\n      });\n      \n      const hasAllPermissions = permissions.every(permission => \n        userPermissions.includes(permission) || userPermissions.includes('ALL')\n      );\n      \n      if (!hasAllPermissions) {\n        console.log('❌ المستخدم ليس لديه جميع الصلاحيات المطلوبة');\n      } else {\n        console.log('✅ المستخدم لديه جميع الصلاحيات المطلوبة');\n      }\n      \n      return hasAllPermissions;\n    }\n\n    return true;\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user?.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user?.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    if (!user) return false;\n    if (user.role === 'ADMIN') return true;\n\n    const userPermissions = getUserPermissions(user.role);\n    return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: user?.role === 'ADMIN',\n        isMediaManager: user?.role === 'MEDIA_MANAGER',\n        isScheduler: user?.role === 'SCHEDULER',\n        isViewer: user?.role === 'VIEWER',\n        isFullViewer: user?.role === 'FULL_VIEWER',\n        isDataEntry: user?.role === 'DATA_ENTRY',\n        isMapScheduler: user?.role === 'MAP_SCHEDULER',\n        isContentManager: user?.role === 'CONTENT_MANAGER'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Logo({ size = 'medium', className, style }) {\n    const sizes = {\n        small: {\n            fontSize: '1rem',\n            gap: '3px',\n            xSize: '1.2rem'\n        },\n        medium: {\n            fontSize: '1.2rem',\n            gap: '5px',\n            xSize: '1.5rem'\n        },\n        large: {\n            fontSize: '2rem',\n            gap: '8px',\n            xSize: '2.5rem'\n        }\n    };\n    const currentSize = sizes[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        style: {\n            display: 'flex',\n            alignItems: 'center',\n            fontSize: currentSize.fontSize,\n            fontWeight: '900',\n            fontFamily: 'Arial, sans-serif',\n            gap: currentSize.gap,\n            direction: 'ltr',\n            ...style\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '800',\n                    letterSpacing: '1px'\n                },\n                children: \"Prime\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: '#6c757d',\n                    fontSize: '0.8em',\n                    fontWeight: '300'\n                },\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '900',\n                    fontSize: currentSize.xSize\n                },\n                children: \"X\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NavigationCard.tsx":
/*!*******************************************!*\
  !*** ./src/components/NavigationCard.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n\n\n\n\n\n// خلفية مطابقة للشاشة مع حدود ضوئية ملونة\nconst getIconColors = (icon)=>{\n    switch(icon){\n        case '🎬':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(139, 92, 246, 0.3)',\n                hoverShadow: 'rgba(139, 92, 246, 0.8)',\n                hoverBorder: '#8B5CF6',\n                glowColor: '#8B5CF6',\n                iconBackground: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%)',\n                iconShadow: 'rgba(139, 92, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(139, 92, 246, 0.3)'\n            };\n        case '➕':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(6, 182, 212, 0.3)',\n                hoverShadow: 'rgba(6, 182, 212, 0.8)',\n                hoverBorder: '#06B6D4',\n                glowColor: '#06B6D4',\n                iconBackground: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 50%, #0E7490 100%)',\n                iconShadow: 'rgba(6, 182, 212, 0.4)',\n                borderGlow: '0 0 20px rgba(6, 182, 212, 0.3)'\n            };\n        case '🔄':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(249, 115, 22, 0.3)',\n                hoverShadow: 'rgba(249, 115, 22, 0.8)',\n                hoverBorder: '#F97316',\n                glowColor: '#F97316',\n                iconBackground: 'linear-gradient(135deg, #F97316 0%, #EA580C 50%, #DC2626 100%)',\n                iconShadow: 'rgba(249, 115, 22, 0.4)',\n                borderGlow: '0 0 20px rgba(249, 115, 22, 0.3)'\n            };\n        case '📅':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(59, 130, 246, 0.3)',\n                hoverShadow: 'rgba(59, 130, 246, 0.8)',\n                hoverBorder: '#3B82F6',\n                glowColor: '#3B82F6',\n                iconBackground: 'linear-gradient(135deg, #3B82F6 0%, #2563EB 50%, #1D4ED8 100%)',\n                iconShadow: 'rgba(59, 130, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(59, 130, 246, 0.3)'\n            };\n        case '📊':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(16, 185, 129, 0.3)',\n                hoverShadow: 'rgba(16, 185, 129, 0.8)',\n                hoverBorder: '#10B981',\n                glowColor: '#10B981',\n                iconBackground: 'linear-gradient(135deg, #10B981 0%, #059669 50%, #047857 100%)',\n                iconShadow: 'rgba(16, 185, 129, 0.4)',\n                borderGlow: '0 0 20px rgba(16, 185, 129, 0.3)'\n            };\n        case '📋':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(239, 68, 68, 0.3)',\n                hoverShadow: 'rgba(239, 68, 68, 0.8)',\n                hoverBorder: '#EF4444',\n                glowColor: '#EF4444',\n                iconBackground: 'linear-gradient(135deg, #EF4444 0%, #DC2626 50%, #B91C1C 100%)',\n                iconShadow: 'rgba(239, 68, 68, 0.4)',\n                borderGlow: '0 0 20px rgba(239, 68, 68, 0.3)'\n            };\n        case '👥':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(245, 158, 11, 0.3)',\n                hoverShadow: 'rgba(245, 158, 11, 0.8)',\n                hoverBorder: '#F59E0B',\n                glowColor: '#F59E0B',\n                iconBackground: 'linear-gradient(135deg, #F59E0B 0%, #D97706 50%, #B45309 100%)',\n                iconShadow: 'rgba(245, 158, 11, 0.4)',\n                borderGlow: '0 0 20px rgba(245, 158, 11, 0.3)'\n            };\n        case '📈':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(236, 72, 153, 0.3)',\n                hoverShadow: 'rgba(236, 72, 153, 0.8)',\n                hoverBorder: '#EC4899',\n                glowColor: '#EC4899',\n                iconBackground: 'linear-gradient(135deg, #EC4899 0%, #DB2777 50%, #BE185D 100%)',\n                iconShadow: 'rgba(236, 72, 153, 0.4)',\n                borderGlow: '0 0 20px rgba(236, 72, 153, 0.3)'\n            };\n        default:\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(139, 92, 246, 0.3)',\n                hoverShadow: 'rgba(139, 92, 246, 0.8)',\n                hoverBorder: '#8B5CF6',\n                glowColor: '#8B5CF6',\n                iconBackground: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%)',\n                iconShadow: 'rgba(139, 92, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(139, 92, 246, 0.3)'\n            };\n    }\n};\nfunction NavigationCard({ icon, title, subtitle, path, permission, adminOnly = false, height = 'auto' }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    // التحقق من الصلاحيات\n    if (adminOnly && user?.role !== 'ADMIN') {\n        return null;\n    }\n    if (permission && !hasPermission(permission)) {\n        return null;\n    }\n    const handleClick = ()=>{\n        router.push(path);\n    };\n    const iconColors = getIconColors(icon);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: handleClick,\n        style: {\n            background: iconColors.background,\n            borderRadius: '20px',\n            padding: '30px',\n            border: `3px solid ${iconColors.border}`,\n            position: 'relative',\n            overflow: 'hidden',\n            cursor: 'pointer',\n            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n            transform: 'translateZ(0)',\n            height: height,\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            textAlign: 'center',\n            boxShadow: `0 10px 30px ${iconColors.shadow}, ${iconColors.borderGlow}, inset 0 1px 0 rgba(255,255,255,0.1)`,\n            direction: isRTL ? 'rtl' : 'ltr',\n            backdropFilter: 'blur(10px)',\n            WebkitBackdropFilter: 'blur(10px)'\n        },\n        onMouseEnter: (e)=>{\n            e.currentTarget.style.transform = 'translateY(-12px) scale(1.03)';\n            e.currentTarget.style.boxShadow = `0 25px 50px ${iconColors.hoverShadow}, 0 0 40px ${iconColors.hoverShadow}, 0 0 80px ${iconColors.hoverShadow}, inset 0 1px 0 rgba(255,255,255,0.2)`;\n            e.currentTarget.style.border = `3px solid ${iconColors.hoverBorder}`;\n            e.currentTarget.style.background = `rgba(17, 24, 39, 0.95)`;\n            // تأثير الإضاءة على الأيقونة\n            const iconElement = e.currentTarget.querySelector('.card-icon');\n            if (iconElement) {\n                iconElement.style.transform = 'scale(1.2) rotateY(5deg)';\n                iconElement.style.boxShadow = `0 12px 35px ${iconColors.iconShadow}, 0 0 30px ${iconColors.glowColor}, 0 0 50px ${iconColors.glowColor}, inset 0 2px 8px rgba(255,255,255,0.3)`;\n                iconElement.style.filter = `brightness(1.4) contrast(1.3) drop-shadow(0 0 25px ${iconColors.glowColor})`;\n            }\n        },\n        onMouseLeave: (e)=>{\n            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n            e.currentTarget.style.boxShadow = `0 10px 30px ${iconColors.shadow}, ${iconColors.borderGlow}, inset 0 1px 0 rgba(255,255,255,0.1)`;\n            e.currentTarget.style.border = `3px solid ${iconColors.border}`;\n            e.currentTarget.style.background = iconColors.background;\n            // إعادة الأيقونة لحالتها الطبيعية\n            const iconElement = e.currentTarget.querySelector('.card-icon');\n            if (iconElement) {\n                iconElement.style.transform = 'scale(1) rotateY(0deg)';\n                iconElement.style.boxShadow = `0 8px 25px ${iconColors.iconShadow}, 0 4px 15px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255,255,255,0.2)`;\n                iconElement.style.filter = 'brightness(1.1) contrast(1.1) drop-shadow(0 2px 4px rgba(0,0,0,0.3))';\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-icon\",\n                style: {\n                    position: 'absolute',\n                    top: '25px',\n                    [isRTL ? 'right' : 'left']: '25px',\n                    width: '75px',\n                    height: '75px',\n                    background: iconColors.iconBackground,\n                    borderRadius: '20px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '2.4rem',\n                    border: '3px solid rgba(255, 255, 255, 0.4)',\n                    boxShadow: `0 8px 25px ${iconColors.iconShadow}, 0 4px 15px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255,255,255,0.2)`,\n                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n                    backdropFilter: 'blur(10px)',\n                    WebkitBackdropFilter: 'blur(10px)',\n                    transformStyle: 'preserve-3d'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        filter: 'brightness(1.3) contrast(1.2) drop-shadow(0 2px 4px rgba(0,0,0,0.3))',\n                        textShadow: '0 2px 8px rgba(0,0,0,0.4)',\n                        color: 'white',\n                        transition: 'all 0.3s ease'\n                    },\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    height: '100%',\n                    paddingTop: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.7rem',\n                            fontWeight: '600',\n                            color: 'rgba(255, 255, 255, 0.95)',\n                            marginBottom: '12px',\n                            textShadow: '0 2px 12px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.1)',\n                            textAlign: 'center',\n                            letterSpacing: '0.5px',\n                            transition: 'all 0.3s ease'\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'rgba(255, 255, 255, 0.75)',\n                            fontSize: '1.1rem',\n                            lineHeight: '1.5',\n                            textShadow: '0 1px 6px rgba(0,0,0,0.6)',\n                            textAlign: 'center',\n                            opacity: 0.9,\n                            transition: 'all 0.3s ease'\n                        },\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: '0',\n                    left: '0',\n                    right: '0',\n                    height: '2px',\n                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)',\n                    transform: 'translateX(-100%)',\n                    transition: 'transform 0.6s ease'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NavigationCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(ssr)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Sidebar({ isOpen, onToggle }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const menuItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.importSchedule'),\n            icon: '📤',\n            path: '/daily-schedule/import',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            path: '/statistics',\n            permission: null\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    ...isRTL ? {\n                        right: isOpen ? 0 : '-280px',\n                        borderLeft: '1px solid #2d3748'\n                    } : {\n                        left: isOpen ? 0 : '-280px',\n                        borderRight: '1px solid #2d3748'\n                    },\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    transition: `${isRTL ? 'right' : 'left'} 0.3s ease`,\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: \"small\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                margin: 0,\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: t('dashboard.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#2d3748' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    borderTop: 'none',\n                                    borderBottom: 'none',\n                                    ...isRTL ? {\n                                        borderLeft: 'none',\n                                        borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    } : {\n                                        borderRight: 'none',\n                                        borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    },\n                                    padding: isRTL ? '12px 20px 12px 8px' : '12px 8px 12px 20px',\n                                    textAlign: isRTL ? 'right' : 'left',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    transition: 'all 0.2s ease',\n                                    direction: isRTL ? 'rtl' : 'ltr'\n                                },\n                                children: item.name\n                            }, index, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('user');\n                                localStorage.removeItem('token');\n                                router.push('/login');\n                            },\n                            style: {\n                                width: '100%',\n                                background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '10px',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '8px',\n                                fontSize: '0.9rem',\n                                fontWeight: 'bold',\n                                marginBottom: '15px'\n                            },\n                            children: [\n                                \"\\uD83D\\uDEAA \",\n                                t('navigation.logout')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TaWRlYmFyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQytCO0FBQ25CO0FBQ3dCO0FBQ3BDO0FBZ0JYLFNBQVNNLFFBQVEsRUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQWdCO0lBQ2hFLE1BQU1DLFNBQVNSLDBEQUFTQTtJQUN4QixNQUFNUyxXQUFXUiw0REFBV0E7SUFDNUIsTUFBTSxFQUFFUyxJQUFJLEVBQUVDLGFBQWEsRUFBRSxHQUFHVCxtREFBT0E7SUFDdkMsTUFBTSxFQUFFVSxDQUFDLEVBQUVDLEtBQUssRUFBRSxHQUFHViwyRUFBaUJBO0lBRXRDLE1BQU1XLFlBQXdCO1FBQzVCO1lBQ0VDLE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtRQUNBO1lBQ0VILE1BQU1ILEVBQUU7WUFDUkksTUFBTTtZQUNOQyxNQUFNO1lBQ05DLFlBQVk7UUFDZDtLQUNEO0lBRUQsTUFBTUMsb0JBQW9CTCxVQUFVTSxNQUFNLENBQUNDLENBQUFBO1FBQ3pDLElBQUlBLEtBQUtDLFNBQVMsSUFBSVosTUFBTWEsU0FBUyxTQUFTLE9BQU87UUFDckQsSUFBSUYsS0FBS0gsVUFBVSxJQUFJLENBQUNQLGNBQWNVLEtBQUtILFVBQVUsR0FBRyxPQUFPO1FBQy9ELE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDTTs7WUFDRWxCLHdCQUNDLDhEQUFDa0I7Z0JBQ0NDLE9BQU87b0JBQ0xDLFVBQVU7b0JBQ1ZDLEtBQUs7b0JBQ0xDLE1BQU07b0JBQ05DLE9BQU87b0JBQ1BDLFFBQVE7b0JBQ1JDLFlBQVk7b0JBQ1pDLFFBQVE7b0JBQ1JDLFNBQVNDLE9BQU9DLFVBQVUsSUFBSSxNQUFNLFVBQVU7Z0JBQ2hEO2dCQUNBQyxTQUFTN0I7Ozs7OzswQkFJYiw4REFBQ2lCO2dCQUNDQyxPQUFPO29CQUNMQyxVQUFVO29CQUNWQyxLQUFLO29CQUNMLEdBQUlkLFFBQVE7d0JBQ1ZnQixPQUFPdkIsU0FBUyxJQUFJO3dCQUNwQitCLFlBQVk7b0JBQ2QsSUFBSTt3QkFDRlQsTUFBTXRCLFNBQVMsSUFBSTt3QkFDbkJnQyxhQUFhO29CQUNmLENBQUM7b0JBQ0RDLE9BQU87b0JBQ1BDLFFBQVE7b0JBQ1JULFlBQVk7b0JBQ1pVLFlBQVksR0FBRzVCLFFBQVEsVUFBVSxPQUFPLFVBQVUsQ0FBQztvQkFDbkRtQixRQUFRO29CQUNSQyxTQUFTO29CQUNUUyxlQUFlO29CQUNmQyxZQUFZO2dCQUNkOztrQ0FFQSw4REFBQ25CO3dCQUFJQyxPQUFPOzRCQUNWbUIsU0FBUzs0QkFDVEMsY0FBYzs0QkFDZFosU0FBUzs0QkFDVGEsWUFBWTs0QkFDWkMsZ0JBQWdCO3dCQUNsQjs7MENBQ0UsOERBQUN2QjtnQ0FBSUMsT0FBTztvQ0FBRVEsU0FBUztvQ0FBUWEsWUFBWTtvQ0FBVUUsS0FBSztnQ0FBTzswQ0FDL0QsNEVBQUN4Qjs7c0RBQ0MsOERBQUNwQiw2Q0FBSUE7NENBQUM2QyxNQUFLOzRDQUFReEIsT0FBTztnREFBRXlCLE9BQU87NENBQVE7Ozs7OztzREFDM0MsOERBQUNDOzRDQUFFMUIsT0FBTztnREFDUnlCLE9BQU87Z0RBQ1BFLFFBQVE7Z0RBQ1JDLFVBQVU7NENBQ1o7c0RBQ0d6QyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJVCw4REFBQ1k7Z0NBQUlDLE9BQU87b0NBQUVRLFNBQVM7b0NBQVFhLFlBQVk7b0NBQVVFLEtBQUs7Z0NBQU87MENBQy9ELDRFQUFDTTtvQ0FDQ2xCLFNBQVM3QjtvQ0FDVGtCLE9BQU87d0NBQ0xNLFlBQVk7d0NBQ1p3QixRQUFRO3dDQUNSTCxPQUFPO3dDQUNQRyxVQUFVO3dDQUNWRyxRQUFRO3dDQUNSWixTQUFTO29DQUNYOzhDQUNEOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNTCw4REFBQ3BCO3dCQUFJQyxPQUFPOzRCQUNWZ0MsTUFBTTs0QkFDTmIsU0FBUzs0QkFDVGMsV0FBVzt3QkFDYjtrQ0FDR3ZDLGtCQUFrQndDLEdBQUcsQ0FBQyxDQUFDdEMsTUFBTXVDOzRCQUM1QixNQUFNQyxXQUFXcEQsYUFBYVksS0FBS0osSUFBSTs0QkFDdkMscUJBQ0UsOERBQUNxQztnQ0FFQ2xCLFNBQVM7b0NBQ1A1QixPQUFPc0QsSUFBSSxDQUFDekMsS0FBS0osSUFBSTtvQ0FDckIsSUFBSWlCLE9BQU9DLFVBQVUsSUFBSSxLQUFLO3dDQUM1QjVCO29DQUNGO2dDQUNGO2dDQUNBa0IsT0FBTztvQ0FDTGMsT0FBTztvQ0FDUFIsWUFBWThCLFdBQVcsWUFBWTtvQ0FDbkNYLE9BQU9XLFdBQVcsVUFBVTtvQ0FDNUJOLFFBQVE7b0NBQ1JRLFdBQVc7b0NBQ1hsQixjQUFjO29DQUNkLEdBQUloQyxRQUFRO3dDQUNWd0IsWUFBWTt3Q0FDWkMsYUFBYXVCLFdBQVcsc0JBQXNCO29DQUNoRCxJQUFJO3dDQUNGdkIsYUFBYTt3Q0FDYkQsWUFBWXdCLFdBQVcsc0JBQXNCO29DQUMvQyxDQUFDO29DQUNEakIsU0FBUy9CLFFBQVEsdUJBQXVCO29DQUN4Q21ELFdBQVduRCxRQUFRLFVBQVU7b0NBQzdCMkMsUUFBUTtvQ0FDUkgsVUFBVTtvQ0FDVlksWUFBWTtvQ0FDWnhCLFlBQVk7b0NBQ1p5QixXQUFXckQsUUFBUSxRQUFRO2dDQUM3QjswQ0FFQ1EsS0FBS04sSUFBSTsrQkE5Qkw2Qzs7Ozs7d0JBaUNYOzs7Ozs7a0NBR0YsOERBQUNwQzt3QkFBSUMsT0FBTzs0QkFDVm1CLFNBQVM7NEJBQ1RtQixXQUFXO3dCQUNiO2tDQUNFLDRFQUFDVDs0QkFDQ2xCLFNBQVM7Z0NBQ1ArQixhQUFhQyxVQUFVLENBQUM7Z0NBQ3hCRCxhQUFhQyxVQUFVLENBQUM7Z0NBQ3hCNUQsT0FBT3NELElBQUksQ0FBQzs0QkFDZDs0QkFDQXJDLE9BQU87Z0NBQ0xjLE9BQU87Z0NBQ1BSLFlBQVk7Z0NBQ1ptQixPQUFPO2dDQUNQSyxRQUFRO2dDQUNSYyxjQUFjO2dDQUNkekIsU0FBUztnQ0FDVFksUUFBUTtnQ0FDUnZCLFNBQVM7Z0NBQ1RhLFlBQVk7Z0NBQ1pDLGdCQUFnQjtnQ0FDaEJDLEtBQUs7Z0NBQ0xLLFVBQVU7Z0NBQ1ZZLFlBQVk7Z0NBQ1pLLGNBQWM7NEJBQ2hCOztnQ0FDRDtnQ0FDSzFELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1sQiIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxzcmNcXGNvbXBvbmVudHNcXFNpZGViYXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlUGF0aG5hbWUgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJy4vQXV0aEd1YXJkJztcbmltcG9ydCB7IHVzZUFwcFRyYW5zbGF0aW9uIH0gZnJvbSAnQC9ob29rcy91c2VBcHBUcmFuc2xhdGlvbic7XG5pbXBvcnQgTG9nbyBmcm9tICcuL0xvZ28nO1xuXG5cbmludGVyZmFjZSBTaWRlYmFyUHJvcHMge1xuICBpc09wZW46IGJvb2xlYW47XG4gIG9uVG9nZ2xlOiAoKSA9PiB2b2lkO1xufVxuXG5pbnRlcmZhY2UgTWVudUl0ZW0ge1xuICBuYW1lOiBzdHJpbmc7XG4gIGljb246IHN0cmluZztcbiAgcGF0aDogc3RyaW5nO1xuICBwZXJtaXNzaW9uOiBzdHJpbmcgfCBudWxsO1xuICBhZG1pbk9ubHk/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTaWRlYmFyKHsgaXNPcGVuLCBvblRvZ2dsZSB9OiBTaWRlYmFyUHJvcHMpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHBhdGhuYW1lID0gdXNlUGF0aG5hbWUoKTtcbiAgY29uc3QgeyB1c2VyLCBoYXNQZXJtaXNzaW9uIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHsgdCwgaXNSVEwgfSA9IHVzZUFwcFRyYW5zbGF0aW9uKCk7XG5cbiAgY29uc3QgbWVudUl0ZW1zOiBNZW51SXRlbVtdID0gW1xuICAgIHtcbiAgICAgIG5hbWU6IHQoJ25hdmlnYXRpb24uZGFzaGJvYXJkJyksXG4gICAgICBpY29uOiAn8J+TiicsXG4gICAgICBwYXRoOiAnL2Rhc2hib2FyZCcsXG4gICAgICBwZXJtaXNzaW9uOiBudWxsXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiB0KCduYXZpZ2F0aW9uLm1lZGlhTGlzdCcpLFxuICAgICAgaWNvbjogJ/CfjqwnLFxuICAgICAgcGF0aDogJy9tZWRpYS1saXN0JyxcbiAgICAgIHBlcm1pc3Npb246ICdNRURJQV9SRUFEJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogdCgnbmF2aWdhdGlvbi5hZGRNZWRpYScpLFxuICAgICAgaWNvbjogJ+KelScsXG4gICAgICBwYXRoOiAnL2FkZC1tZWRpYScsXG4gICAgICBwZXJtaXNzaW9uOiAnTUVESUFfQ1JFQVRFJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogdCgnbmF2aWdhdGlvbi53ZWVrbHlTY2hlZHVsZScpLFxuICAgICAgaWNvbjogJ/Cfk4UnLFxuICAgICAgcGF0aDogJy93ZWVrbHktc2NoZWR1bGUnLFxuICAgICAgcGVybWlzc2lvbjogJ1NDSEVEVUxFX1JFQUQnXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiB0KCduYXZpZ2F0aW9uLmRhaWx5U2NoZWR1bGUnKSxcbiAgICAgIGljb246ICfwn5OKJyxcbiAgICAgIHBhdGg6ICcvZGFpbHktc2NoZWR1bGUnLFxuICAgICAgcGVybWlzc2lvbjogJ1NDSEVEVUxFX1JFQUQnXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiB0KCduYXZpZ2F0aW9uLnJlcG9ydHMnKSxcbiAgICAgIGljb246ICfwn5OLJyxcbiAgICAgIHBhdGg6ICcvcmVwb3J0cycsXG4gICAgICBwZXJtaXNzaW9uOiAnU0NIRURVTEVfUkVBRCdcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6IHQoJ25hdmlnYXRpb24uaW1wb3J0U2NoZWR1bGUnKSxcbiAgICAgIGljb246ICfwn5OkJyxcbiAgICAgIHBhdGg6ICcvZGFpbHktc2NoZWR1bGUvaW1wb3J0JyxcbiAgICAgIHBlcm1pc3Npb246ICdTQ0hFRFVMRV9SRUFEJ1xuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogdCgnbmF2aWdhdGlvbi5hZG1pbkRhc2hib2FyZCcpLFxuICAgICAgaWNvbjogJ/CfkaUnLFxuICAgICAgcGF0aDogJy9hZG1pbi1kYXNoYm9hcmQnLFxuICAgICAgcGVybWlzc2lvbjogbnVsbFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogdCgnbmF2aWdhdGlvbi5zdGF0aXN0aWNzJyksXG4gICAgICBpY29uOiAn8J+TiCcsXG4gICAgICBwYXRoOiAnL3N0YXRpc3RpY3MnLFxuICAgICAgcGVybWlzc2lvbjogbnVsbFxuICAgIH1cbiAgXTtcblxuICBjb25zdCBmaWx0ZXJlZE1lbnVJdGVtcyA9IG1lbnVJdGVtcy5maWx0ZXIoaXRlbSA9PiB7XG4gICAgaWYgKGl0ZW0uYWRtaW5Pbmx5ICYmIHVzZXI/LnJvbGUgIT09ICdBRE1JTicpIHJldHVybiBmYWxzZTtcbiAgICBpZiAoaXRlbS5wZXJtaXNzaW9uICYmICFoYXNQZXJtaXNzaW9uKGl0ZW0ucGVybWlzc2lvbikpIHJldHVybiBmYWxzZTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2PlxuICAgICAge2lzT3BlbiAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICAgICAgICB0b3A6IDAsXG4gICAgICAgICAgICBsZWZ0OiAwLFxuICAgICAgICAgICAgcmlnaHQ6IDAsXG4gICAgICAgICAgICBib3R0b206IDAsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAncmdiYSgwLCAwLCAwLCAwLjUpJyxcbiAgICAgICAgICAgIHpJbmRleDogOTk4LFxuICAgICAgICAgICAgZGlzcGxheTogd2luZG93LmlubmVyV2lkdGggPD0gNzY4ID8gJ2Jsb2NrJyA6ICdub25lJ1xuICAgICAgICAgIH19XG4gICAgICAgICAgb25DbGljaz17b25Ub2dnbGV9XG4gICAgICAgIC8+XG4gICAgICApfVxuXG4gICAgICA8ZGl2XG4gICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgcG9zaXRpb246ICdmaXhlZCcsXG4gICAgICAgICAgdG9wOiAwLFxuICAgICAgICAgIC4uLihpc1JUTCA/IHtcbiAgICAgICAgICAgIHJpZ2h0OiBpc09wZW4gPyAwIDogJy0yODBweCcsXG4gICAgICAgICAgICBib3JkZXJMZWZ0OiAnMXB4IHNvbGlkICMyZDM3NDgnXG4gICAgICAgICAgfSA6IHtcbiAgICAgICAgICAgIGxlZnQ6IGlzT3BlbiA/IDAgOiAnLTI4MHB4JyxcbiAgICAgICAgICAgIGJvcmRlclJpZ2h0OiAnMXB4IHNvbGlkICMyZDM3NDgnXG4gICAgICAgICAgfSksXG4gICAgICAgICAgd2lkdGg6ICcyODBweCcsXG4gICAgICAgICAgaGVpZ2h0OiAnMTAwdmgnLFxuICAgICAgICAgIGJhY2tncm91bmQ6ICcjMWExZDI5JyxcbiAgICAgICAgICB0cmFuc2l0aW9uOiBgJHtpc1JUTCA/ICdyaWdodCcgOiAnbGVmdCd9IDAuM3MgZWFzZWAsXG4gICAgICAgICAgekluZGV4OiA5OTksXG4gICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nLFxuICAgICAgICAgIGZvbnRGYW1pbHk6ICdDYWlybywgQXJpYWwsIHNhbnMtc2VyaWYnXG4gICAgICAgIH19XG4gICAgICA+XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICAgICAgYm9yZGVyQm90dG9tOiAnMXB4IHNvbGlkICMyZDM3NDgnLFxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nXG4gICAgICAgIH19PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTJweCcgfX0+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TG9nbyBzaXplPVwic21hbGxcIiBzdHlsZT17eyBjb2xvcjogJ3doaXRlJyB9fSAvPlxuICAgICAgICAgICAgICA8cCBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGNvbG9yOiAnI2EwYWVjMCcsXG4gICAgICAgICAgICAgICAgbWFyZ2luOiAwLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44cmVtJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7dCgnZGFzaGJvYXJkLnN1YnRpdGxlJyl9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTBweCcgfX0+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29uVG9nZ2xlfVxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgY29sb3I6ICcjYTBhZWMwJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEuMnJlbScsXG4gICAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgICAgcGFkZGluZzogJzVweCdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg4pyVXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIGZsZXg6IDEsXG4gICAgICAgICAgcGFkZGluZzogJzIwcHggMCcsXG4gICAgICAgICAgb3ZlcmZsb3dZOiAnYXV0bydcbiAgICAgICAgfX0+XG4gICAgICAgICAge2ZpbHRlcmVkTWVudUl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGlzQWN0aXZlID0gcGF0aG5hbWUgPT09IGl0ZW0ucGF0aDtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKGl0ZW0ucGF0aCk7XG4gICAgICAgICAgICAgICAgICBpZiAod2luZG93LmlubmVyV2lkdGggPD0gNzY4KSB7XG4gICAgICAgICAgICAgICAgICAgIG9uVG9nZ2xlKCk7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6IGlzQWN0aXZlID8gJyMyZDM3NDgnIDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBpc0FjdGl2ZSA/ICd3aGl0ZScgOiAnI2EwYWVjMCcsXG4gICAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclRvcDogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyQm90dG9tOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAuLi4oaXNSVEwgPyB7XG4gICAgICAgICAgICAgICAgICAgIGJvcmRlckxlZnQ6ICdub25lJyxcbiAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmlnaHQ6IGlzQWN0aXZlID8gJzNweCBzb2xpZCAjNjY3ZWVhJyA6ICczcHggc29saWQgdHJhbnNwYXJlbnQnXG4gICAgICAgICAgICAgICAgICB9IDoge1xuICAgICAgICAgICAgICAgICAgICBib3JkZXJSaWdodDogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICBib3JkZXJMZWZ0OiBpc0FjdGl2ZSA/ICczcHggc29saWQgIzY2N2VlYScgOiAnM3B4IHNvbGlkIHRyYW5zcGFyZW50J1xuICAgICAgICAgICAgICAgICAgfSksXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiBpc1JUTCA/ICcxMnB4IDIwcHggMTJweCA4cHgnIDogJzEycHggOHB4IDEycHggMjBweCcsXG4gICAgICAgICAgICAgICAgICB0ZXh0QWxpZ246IGlzUlRMID8gJ3JpZ2h0JyA6ICdsZWZ0JyxcbiAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nLFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogJ2JvbGQnLFxuICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UnLFxuICAgICAgICAgICAgICAgICAgZGlyZWN0aW9uOiBpc1JUTCA/ICdydGwnIDogJ2x0cidcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApO1xuICAgICAgICAgIH0pfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzIwcHgnLFxuICAgICAgICAgIGJvcmRlclRvcDogJzFweCBzb2xpZCAjMmQzNzQ4J1xuICAgICAgICB9fT5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJyk7XG4gICAgICAgICAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd0b2tlbicpO1xuICAgICAgICAgICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgI2Y1NjU2NSwgI2U1M2UzZSknLFxuICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcxMHB4JyxcbiAgICAgICAgICAgICAgY3Vyc29yOiAncG9pbnRlcicsXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgZ2FwOiAnOHB4JyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nLFxuICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzE1cHgnXG4gICAgICAgICAgICB9fVxuICAgICAgICAgID5cbiAgICAgICAgICAgIPCfmqoge3QoJ25hdmlnYXRpb24ubG9nb3V0Jyl9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VQYXRobmFtZSIsInVzZUF1dGgiLCJ1c2VBcHBUcmFuc2xhdGlvbiIsIkxvZ28iLCJTaWRlYmFyIiwiaXNPcGVuIiwib25Ub2dnbGUiLCJyb3V0ZXIiLCJwYXRobmFtZSIsInVzZXIiLCJoYXNQZXJtaXNzaW9uIiwidCIsImlzUlRMIiwibWVudUl0ZW1zIiwibmFtZSIsImljb24iLCJwYXRoIiwicGVybWlzc2lvbiIsImZpbHRlcmVkTWVudUl0ZW1zIiwiZmlsdGVyIiwiaXRlbSIsImFkbWluT25seSIsInJvbGUiLCJkaXYiLCJzdHlsZSIsInBvc2l0aW9uIiwidG9wIiwibGVmdCIsInJpZ2h0IiwiYm90dG9tIiwiYmFja2dyb3VuZCIsInpJbmRleCIsImRpc3BsYXkiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwib25DbGljayIsImJvcmRlckxlZnQiLCJib3JkZXJSaWdodCIsIndpZHRoIiwiaGVpZ2h0IiwidHJhbnNpdGlvbiIsImZsZXhEaXJlY3Rpb24iLCJmb250RmFtaWx5IiwicGFkZGluZyIsImJvcmRlckJvdHRvbSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImdhcCIsInNpemUiLCJjb2xvciIsInAiLCJtYXJnaW4iLCJmb250U2l6ZSIsImJ1dHRvbiIsImJvcmRlciIsImN1cnNvciIsImZsZXgiLCJvdmVyZmxvd1kiLCJtYXAiLCJpbmRleCIsImlzQWN0aXZlIiwicHVzaCIsImJvcmRlclRvcCIsInRleHRBbGlnbiIsImZvbnRXZWlnaHQiLCJkaXJlY3Rpb24iLCJsb2NhbFN0b3JhZ2UiLCJyZW1vdmVJdGVtIiwiYm9yZGVyUmFkaXVzIiwibWFyZ2luQm90dG9tIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAppTranslation.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAppTranslation.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppTranslation: () => (/* binding */ useAppTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _utils_translations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/translations */ \"(ssr)/./src/utils/translations.ts\");\n\n\n// Hook مخصص للترجمة يضمن الاتساق\nconst useAppTranslation = ()=>{\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const currentLang = i18n.language || 'ar';\n    // دالة الترجمة الأساسية\n    const t = (key)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getTranslation)(key, currentLang);\n    };\n    // دالة ترجمة أنواع المواد\n    const tMediaType = (type)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getMediaTypeLabel)(type, currentLang);\n    };\n    // دالة ترجمة الأدوار\n    const tRole = (role)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getRoleLabel)(role, currentLang);\n    };\n    // دالة ترجمة أوصاف الأدوار\n    const tRoleDesc = (role)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getRoleDescription)(role, currentLang);\n    };\n    return {\n        t,\n        tMediaType,\n        tRole,\n        tRoleDesc,\n        currentLang,\n        isRTL: currentLang === 'ar'\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAppTranslation.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n.ts":
/*!*************************!*\
  !*** ./src/lib/i18n.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _public_locales_ar_common_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/locales/ar/common.json */ \"(ssr)/./public/locales/ar/common.json\");\n/* harmony import */ var _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../public/locales/en/common.json */ \"(ssr)/./public/locales/en/common.json\");\n\n\n// Import translation files\n\n\nconst resources = {\n    ar: {\n        common: _public_locales_ar_common_json__WEBPACK_IMPORTED_MODULE_2__\n    },\n    en: {\n        common: _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__\n    }\n};\n// Get saved language from localStorage or default to Arabic\nconst getInitialLanguage = ()=>{\n    if (false) {}\n    return 'ar';\n};\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    lng: getInitialLanguage(),\n    fallbackLng: 'ar',\n    debug: false,\n    interpolation: {\n        escapeValue: false\n    },\n    react: {\n        useSuspense: false\n    },\n    // Configure namespaces\n    defaultNS: 'common',\n    ns: [\n        'common'\n    ]\n});\n// Listen for language changes and update document direction\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].on('languageChanged', (lng)=>{\n    if (false) {}\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./src/styles/dashboard.css":
/*!**********************************!*\
  !*** ./src/styles/dashboard.css ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"7d7b5f8e792d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3R5bGVzL2Rhc2hib2FyZC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxzdHlsZXNcXGRhc2hib2FyZC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3ZDdiNWY4ZTc5MmRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/styles/dashboard.css\n");

/***/ }),

/***/ "(ssr)/./src/utils/translations.ts":
/*!***********************************!*\
  !*** ./src/utils/translations.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMediaTypeLabel: () => (/* binding */ getMediaTypeLabel),\n/* harmony export */   getRoleDescription: () => (/* binding */ getRoleDescription),\n/* harmony export */   getRoleLabel: () => (/* binding */ getRoleLabel),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\n// مركز الترجمة الموحد - يضمن الترجمة الصحيحة في كلا اللغتين\nconst translations = {\n    ar: {\n        // Navigation\n        navigation: {\n            dashboard: \"لوحة التحكم\",\n            mediaList: \"المواد الإعلامية\",\n            addMedia: \"إضافة مادة\",\n            weeklySchedule: \"الخريطة البرامجية\",\n            dailySchedule: \"الجدول الإذاعي اليومي\",\n            importSchedule: \"استيراد جدول\",\n            statistics: \"الإحصائيات\",\n            adminDashboard: \"إدارة المستخدمين\",\n            reports: \"تقارير البث\",\n            logout: \"تسجيل الخروج\"\n        },\n        // Common terms\n        common: {\n            name: \"الاسم\",\n            type: \"النوع\",\n            status: \"الحالة\",\n            actions: \"الإجراءات\",\n            time: \"الوقت\",\n            duration: \"المدة\",\n            content: \"المحتوى\",\n            code: \"الكود\",\n            segments: \"السيجمنتات\"\n        },\n        // Media types - أسماء ثابتة حسب المطلوب\n        mediaTypes: {\n            ALL: \"جميع الأنواع\",\n            PROGRAM: \"برنامج\",\n            SERIES: \"مسلسل\",\n            FILM: \"فيلم\",\n            SONG: \"أغنية\",\n            PROMO: \"إعلان ترويجي\",\n            STING: \"فاصل\",\n            FILLER: \"مادة مالئة\",\n            NEXT: \"التالي\",\n            NOW: \"الآن\",\n            MINI: \"Mini\",\n            CROSS: \"Cross\",\n            \"سنعود\": \"سنعود\",\n            \"عدنا\": \"عدنا\" // يبقى بالعربية\n        },\n        // User roles\n        roles: {\n            ADMIN: \"مدير النظام\",\n            CONTENT_MANAGER: \"مدير المحتوى\",\n            MEDIA_MANAGER: \"مدير قاعدة البيانات\",\n            SCHEDULER: \"مجدول البرامج\",\n            FULL_VIEWER: \"مستخدم عرض كامل\",\n            DATA_ENTRY: \"إدخال البيانات\",\n            MAP_SCHEDULER: \"مدير الخريطة والجداول\",\n            VIEWER: \"مستخدم عرض\"\n        },\n        // Role descriptions\n        roleDescriptions: {\n            ADMIN: \"صلاحيات كاملة لإدارة النظام والمستخدمين\",\n            CONTENT_MANAGER: \"إدارة المحتوى والمواد الإعلامية\",\n            MEDIA_MANAGER: \"إدارة قاعدة بيانات المواد\",\n            SCHEDULER: \"إنشاء وتعديل الجداول البرامجية\",\n            FULL_VIEWER: \"عرض جميع البيانات والتقارير\",\n            DATA_ENTRY: \"إدخال وتعديل البيانات الأساسية\",\n            MAP_SCHEDULER: \"إدارة الخريطة البرامجية والجداول\",\n            VIEWER: \"عرض البيانات الأساسية فقط\"\n        }\n    },\n    en: {\n        // Navigation\n        navigation: {\n            dashboard: \"Dashboard\",\n            mediaList: \"Media List\",\n            addMedia: \"Add Media\",\n            weeklySchedule: \"Weekly Schedule\",\n            dailySchedule: \"Daily Schedule\",\n            importSchedule: \"Import Schedule\",\n            statistics: \"Statistics\",\n            adminDashboard: \"User Management\",\n            reports: \"Broadcast Reports\",\n            logout: \"Logout\"\n        },\n        // Common terms\n        common: {\n            name: \"Name\",\n            type: \"Type\",\n            status: \"Status\",\n            actions: \"Actions\",\n            time: \"Time\",\n            duration: \"Duration\",\n            content: \"Content\",\n            code: \"Code\",\n            segments: \"Segments\"\n        },\n        // Media types - أسماء ثابتة حسب المطلوب\n        mediaTypes: {\n            ALL: \"All Types\",\n            PROGRAM: \"Program\",\n            SERIES: \"Series\",\n            FILM: \"Film\",\n            SONG: \"Song\",\n            PROMO: \"Promo\",\n            STING: \"Sting\",\n            FILLER: \"Filler\",\n            NEXT: \"Next\",\n            NOW: \"Now\",\n            MINI: \"Mini\",\n            CROSS: \"Cross\",\n            \"سنعود\": \"We'll Be Back\",\n            \"عدنا\": \"We're Back\" // ترجمة للإنجليزية\n        },\n        // User roles\n        roles: {\n            ADMIN: \"System Administrator\",\n            CONTENT_MANAGER: \"Content Manager\",\n            MEDIA_MANAGER: \"Database Manager\",\n            SCHEDULER: \"Program Scheduler\",\n            FULL_VIEWER: \"Full View User\",\n            DATA_ENTRY: \"Data Entry\",\n            MAP_SCHEDULER: \"Map & Schedule Manager\",\n            VIEWER: \"Viewer\"\n        },\n        // Role descriptions\n        roleDescriptions: {\n            ADMIN: \"Full system administration and user management permissions\",\n            CONTENT_MANAGER: \"Manage content and media materials\",\n            MEDIA_MANAGER: \"Manage media database\",\n            SCHEDULER: \"Create and edit program schedules\",\n            FULL_VIEWER: \"View all data and reports\",\n            DATA_ENTRY: \"Enter and edit basic data\",\n            MAP_SCHEDULER: \"Manage program map and schedules\",\n            VIEWER: \"View basic data only\"\n        }\n    }\n};\n// دالة الترجمة المركزية\nconst getTranslation = (key, language = 'ar')=>{\n    const keys = key.split('.');\n    let value = translations[language];\n    for (const k of keys){\n        if (value && typeof value === 'object' && k in value) {\n            value = value[k];\n        } else {\n            // إذا لم توجد الترجمة، ارجع المفتاح نفسه\n            console.warn(`Translation missing for key: ${key} in language: ${language}`);\n            return key;\n        }\n    }\n    return typeof value === 'string' ? value : key;\n};\n// دالة مساعدة للحصول على ترجمة نوع المادة\nconst getMediaTypeLabel = (type, language = 'ar')=>{\n    return getTranslation(`mediaTypes.${type}`, language);\n};\n// دالة مساعدة للحصول على ترجمة الدور\nconst getRoleLabel = (role, language = 'ar')=>{\n    return getTranslation(`roles.${role}`, language);\n};\n// دالة مساعدة للحصول على وصف الدور\nconst getRoleDescription = (role, language = 'ar')=>{\n    return getTranslation(`roleDescriptions.${role}`, language);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/translations.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();