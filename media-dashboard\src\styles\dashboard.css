/* تحسينات لوحة التحكم */

/* تأثيرات الحركة */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.6);
  }
}

/* تحسينات الاستجابة المتقدمة */

/* الشاشات الكبيرة - أكثر من 1200px */
@media (min-width: 1200px) {
  .navigation-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

/* الشاشات المتوسطة - 768px إلى 1199px */
@media (min-width: 768px) and (max-width: 1199px) {
  .navigation-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }

  .main-content {
    padding: 20px !important;
  }
}

/* الأجهزة اللوحية - 481px إلى 767px */
@media (min-width: 481px) and (max-width: 767px) {
  .navigation-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 15px !important;
  }

  .main-content {
    padding: 15px !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .dashboard-header {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 15px !important;
  }

  .dashboard-status {
    width: 100%;
    justify-content: space-between !important;
  }
}

/* الهواتف المحمولة - أقل من 480px */
@media (max-width: 480px) {
  .navigation-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .main-content {
    padding: 10px !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .dashboard-header {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
    gap: 15px !important;
  }

  .dashboard-status {
    flex-direction: column !important;
    gap: 10px !important;
    width: 100%;
    align-items: center !important;
  }

  .stats-grid {
    grid-template-columns: 1fr !important;
    gap: 15px !important;
  }

  .nav-items {
    display: none !important;
  }

  .mobile-menu {
    display: block !important;
  }

  .top-navbar {
    flex-direction: column !important;
    gap: 15px !important;
    align-items: stretch !important;
  }

  .nav-text {
    display: none !important;
  }

  .user-tools {
    justify-content: center !important;
  }
}

/* تحسينات إضافية للشريط العلوي */
@media (max-width: 768px) {
  .nav-items {
    order: 3;
    width: 100%;
    justify-content: center !important;
  }

  .nav-items button {
    flex: 1;
    min-width: auto !important;
  }
}

@media (max-width: 600px) {
  .nav-items button span.nav-text {
    display: none;
  }

  .nav-items button {
    padding: 8px !important;
    min-width: 40px;
  }
}

/* تحسينات الألوان الداكنة */
.dark-theme {
  --bg-primary: #1a1d29;
  --bg-secondary: #2d3748;
  --text-primary: #ffffff;
  --text-secondary: #a0aec0;
  --accent-primary: #667eea;
  --accent-secondary: #764ba2;
  --success: #68d391;
  --warning: #f6ad55;
  --error: #f56565;
  --info: #4299e1;
}

/* تحسينات التمرير */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #2d3748;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #667eea;
}

/* تحسينات البطاقات */
.stat-card {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.5), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.stat-card:hover::before {
  transform: translateX(100%);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* تحسينات الأزرار */
.btn-primary {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
  border-radius: 8px;
  color: white;
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', Arial, sans-serif;
  font-weight: bold;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: transparent;
  border: 2px solid #4a5568;
  border-radius: 8px;
  color: #a0aec0;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', Arial, sans-serif;
}

.btn-secondary:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

/* تحسينات النصوص */
.gradient-text {
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-glow {
  text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

/* تحسينات الشبكة */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  animation: fadeIn 0.6s ease-out;
}

.navigation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  animation: fadeIn 0.8s ease-out;
}

/* تحسينات إضافية للتصميم المتجاوب */
.main-content {
  transition: all 0.3s ease;
}

.dashboard-header {
  transition: all 0.3s ease;
}

.dashboard-status {
  transition: all 0.3s ease;
}

/* تحسينات للنصوص المتجاوبة */
.responsive-text-large {
  font-size: clamp(1.5rem, 4vw, 2rem);
}

.responsive-text-medium {
  font-size: clamp(1rem, 3vw, 1.2rem);
}

.responsive-text-small {
  font-size: clamp(0.8rem, 2vw, 0.9rem);
}

/* تحسينات للمساحات المتجاوبة */
.responsive-padding {
  padding: clamp(10px, 3vw, 30px);
}

.responsive-margin {
  margin: clamp(10px, 2vw, 20px);
}

.responsive-gap {
  gap: clamp(10px, 2vw, 20px);
}

/* تحسينات الشريط الجانبي */
.sidebar {
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-container {
  backdrop-filter: blur(10px);
}

.sidebar-item {
  position: relative;
  overflow: hidden;
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 100%;
  background: #667eea;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.sidebar-item.active::before,
.sidebar-item:hover::before {
  transform: scaleY(1);
}

/* تحسينات الشريط الجانبي للهواتف المحمولة */
@media (max-width: 768px) {
  .sidebar-container {
    width: min(280px, 85vw) !important;
  }

  .sidebar-header {
    padding: 15px !important;
    min-height: 60px !important;
  }

  .sidebar-menu {
    padding: 15px 0 !important;
  }

  .sidebar-menu-item {
    min-height: 48px !important;
    font-size: 0.9rem !important;
  }

  .sidebar-footer {
    padding: 15px !important;
  }
}

@media (max-width: 480px) {
  .sidebar-container {
    width: min(280px, 90vw) !important;
  }

  .sidebar-header {
    padding: 12px !important;
    min-height: 55px !important;
  }

  .sidebar-menu-item {
    min-height: 44px !important;
    font-size: 0.85rem !important;
    padding: 10px 8px 10px 15px !important;
  }

  .sidebar-footer button {
    min-height: 40px !important;
    font-size: 0.85rem !important;
  }
}

/* تحسينات التمرير للشريط الجانبي */
.sidebar-menu::-webkit-scrollbar {
  width: 4px;
}

.sidebar-menu::-webkit-scrollbar-track {
  background: #1a1d29;
}

.sidebar-menu::-webkit-scrollbar-thumb {
  background: #4a5568;
  border-radius: 2px;
}

.sidebar-menu::-webkit-scrollbar-thumb:hover {
  background: #667eea;
}

/* تحسينات الحالة المتصلة */
.online-indicator {
  position: relative;
}

.online-indicator::before {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #68d391;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* تحسينات التحميل */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #2d3748;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تحسينات الإشعارات */
.notification-badge {
  position: absolute;
  top: -5px;
  left: -5px;
  background: #f56565;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: bold;
  animation: pulse 2s infinite;
}

/* تحسينات الخطوط */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

body {
  font-family: 'Cairo', Arial, sans-serif;
  font-feature-settings: 'liga' 1, 'kern' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* تحسينات إمكانية الوصول */
.focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
