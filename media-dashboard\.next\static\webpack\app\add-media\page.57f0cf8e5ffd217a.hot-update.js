"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/hooks/useAppTranslation.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAppTranslation.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppTranslation: () => (/* binding */ useAppTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// نظام ترجمة احترافي مثل التطبيقات الكبيرة\nconst useAppTranslation = ()=>{\n    const { i18n, t: i18nT } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)('common');\n    const [currentLang, setCurrentLang] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ar');\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تهيئة اللغة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAppTranslation.useEffect\": ()=>{\n            const initLanguage = {\n                \"useAppTranslation.useEffect.initLanguage\": ()=>{\n                    try {\n                        // جلب اللغة المحفوظة أو استخدام العربية كافتراضي\n                        const savedLang = localStorage.getItem('language') || 'ar';\n                        const validLang = savedLang === 'en' || savedLang === 'ar' ? savedLang : 'ar';\n                        setCurrentLang(validLang);\n                        i18n.changeLanguage(validLang);\n                        setIsReady(true);\n                        console.log('🌐 Language initialized:', validLang);\n                    } catch (error) {\n                        console.error('❌ Language initialization error:', error);\n                        setCurrentLang('ar');\n                        setIsReady(true);\n                    }\n                }\n            }[\"useAppTranslation.useEffect.initLanguage\"];\n            initLanguage();\n        }\n    }[\"useAppTranslation.useEffect\"], [\n        i18n\n    ]);\n    // مراقبة تغيير اللغة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAppTranslation.useEffect\": ()=>{\n            const handleLanguageChange = {\n                \"useAppTranslation.useEffect.handleLanguageChange\": (lng)=>{\n                    const validLang = lng === 'en' || lng === 'ar' ? lng : 'ar';\n                    setCurrentLang(validLang);\n                    localStorage.setItem('language', validLang);\n                    console.log('🔄 Language changed to:', validLang);\n                }\n            }[\"useAppTranslation.useEffect.handleLanguageChange\"];\n            i18n.on('languageChanged', handleLanguageChange);\n            return ({\n                \"useAppTranslation.useEffect\": ()=>i18n.off('languageChanged', handleLanguageChange)\n            })[\"useAppTranslation.useEffect\"];\n        }\n    }[\"useAppTranslation.useEffect\"], [\n        i18n\n    ]);\n    // دالة ترجمة آمنة ومضمونة مع interpolation\n    const t = (key, options, fallback)=>{\n        try {\n            if (!isReady) return fallback || key;\n            let translation = i18nT(key, options);\n            // إذا كانت الترجمة مفقودة، استخدم الاحتياطي أو المفتاح\n            if (translation === key && fallback) {\n                translation = fallback;\n            }\n            // معالجة interpolation يدوياً إذا لم يعمل i18next\n            if (options && translation && typeof translation === 'string') {\n                Object.keys(options).forEach((optionKey)=>{\n                    const placeholder = \"{{\".concat(optionKey, \"}}\");\n                    if (translation.includes(placeholder)) {\n                        translation = translation.replace(new RegExp(placeholder, 'g'), String(options[optionKey]));\n                    }\n                });\n            }\n            return translation || fallback || key;\n        } catch (error) {\n            console.error('❌ Translation error for key:', key, error);\n            return fallback || key;\n        }\n    };\n    // دالة تغيير اللغة\n    const changeLanguage = (newLang)=>{\n        try {\n            i18n.changeLanguage(newLang);\n        } catch (error) {\n            console.error('❌ Language change error:', error);\n        }\n    };\n    // دالة ترجمة أنواع المواد (محفوظة كما هي)\n    const tMediaType = (type)=>{\n        var _mediaTypeMap_type;\n        const mediaTypeMap = {\n            'FILM': {\n                ar: 'Film',\n                en: 'Film'\n            },\n            'SERIES': {\n                ar: 'Series',\n                en: 'Series'\n            },\n            'PROGRAM': {\n                ar: 'Program',\n                en: 'Program'\n            },\n            'SONG': {\n                ar: 'Song',\n                en: 'Song'\n            },\n            'FILLER': {\n                ar: 'Filler',\n                en: 'Filler'\n            },\n            'STING': {\n                ar: 'Sting',\n                en: 'Sting'\n            },\n            'PROMO': {\n                ar: 'Promo',\n                en: 'Promo'\n            },\n            'NEXT': {\n                ar: 'Next',\n                en: 'Next'\n            },\n            'NOW': {\n                ar: 'Now',\n                en: 'Now'\n            },\n            'سنعود': {\n                ar: 'سنعود',\n                en: 'سنعود'\n            },\n            'عدنا': {\n                ar: 'عدنا',\n                en: 'عدنا'\n            },\n            'MINI': {\n                ar: 'Mini',\n                en: 'Mini'\n            },\n            'CROSS': {\n                ar: 'Cross',\n                en: 'Cross'\n            },\n            'ALL': {\n                ar: 'جميع الأنواع',\n                en: 'All Types'\n            }\n        };\n        return ((_mediaTypeMap_type = mediaTypeMap[type]) === null || _mediaTypeMap_type === void 0 ? void 0 : _mediaTypeMap_type[currentLang]) || type;\n    };\n    // دالة ترجمة أسماء الأدوار\n    const tRole = (role)=>{\n        var _roleMap_role;\n        const roleMap = {\n            'ADMIN': {\n                ar: 'مدير النظام',\n                en: 'System Admin'\n            },\n            'CONTENT_MANAGER': {\n                ar: 'مدير المحتوى',\n                en: 'Content Manager'\n            },\n            'MEDIA_MANAGER': {\n                ar: 'مدير المواد الإعلامية',\n                en: 'Media Manager'\n            },\n            'SCHEDULER': {\n                ar: 'مجدول البرامج',\n                en: 'Scheduler'\n            },\n            'FULL_VIEWER': {\n                ar: 'مستخدم رؤية كاملة',\n                en: 'Full Viewer'\n            },\n            'DATA_ENTRY': {\n                ar: 'مدخل بيانات',\n                en: 'Data Entry'\n            },\n            'MAP_SCHEDULER': {\n                ar: 'مسؤول الخريطة والجدول',\n                en: 'Map Scheduler'\n            },\n            'VIEWER': {\n                ar: 'مستخدم عرض',\n                en: 'Viewer'\n            }\n        };\n        return ((_roleMap_role = roleMap[role]) === null || _roleMap_role === void 0 ? void 0 : _roleMap_role[currentLang]) || role;\n    };\n    // دالة ترجمة أوصاف الأدوار\n    const tRoleDesc = (role)=>{\n        var _roleDescMap_role;\n        const roleDescMap = {\n            'ADMIN': {\n                ar: 'وصول كامل للنظام وإدارة المستخدمين',\n                en: 'Full system access and user management'\n            },\n            'CONTENT_MANAGER': {\n                ar: 'إدارة المحتوى الإعلامي والجداول',\n                en: 'Media content and schedule management'\n            },\n            'MEDIA_MANAGER': {\n                ar: 'إدارة مكتبة المواد الإعلامية والمحتوى',\n                en: 'Media library and content management'\n            },\n            'SCHEDULER': {\n                ar: 'إنشاء وإدارة جداول البث',\n                en: 'Create and manage broadcast schedules'\n            },\n            'FULL_VIEWER': {\n                ar: 'عرض جميع بيانات النظام والتقارير',\n                en: 'View all system data and reports'\n            },\n            'DATA_ENTRY': {\n                ar: 'إضافة وتعديل المحتوى الإعلامي',\n                en: 'Add and edit media content'\n            },\n            'MAP_SCHEDULER': {\n                ar: 'إدارة خرائط البرامج والجداول الإذاعية اليومية مع عرض قاعدة البيانات بدون تعديل',\n                en: 'Manage program maps and daily schedules with database view without editing'\n            },\n            'VIEWER': {\n                ar: 'عرض المحتوى فقط بدون إمكانيات التعديل أو الإضافة',\n                en: 'View content only without editing or adding capabilities'\n            }\n        };\n        return ((_roleDescMap_role = roleDescMap[role]) === null || _roleDescMap_role === void 0 ? void 0 : _roleDescMap_role[currentLang]) || role;\n    };\n    return {\n        t,\n        tMediaType,\n        tRole,\n        tRoleDesc,\n        currentLang,\n        isRTL: currentLang === 'ar',\n        isReady,\n        changeLanguage,\n        i18n\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAppTranslation.ts\n"));

/***/ })

});