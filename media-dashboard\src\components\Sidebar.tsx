'use client';

import React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from './AuthGuard';
import { useAppTranslation } from '@/hooks/useAppTranslation';
import Logo from './Logo';


interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

interface MenuItem {
  name: string;
  icon: string;
  path: string;
  permission: string | null;
  adminOnly?: boolean;
}

export default function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, hasPermission } = useAuth();
  const { t, isRTL } = useAppTranslation();

  const menuItems: MenuItem[] = [
    {
      name: t('navigation.dashboard'),
      icon: '📊',
      path: '/dashboard',
      permission: null
    },
    {
      name: t('navigation.mediaList'),
      icon: '🎬',
      path: '/media-list',
      permission: 'MEDIA_READ'
    },
    {
      name: t('navigation.addMedia'),
      icon: '➕',
      path: '/add-media',
      permission: 'MEDIA_CREATE'
    },
    {
      name: t('navigation.weeklySchedule'),
      icon: '📅',
      path: '/weekly-schedule',
      permission: 'SCHEDULE_READ'
    },
    {
      name: t('navigation.dailySchedule'),
      icon: '📊',
      path: '/daily-schedule',
      permission: 'SCHEDULE_READ'
    },
    {
      name: t('navigation.reports'),
      icon: '📋',
      path: '/reports',
      permission: 'SCHEDULE_READ'
    },
    {
      name: t('navigation.importSchedule'),
      icon: '📤',
      path: '/daily-schedule/import',
      permission: 'SCHEDULE_READ'
    },
    {
      name: t('navigation.adminDashboard'),
      icon: '👥',
      path: '/admin-dashboard',
      permission: null
    },
    {
      name: t('navigation.statistics'),
      icon: '📈',
      path: '/statistics',
      permission: null
    }
  ];

  const filteredMenuItems = menuItems.filter(item => {
    if (item.adminOnly && user?.role !== 'ADMIN') return false;
    if (item.permission && !hasPermission(item.permission)) return false;
    return true;
  });

  return (
    <div>
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(0, 0, 0, 0.5)',
            zIndex: 998,
            display: window.innerWidth <= 768 ? 'block' : 'none'
          }}
          onClick={onToggle}
        />
      )}

      <div
        className="sidebar-container"
        style={{
          position: 'fixed',
          top: 0,
          ...(isRTL ? {
            right: isOpen ? 0 : '-280px',
            borderLeft: '1px solid #2d3748'
          } : {
            left: isOpen ? 0 : '-280px',
            borderRight: '1px solid #2d3748'
          }),
          width: 'min(280px, 90vw)',
          height: '100vh',
          background: '#1a1d29',
          transition: `${isRTL ? 'right' : 'left'} 0.3s ease`,
          zIndex: 999,
          display: 'flex',
          flexDirection: 'column',
          fontFamily: 'Cairo, Arial, sans-serif',
          boxShadow: isOpen ? '0 0 20px rgba(0, 0, 0, 0.5)' : 'none'
        }}
      >
        <div className="sidebar-header" style={{
          padding: 'clamp(15px, 3vw, 20px)',
          borderBottom: '1px solid #2d3748',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          minHeight: '70px'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px', flex: 1 }}>
            <div style={{ flex: 1 }}>
              <Logo size="small" style={{ color: 'white' }} />
              <p style={{
                color: '#a0aec0',
                margin: 0,
                fontSize: 'clamp(0.7rem, 2vw, 0.8rem)',
                lineHeight: 1.3
              }}>
                {t('dashboard.subtitle')}
              </p>
            </div>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
            <button
              onClick={onToggle}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: 'clamp(1rem, 3vw, 1.2rem)',
                cursor: 'pointer',
                padding: '8px',
                borderRadius: '4px',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(102, 126, 234, 0.1)';
                e.target.style.color = '#667eea';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'transparent';
                e.target.style.color = '#a0aec0';
              }}
            >
              ✕
            </button>
          </div>
        </div>

        <div className="sidebar-menu" style={{
          flex: 1,
          padding: 'clamp(15px, 3vw, 20px) 0',
          overflowY: 'auto'
        }}>
          {filteredMenuItems.map((item, index) => {
            const isActive = pathname === item.path;
            return (
              <button
                key={index}
                className="sidebar-menu-item"
                onClick={() => {
                  router.push(item.path);
                  if (window.innerWidth <= 768) {
                    onToggle();
                  }
                }}
                style={{
                  width: '100%',
                  background: isActive ? '#2d3748' : 'transparent',
                  color: isActive ? 'white' : '#a0aec0',
                  border: 'none',
                  borderTop: 'none',
                  borderBottom: 'none',
                  ...(isRTL ? {
                    borderLeft: 'none',
                    borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'
                  } : {
                    borderRight: 'none',
                    borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'
                  }),
                  padding: isRTL ? 'clamp(10px, 2.5vw, 12px) clamp(15px, 4vw, 20px) clamp(10px, 2.5vw, 12px) clamp(6px, 2vw, 8px)' : 'clamp(10px, 2.5vw, 12px) clamp(6px, 2vw, 8px) clamp(10px, 2.5vw, 12px) clamp(15px, 4vw, 20px)',
                  textAlign: isRTL ? 'right' : 'left',
                  cursor: 'pointer',
                  fontSize: 'clamp(0.8rem, 2.5vw, 0.9rem)',
                  fontWeight: 'bold',
                  transition: 'all 0.2s ease',
                  direction: isRTL ? 'rtl' : 'ltr',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 'clamp(8px, 2vw, 12px)',
                  minHeight: '44px'
                }}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.target.style.background = 'rgba(102, 126, 234, 0.1)';
                    e.target.style.color = '#667eea';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.target.style.background = 'transparent';
                    e.target.style.color = '#a0aec0';
                  }
                }}
              >
                <span style={{ fontSize: 'clamp(1rem, 3vw, 1.2rem)' }}>{item.icon}</span>
                <span style={{ flex: 1 }}>{item.name}</span>
              </button>
            );
          })}
        </div>

        <div className="sidebar-footer" style={{
          padding: 'clamp(15px, 3vw, 20px)',
          borderTop: '1px solid #2d3748'
        }}>
          <button
            onClick={() => {
              localStorage.removeItem('user');
              localStorage.removeItem('token');
              router.push('/login');
            }}
            style={{
              width: '100%',
              background: 'linear-gradient(45deg, #f56565, #e53e3e)',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              padding: 'clamp(10px, 2.5vw, 12px)',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 'clamp(6px, 2vw, 8px)',
              fontSize: 'clamp(0.8rem, 2.5vw, 0.9rem)',
              fontWeight: 'bold',
              marginBottom: 'clamp(10px, 2.5vw, 15px)',
              transition: 'all 0.2s ease',
              minHeight: '44px'
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = 'translateY(-2px)';
              e.target.style.boxShadow = '0 4px 12px rgba(245, 101, 101, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = 'translateY(0)';
              e.target.style.boxShadow = 'none';
            }}
          >
            <span style={{ fontSize: 'clamp(1rem, 3vw, 1.2rem)' }}>🚪</span>
            <span>{t('navigation.logout')}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
