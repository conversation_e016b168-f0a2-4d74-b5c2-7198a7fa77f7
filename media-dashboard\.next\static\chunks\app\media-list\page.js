/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/media-list/page"],{

/***/ "(app-pages-browser)/./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var void_elements__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! void-elements */ \"(app-pages-browser)/./node_modules/void-elements/index.js\");\n/* harmony import */ var void_elements__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(void_elements__WEBPACK_IMPORTED_MODULE_0__);\nvar t=/\\s([^'\"/\\s><]+?)[\\s/>]|([^\\s=]+)=\\s?(\".*?\"|'.*?')/g;function n(n){var r={type:\"tag\",name:\"\",voidElement:!1,attrs:{},children:[]},i=n.match(/<\\/?([^\\s]+?)[/\\s>]/);if(i&&(r.name=i[1],((void_elements__WEBPACK_IMPORTED_MODULE_0___default())[i[1]]||\"/\"===n.charAt(n.length-2))&&(r.voidElement=!0),r.name.startsWith(\"!--\"))){var s=n.indexOf(\"--\\x3e\");return{type:\"comment\",comment:-1!==s?n.slice(4,s):\"\"}}for(var a=new RegExp(t),c=null;null!==(c=a.exec(n));)if(c[0].trim())if(c[1]){var o=c[1].trim(),l=[o,\"\"];o.indexOf(\"=\")>-1&&(l=o.split(\"=\")),r.attrs[l[0]]=l[1],a.lastIndex--}else c[2]&&(r.attrs[c[2]]=c[3].trim().substring(1,c[3].length-1));return r}var r=/<[a-zA-Z0-9\\-\\!\\/](?:\"[^\"]*\"|'[^']*'|[^'\">])*>/g,i=/^\\s*$/,s=Object.create(null);function a(e,t){switch(t.type){case\"text\":return e+t.content;case\"tag\":return e+=\"<\"+t.name+(t.attrs?function(e){var t=[];for(var n in e)t.push(n+'=\"'+e[n]+'\"');return t.length?\" \"+t.join(\" \"):\"\"}(t.attrs):\"\")+(t.voidElement?\"/>\":\">\"),t.voidElement?e:e+t.children.reduce(a,\"\")+\"</\"+t.name+\">\";case\"comment\":return e+\"\\x3c!--\"+t.comment+\"--\\x3e\"}}var c={parse:function(e,t){t||(t={}),t.components||(t.components=s);var a,c=[],o=[],l=-1,m=!1;if(0!==e.indexOf(\"<\")){var u=e.indexOf(\"<\");c.push({type:\"text\",content:-1===u?e:e.substring(0,u)})}return e.replace(r,function(r,s){if(m){if(r!==\"</\"+a.name+\">\")return;m=!1}var u,f=\"/\"!==r.charAt(1),h=r.startsWith(\"\\x3c!--\"),p=s+r.length,d=e.charAt(p);if(h){var v=n(r);return l<0?(c.push(v),c):((u=o[l]).children.push(v),c)}if(f&&(l++,\"tag\"===(a=n(r)).type&&t.components[a.name]&&(a.type=\"component\",m=!0),a.voidElement||m||!d||\"<\"===d||a.children.push({type:\"text\",content:e.slice(p,e.indexOf(\"<\",p))}),0===l&&c.push(a),(u=o[l-1])&&u.children.push(a),o[l]=a),(!f||a.voidElement)&&(l>-1&&(a.voidElement||a.name===r.slice(2,-1))&&(l--,a=-1===l?c:o[l]),!m&&\"<\"!==d&&d)){u=-1===l?c:o[l].children;var x=e.indexOf(\"<\",p),g=e.slice(p,-1===x?void 0:x);i.test(g)&&(g=\" \"),(x>-1&&l+u.length>=0||\" \"!==g)&&u.push({type:\"text\",content:g})}}),c},stringify:function(e){return e.reduce(function(e,t){return e+a(\"\",t)},\"\")}};/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (c);\n//# sourceMappingURL=html-parse-stringify.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9odG1sLXBhcnNlLXN0cmluZ2lmeS9kaXN0L2h0bWwtcGFyc2Utc3RyaW5naWZ5Lm1vZHVsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNkIsMkRBQTJELGNBQWMsT0FBTywwQ0FBMEMsYUFBYSxrQ0FBa0Msb0JBQW9CLHNEQUFDLG1GQUFtRiwwQkFBMEIsT0FBTywrQ0FBK0MsK0JBQStCLHFCQUFxQix5QkFBeUIsMkJBQTJCLHFFQUFxRSxrRUFBa0UsU0FBUyx3RkFBd0YsZ0JBQWdCLGVBQWUsOEJBQThCLG9EQUFvRCxTQUFTLHVDQUF1QyxtQ0FBbUMsaUdBQWlHLHFEQUFxRCxPQUFPLG9CQUFvQixRQUFRLGlDQUFpQywwQkFBMEIsdUJBQXVCLHFCQUFxQixRQUFRLDhDQUE4QyxFQUFFLGlDQUFpQyxNQUFNLDhCQUE4QixLQUFLLCtFQUErRSxNQUFNLFdBQVcsdURBQXVELGtJQUFrSSxnREFBZ0Qsc0tBQXNLLHlCQUF5QixvREFBb0QsMkRBQTJELHNCQUFzQixHQUFHLElBQUksdUJBQXVCLDhCQUE4QixpQkFBaUIsT0FBTyxpRUFBZSxDQUFDLEVBQUM7QUFDdGlFIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcaHRtbC1wYXJzZS1zdHJpbmdpZnlcXGRpc3RcXGh0bWwtcGFyc2Utc3RyaW5naWZ5Lm1vZHVsZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZSBmcm9tXCJ2b2lkLWVsZW1lbnRzXCI7dmFyIHQ9L1xccyhbXidcIi9cXHM+PF0rPylbXFxzLz5dfChbXlxccz1dKyk9XFxzPyhcIi4qP1wifCcuKj8nKS9nO2Z1bmN0aW9uIG4obil7dmFyIHI9e3R5cGU6XCJ0YWdcIixuYW1lOlwiXCIsdm9pZEVsZW1lbnQ6ITEsYXR0cnM6e30sY2hpbGRyZW46W119LGk9bi5tYXRjaCgvPFxcLz8oW15cXHNdKz8pWy9cXHM+XS8pO2lmKGkmJihyLm5hbWU9aVsxXSwoZVtpWzFdXXx8XCIvXCI9PT1uLmNoYXJBdChuLmxlbmd0aC0yKSkmJihyLnZvaWRFbGVtZW50PSEwKSxyLm5hbWUuc3RhcnRzV2l0aChcIiEtLVwiKSkpe3ZhciBzPW4uaW5kZXhPZihcIi0tXFx4M2VcIik7cmV0dXJue3R5cGU6XCJjb21tZW50XCIsY29tbWVudDotMSE9PXM/bi5zbGljZSg0LHMpOlwiXCJ9fWZvcih2YXIgYT1uZXcgUmVnRXhwKHQpLGM9bnVsbDtudWxsIT09KGM9YS5leGVjKG4pKTspaWYoY1swXS50cmltKCkpaWYoY1sxXSl7dmFyIG89Y1sxXS50cmltKCksbD1bbyxcIlwiXTtvLmluZGV4T2YoXCI9XCIpPi0xJiYobD1vLnNwbGl0KFwiPVwiKSksci5hdHRyc1tsWzBdXT1sWzFdLGEubGFzdEluZGV4LS19ZWxzZSBjWzJdJiYoci5hdHRyc1tjWzJdXT1jWzNdLnRyaW0oKS5zdWJzdHJpbmcoMSxjWzNdLmxlbmd0aC0xKSk7cmV0dXJuIHJ9dmFyIHI9LzxbYS16QS1aMC05XFwtXFwhXFwvXSg/OlwiW15cIl0qXCJ8J1teJ10qJ3xbXidcIj5dKSo+L2csaT0vXlxccyokLyxzPU9iamVjdC5jcmVhdGUobnVsbCk7ZnVuY3Rpb24gYShlLHQpe3N3aXRjaCh0LnR5cGUpe2Nhc2VcInRleHRcIjpyZXR1cm4gZSt0LmNvbnRlbnQ7Y2FzZVwidGFnXCI6cmV0dXJuIGUrPVwiPFwiK3QubmFtZSsodC5hdHRycz9mdW5jdGlvbihlKXt2YXIgdD1bXTtmb3IodmFyIG4gaW4gZSl0LnB1c2gobisnPVwiJytlW25dKydcIicpO3JldHVybiB0Lmxlbmd0aD9cIiBcIit0LmpvaW4oXCIgXCIpOlwiXCJ9KHQuYXR0cnMpOlwiXCIpKyh0LnZvaWRFbGVtZW50P1wiLz5cIjpcIj5cIiksdC52b2lkRWxlbWVudD9lOmUrdC5jaGlsZHJlbi5yZWR1Y2UoYSxcIlwiKStcIjwvXCIrdC5uYW1lK1wiPlwiO2Nhc2VcImNvbW1lbnRcIjpyZXR1cm4gZStcIlxceDNjIS0tXCIrdC5jb21tZW50K1wiLS1cXHgzZVwifX12YXIgYz17cGFyc2U6ZnVuY3Rpb24oZSx0KXt0fHwodD17fSksdC5jb21wb25lbnRzfHwodC5jb21wb25lbnRzPXMpO3ZhciBhLGM9W10sbz1bXSxsPS0xLG09ITE7aWYoMCE9PWUuaW5kZXhPZihcIjxcIikpe3ZhciB1PWUuaW5kZXhPZihcIjxcIik7Yy5wdXNoKHt0eXBlOlwidGV4dFwiLGNvbnRlbnQ6LTE9PT11P2U6ZS5zdWJzdHJpbmcoMCx1KX0pfXJldHVybiBlLnJlcGxhY2UocixmdW5jdGlvbihyLHMpe2lmKG0pe2lmKHIhPT1cIjwvXCIrYS5uYW1lK1wiPlwiKXJldHVybjttPSExfXZhciB1LGY9XCIvXCIhPT1yLmNoYXJBdCgxKSxoPXIuc3RhcnRzV2l0aChcIlxceDNjIS0tXCIpLHA9cytyLmxlbmd0aCxkPWUuY2hhckF0KHApO2lmKGgpe3ZhciB2PW4ocik7cmV0dXJuIGw8MD8oYy5wdXNoKHYpLGMpOigodT1vW2xdKS5jaGlsZHJlbi5wdXNoKHYpLGMpfWlmKGYmJihsKyssXCJ0YWdcIj09PShhPW4ocikpLnR5cGUmJnQuY29tcG9uZW50c1thLm5hbWVdJiYoYS50eXBlPVwiY29tcG9uZW50XCIsbT0hMCksYS52b2lkRWxlbWVudHx8bXx8IWR8fFwiPFwiPT09ZHx8YS5jaGlsZHJlbi5wdXNoKHt0eXBlOlwidGV4dFwiLGNvbnRlbnQ6ZS5zbGljZShwLGUuaW5kZXhPZihcIjxcIixwKSl9KSwwPT09bCYmYy5wdXNoKGEpLCh1PW9bbC0xXSkmJnUuY2hpbGRyZW4ucHVzaChhKSxvW2xdPWEpLCghZnx8YS52b2lkRWxlbWVudCkmJihsPi0xJiYoYS52b2lkRWxlbWVudHx8YS5uYW1lPT09ci5zbGljZSgyLC0xKSkmJihsLS0sYT0tMT09PWw/YzpvW2xdKSwhbSYmXCI8XCIhPT1kJiZkKSl7dT0tMT09PWw/YzpvW2xdLmNoaWxkcmVuO3ZhciB4PWUuaW5kZXhPZihcIjxcIixwKSxnPWUuc2xpY2UocCwtMT09PXg/dm9pZCAwOngpO2kudGVzdChnKSYmKGc9XCIgXCIpLCh4Pi0xJiZsK3UubGVuZ3RoPj0wfHxcIiBcIiE9PWcpJiZ1LnB1c2goe3R5cGU6XCJ0ZXh0XCIsY29udGVudDpnfSl9fSksY30sc3RyaW5naWZ5OmZ1bmN0aW9uKGUpe3JldHVybiBlLnJlZHVjZShmdW5jdGlvbihlLHQpe3JldHVybiBlK2EoXCJcIix0KX0sXCJcIil9fTtleHBvcnQgZGVmYXVsdCBjO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aHRtbC1wYXJzZS1zdHJpbmdpZnkubW9kdWxlLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/api/navigation.js":
/*!**************************************************!*\
  !*** ./node_modules/next/dist/api/navigation.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../client/components/navigation */ \"(app-pages-browser)/./node_modules/next/dist/client/components/navigation.js\");\n/* harmony import */ var _client_components_navigation__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_client_components_navigation__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => _client_components_navigation__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n//# sourceMappingURL=navigation.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL25hdmlnYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdEOztBQUVoRCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGFwaVxcbmF2aWdhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tICcuLi9jbGllbnQvY29tcG9uZW50cy9uYXZpZ2F0aW9uJztcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bmF2aWdhdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/navigation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/media-list/page.tsx */ \"(app-pages-browser)/./src/app/media-list/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q3Byb2plY3QlMjBzcG9ydCU1QyU1Q21lZGlhLWRhc2hib2FyZC1jbGVhbiU1QyU1Q21lZGlhLWRhc2hib2FyZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q21lZGlhLWxpc3QlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUE4SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccHJvamVjdCBzcG9ydFxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbWVkaWEtbGlzdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/I18nextProvider.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/I18nextProvider.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nextProvider: () => (/* binding */ I18nextProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nfunction I18nextProvider({\n  i18n,\n  defaultNS,\n  children\n}) {\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    i18n,\n    defaultNS\n  }), [i18n, defaultNS]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext.Provider, {\n    value\n  }, children);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvSTE4bmV4dFByb3ZpZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUNKO0FBQ3BDO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELGdCQUFnQiw4Q0FBTztBQUN2QjtBQUNBO0FBQ0EsR0FBRztBQUNILFNBQVMsb0RBQWEsQ0FBQyxvREFBVztBQUNsQztBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxJMThuZXh0UHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlRWxlbWVudCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IEkxOG5Db250ZXh0IH0gZnJvbSAnLi9jb250ZXh0LmpzJztcbmV4cG9ydCBmdW5jdGlvbiBJMThuZXh0UHJvdmlkZXIoe1xuICBpMThuLFxuICBkZWZhdWx0TlMsXG4gIGNoaWxkcmVuXG59KSB7XG4gIGNvbnN0IHZhbHVlID0gdXNlTWVtbygoKSA9PiAoe1xuICAgIGkxOG4sXG4gICAgZGVmYXVsdE5TXG4gIH0pLCBbaTE4biwgZGVmYXVsdE5TXSk7XG4gIHJldHVybiBjcmVhdGVFbGVtZW50KEkxOG5Db250ZXh0LlByb3ZpZGVyLCB7XG4gICAgdmFsdWVcbiAgfSwgY2hpbGRyZW4pO1xufSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/I18nextProvider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/Trans.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Trans.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\nfunction Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_2__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const t = tFromProps || i18n?.t.bind(i18n);\n  return (0,_TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans)({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t?.ns || defaultNSFromContext || i18n?.options?.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/Trans.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/TransWithoutContext.js":
/*!*******************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/TransWithoutContext.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Trans: () => (/* binding */ Trans),\n/* harmony export */   nodesToString: () => (/* binding */ nodesToString)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! html-parse-stringify */ \"(app-pages-browser)/./node_modules/html-parse-stringify/dist/html-parse-stringify.module.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./defaults.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./i18nInstance.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\n\n\n\nconst hasChildren = (node, checkLength) => {\n  if (!node) return false;\n  const base = node.props?.children ?? node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n};\nconst getChildren = node => {\n  if (!node) return [];\n  const children = node.props?.children ?? node.children;\n  return node.props?.i18nIsDynamicList ? getAsArray(children) : children;\n};\nconst hasValidReactChildren = children => Array.isArray(children) && children.every(react__WEBPACK_IMPORTED_MODULE_0__.isValidElement);\nconst getAsArray = data => Array.isArray(data) ? data : [data];\nconst mergeProps = (source, target) => {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n};\nconst nodesToString = (children, i18nOptions, i18n, i18nKey) => {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions?.transSupportBasicHtmlNodes ? i18nOptions.transKeepBasicHtmlNodesFor ?? [] : [];\n  childrenArray.forEach((child, childIndex) => {\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) {\n      stringNode += `${child}`;\n      return;\n    }\n    if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) {\n      const {\n        props,\n        type\n      } = child;\n      const childPropsCount = Object.keys(props).length;\n      const shouldKeepChild = keepArray.indexOf(type) > -1;\n      const childChildren = props.children;\n      if (!childChildren && shouldKeepChild && !childPropsCount) {\n        stringNode += `<${type}/>`;\n        return;\n      }\n      if (!childChildren && (!shouldKeepChild || childPropsCount) || props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n        return;\n      }\n      if (shouldKeepChild && childPropsCount === 1 && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(childChildren)) {\n        stringNode += `<${type}>${childChildren}</${type}>`;\n        return;\n      }\n      const content = nodesToString(childChildren, i18nOptions, i18n, i18nKey);\n      stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      return;\n    }\n    if (child === null) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_NULL_VALUE', `Passed in a null value as child`, {\n        i18nKey\n      });\n      return;\n    }\n    if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child)) {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n        return;\n      }\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_INVALID_OBJ', `Invalid child - Object should only have keys {{ value, format }} (format is optional).`, {\n        i18nKey,\n        child\n      });\n      return;\n    }\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warn)(i18n, 'TRANS_INVALID_VAR', `Passed in a variable like {number} - pass variables for interpolation as full objects like {{number}}.`, {\n      i18nKey,\n      child\n    });\n  });\n  return stringNode;\n};\nconst renderNodes = (children, knownComponentsMap, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) => {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !knownComponentsMap && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = knownComponentsMap ?? {};\n  const getData = childs => {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) return;\n      if (hasChildren(child)) getData(getChildren(child));else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && !(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child)) Object.assign(data, child);\n    });\n  };\n  getData(children);\n  const ast = html_parse_stringify__WEBPACK_IMPORTED_MODULE_1__[\"default\"].parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  const renderInner = (child, node, rootReactNode) => {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props?.i18nIsDynamicList ? childs : mappedChildren;\n  };\n  const pushTranslatedJSX = (child, inner, mem, i, isVoid) => {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...react__WEBPACK_IMPORTED_MODULE_0__.Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(c.type, {\n          ...props,\n          key: i,\n          ref: c.props.ref ?? c.ref\n        }, isVoid ? null : inner);\n      }));\n    }\n  };\n  const mapAST = (reactNode, astNode, rootReactNode) => {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children?.[0]?.content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (!tmp && knownComponentsMap) tmp = knownComponentsMap[node.name];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = (0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && child.dummy && !isElement;\n        const isKnownComponent = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(knownComponentsMap) && Object.hasOwnProperty.call(knownComponentsMap, node.name);\n        if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(child)) {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(child) && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push((0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  };\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n};\nconst fixComponentProps = (component, index, translation) => {\n  const componentKey = component.key || index;\n  const comp = (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(component, {\n    key: componentKey\n  });\n  if (!comp.props || !comp.props.children || translation.indexOf(`${index}/>`) < 0 && translation.indexOf(`${index} />`) < 0) {\n    return comp;\n  }\n  function Componentized() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, comp);\n  }\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Componentized, {\n    key: componentKey\n  });\n};\nconst generateArrayComponents = (components, translation) => components.map((c, index) => fixComponentProps(c, index, translation));\nconst generateObjectComponents = (components, translation) => {\n  const componentMap = {};\n  Object.keys(components).forEach(c => {\n    Object.assign(componentMap, {\n      [c]: fixComponentProps(components[c], c, translation)\n    });\n  });\n  return componentMap;\n};\nconst generateComponents = (components, translation, i18n, i18nKey) => {\n  if (!components) return null;\n  if (Array.isArray(components)) {\n    return generateArrayComponents(components, translation);\n  }\n  if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(components)) {\n    return generateObjectComponents(components, translation);\n  }\n  (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'TRANS_INVALID_COMPONENTS', `<Trans /> \"components\" prop expects an object or array`, {\n    i18nKey\n  });\n  return null;\n};\nconst isComponentsMap = object => {\n  if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(object)) return false;\n  if (Array.isArray(object)) return false;\n  return Object.keys(object).reduce((acc, key) => acc && Number.isNaN(Number.parseFloat(key)), true);\n};\nfunction Trans({\n  children,\n  count,\n  parent,\n  i18nKey,\n  context,\n  tOptions = {},\n  values,\n  defaults,\n  components,\n  ns,\n  i18n: i18nFromProps,\n  t: tFromProps,\n  shouldUnescape,\n  ...additionalProps\n}) {\n  const i18n = i18nFromProps || (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_4__.getI18n)();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'NO_I18NEXT_INSTANCE', `Trans: You need to pass in an i18next instance using i18nextReactModule`, {\n      i18nKey\n    });\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  const reactI18nextOptions = {\n    ...(0,_defaults_js__WEBPACK_IMPORTED_MODULE_3__.getDefaults)(),\n    ...i18n.options?.react\n  };\n  let namespaces = ns || t.ns || i18n.options?.defaultNS;\n  namespaces = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(namespaces) ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions, i18n, i18nKey);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options?.interpolation?.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values || count !== undefined && !i18n.options?.interpolation?.alwaysFormat || !children ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    context: context || tOptions.context,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  const generatedComponents = generateComponents(components, translation, i18n, i18nKey);\n  let indexedChildren = generatedComponents || children;\n  let componentsMap = null;\n  if (isComponentsMap(generatedComponents)) {\n    componentsMap = generatedComponents;\n    indexedChildren = children;\n  }\n  const content = renderNodes(indexedChildren, componentsMap, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent ?? reactI18nextOptions.defaultTransParent;\n  return useAsParent ? (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(useAsParent, additionalProps, content) : content;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/Translation.js":
/*!***********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/Translation.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Translation: () => (/* binding */ Translation)\n/* harmony export */ });\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useTranslation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n\nconst Translation = ({\n  ns,\n  children,\n  ...options\n}) => {\n  const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_0__.useTranslation)(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvVHJhbnNsYXRpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUQ7QUFDOUM7QUFDUDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsMkJBQTJCLGtFQUFjO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxUcmFuc2xhdGlvbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJy4vdXNlVHJhbnNsYXRpb24uanMnO1xuZXhwb3J0IGNvbnN0IFRyYW5zbGF0aW9uID0gKHtcbiAgbnMsXG4gIGNoaWxkcmVuLFxuICAuLi5vcHRpb25zXG59KSA9PiB7XG4gIGNvbnN0IFt0LCBpMThuLCByZWFkeV0gPSB1c2VUcmFuc2xhdGlvbihucywgb3B0aW9ucyk7XG4gIHJldHVybiBjaGlsZHJlbih0LCB7XG4gICAgaTE4bixcbiAgICBsbmc6IGkxOG4ubGFuZ3VhZ2VcbiAgfSwgcmVhZHkpO1xufTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/Translation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/context.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* binding */ I18nContext),\n/* harmony export */   ReportNamespaces: () => (/* binding */ ReportNamespaces),\n/* harmony export */   composeInitialProps: () => (/* binding */ composeInitialProps),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n),\n/* harmony export */   getInitialProps: () => (/* binding */ getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__.initReactI18next),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_1__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.setI18n)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./defaults.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./i18nInstance.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./initReactI18next.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n\n\n\n\n\nconst I18nContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)();\nclass ReportNamespaces {\n  constructor() {\n    this.usedNamespaces = {};\n  }\n  addUsedNamespaces(namespaces) {\n    namespaces.forEach(ns => {\n      if (!this.usedNamespaces[ns]) this.usedNamespaces[ns] = true;\n    });\n  }\n  getUsedNamespaces() {\n    return Object.keys(this.usedNamespaces);\n  }\n}\nconst composeInitialProps = ForComponent => async ctx => {\n  const componentsInitialProps = (await ForComponent.getInitialProps?.(ctx)) ?? {};\n  const i18nInitialProps = getInitialProps();\n  return {\n    ...componentsInitialProps,\n    ...i18nInitialProps\n  };\n};\nconst getInitialProps = () => {\n  const i18n = (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_2__.getI18n)();\n  const namespaces = i18n.reportNamespaces?.getUsedNamespaces() ?? [];\n  const ret = {};\n  const initialI18nStore = {};\n  i18n.languages.forEach(l => {\n    initialI18nStore[l] = {};\n    namespaces.forEach(ns => {\n      initialI18nStore[l][ns] = i18n.getResourceBundle(l, ns) || {};\n    });\n  });\n  ret.initialI18nStore = initialI18nStore;\n  ret.initialLanguage = i18n.language;\n  return ret;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/defaults.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDefaults: () => (/* binding */ getDefaults),\n/* harmony export */   setDefaults: () => (/* binding */ setDefaults)\n/* harmony export */ });\n/* harmony import */ var _unescape_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./unescape.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/unescape.js\");\n\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape: _unescape_js__WEBPACK_IMPORTED_MODULE_0__.unescape\n};\nconst setDefaults = (options = {}) => {\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n};\nconst getDefaults = () => defaultOptions;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvZGVmYXVsdHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXlDO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDTyxpQ0FBaUM7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXGRlZmF1bHRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVuZXNjYXBlIH0gZnJvbSAnLi91bmVzY2FwZS5qcyc7XG5sZXQgZGVmYXVsdE9wdGlvbnMgPSB7XG4gIGJpbmRJMThuOiAnbGFuZ3VhZ2VDaGFuZ2VkJyxcbiAgYmluZEkxOG5TdG9yZTogJycsXG4gIHRyYW5zRW1wdHlOb2RlVmFsdWU6ICcnLFxuICB0cmFuc1N1cHBvcnRCYXNpY0h0bWxOb2RlczogdHJ1ZSxcbiAgdHJhbnNXcmFwVGV4dE5vZGVzOiAnJyxcbiAgdHJhbnNLZWVwQmFzaWNIdG1sTm9kZXNGb3I6IFsnYnInLCAnc3Ryb25nJywgJ2knLCAncCddLFxuICB1c2VTdXNwZW5zZTogdHJ1ZSxcbiAgdW5lc2NhcGVcbn07XG5leHBvcnQgY29uc3Qgc2V0RGVmYXVsdHMgPSAob3B0aW9ucyA9IHt9KSA9PiB7XG4gIGRlZmF1bHRPcHRpb25zID0ge1xuICAgIC4uLmRlZmF1bHRPcHRpb25zLFxuICAgIC4uLm9wdGlvbnNcbiAgfTtcbn07XG5leHBvcnQgY29uc3QgZ2V0RGVmYXVsdHMgPSAoKSA9PiBkZWZhdWx0T3B0aW9uczsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js":
/*!************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/i18nInstance.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getI18n: () => (/* binding */ getI18n),\n/* harmony export */   setI18n: () => (/* binding */ setI18n)\n/* harmony export */ });\nlet i18nInstance;\nconst setI18n = instance => {\n  i18nInstance = instance;\n};\nconst getI18n = () => i18nInstance;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvaTE4bkluc3RhbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFxpMThuSW5zdGFuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGkxOG5JbnN0YW5jZTtcbmV4cG9ydCBjb25zdCBzZXRJMThuID0gaW5zdGFuY2UgPT4ge1xuICBpMThuSW5zdGFuY2UgPSBpbnN0YW5jZTtcbn07XG5leHBvcnQgY29uc3QgZ2V0STE4biA9ICgpID0+IGkxOG5JbnN0YW5jZTsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.I18nContext),\n/* harmony export */   I18nextProvider: () => (/* reexport safe */ _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__.I18nextProvider),\n/* harmony export */   Trans: () => (/* reexport safe */ _Trans_js__WEBPACK_IMPORTED_MODULE_0__.Trans),\n/* harmony export */   TransWithoutContext: () => (/* reexport safe */ _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__.Trans),\n/* harmony export */   Translation: () => (/* reexport safe */ _Translation_js__WEBPACK_IMPORTED_MODULE_4__.Translation),\n/* harmony export */   composeInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.composeInitialProps),\n/* harmony export */   date: () => (/* binding */ date),\n/* harmony export */   getDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.getDefaults),\n/* harmony export */   getI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.getI18n),\n/* harmony export */   getInitialProps: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_11__.getInitialProps),\n/* harmony export */   initReactI18next: () => (/* reexport safe */ _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__.initReactI18next),\n/* harmony export */   number: () => (/* binding */ number),\n/* harmony export */   plural: () => (/* binding */ plural),\n/* harmony export */   select: () => (/* binding */ select),\n/* harmony export */   selectOrdinal: () => (/* binding */ selectOrdinal),\n/* harmony export */   setDefaults: () => (/* reexport safe */ _defaults_js__WEBPACK_IMPORTED_MODULE_9__.setDefaults),\n/* harmony export */   setI18n: () => (/* reexport safe */ _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__.setI18n),\n/* harmony export */   time: () => (/* binding */ time),\n/* harmony export */   useSSR: () => (/* reexport safe */ _useSSR_js__WEBPACK_IMPORTED_MODULE_7__.useSSR),\n/* harmony export */   useTranslation: () => (/* reexport safe */ _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__.useTranslation),\n/* harmony export */   withSSR: () => (/* reexport safe */ _withSSR_js__WEBPACK_IMPORTED_MODULE_6__.withSSR),\n/* harmony export */   withTranslation: () => (/* reexport safe */ _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__.withTranslation)\n/* harmony export */ });\n/* harmony import */ var _Trans_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Trans.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/Trans.js\");\n/* harmony import */ var _TransWithoutContext_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./TransWithoutContext.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/TransWithoutContext.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./useTranslation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _withTranslation_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./withTranslation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/withTranslation.js\");\n/* harmony import */ var _Translation_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Translation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/Translation.js\");\n/* harmony import */ var _I18nextProvider_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./I18nextProvider.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/I18nextProvider.js\");\n/* harmony import */ var _withSSR_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./withSSR.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/withSSR.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./useSSR.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _initReactI18next_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./initReactI18next.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/initReactI18next.js\");\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./defaults.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./i18nInstance.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n\n\n\n\n\n\n\n\n\n\n\n\nconst date = () => '';\nconst time = () => '';\nconst number = () => '';\nconst select = () => '';\nconst plural = () => '';\nconst selectOrdinal = () => '';//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFtQztBQUNxQztBQUNuQjtBQUNFO0FBQ1I7QUFDUTtBQUNoQjtBQUNGO0FBQ29CO0FBQ0E7QUFDSjtBQUM0QjtBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IHsgVHJhbnMgfSBmcm9tICcuL1RyYW5zLmpzJztcbmV4cG9ydCB7IFRyYW5zIGFzIFRyYW5zV2l0aG91dENvbnRleHQgfSBmcm9tICcuL1RyYW5zV2l0aG91dENvbnRleHQuanMnO1xuZXhwb3J0IHsgdXNlVHJhbnNsYXRpb24gfSBmcm9tICcuL3VzZVRyYW5zbGF0aW9uLmpzJztcbmV4cG9ydCB7IHdpdGhUcmFuc2xhdGlvbiB9IGZyb20gJy4vd2l0aFRyYW5zbGF0aW9uLmpzJztcbmV4cG9ydCB7IFRyYW5zbGF0aW9uIH0gZnJvbSAnLi9UcmFuc2xhdGlvbi5qcyc7XG5leHBvcnQgeyBJMThuZXh0UHJvdmlkZXIgfSBmcm9tICcuL0kxOG5leHRQcm92aWRlci5qcyc7XG5leHBvcnQgeyB3aXRoU1NSIH0gZnJvbSAnLi93aXRoU1NSLmpzJztcbmV4cG9ydCB7IHVzZVNTUiB9IGZyb20gJy4vdXNlU1NSLmpzJztcbmV4cG9ydCB7IGluaXRSZWFjdEkxOG5leHQgfSBmcm9tICcuL2luaXRSZWFjdEkxOG5leHQuanMnO1xuZXhwb3J0IHsgc2V0RGVmYXVsdHMsIGdldERlZmF1bHRzIH0gZnJvbSAnLi9kZWZhdWx0cy5qcyc7XG5leHBvcnQgeyBzZXRJMThuLCBnZXRJMThuIH0gZnJvbSAnLi9pMThuSW5zdGFuY2UuanMnO1xuZXhwb3J0IHsgSTE4bkNvbnRleHQsIGNvbXBvc2VJbml0aWFsUHJvcHMsIGdldEluaXRpYWxQcm9wcyB9IGZyb20gJy4vY29udGV4dC5qcyc7XG5leHBvcnQgY29uc3QgZGF0ZSA9ICgpID0+ICcnO1xuZXhwb3J0IGNvbnN0IHRpbWUgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBudW1iZXIgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBzZWxlY3QgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBwbHVyYWwgPSAoKSA9PiAnJztcbmV4cG9ydCBjb25zdCBzZWxlY3RPcmRpbmFsID0gKCkgPT4gJyc7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/initReactI18next.js":
/*!****************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/initReactI18next.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initReactI18next: () => (/* binding */ initReactI18next)\n/* harmony export */ });\n/* harmony import */ var _defaults_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./defaults.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/defaults.js\");\n/* harmony import */ var _i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./i18nInstance.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/i18nInstance.js\");\n\n\nconst initReactI18next = {\n  type: '3rdParty',\n  init(instance) {\n    (0,_defaults_js__WEBPACK_IMPORTED_MODULE_0__.setDefaults)(instance.options.react);\n    (0,_i18nInstance_js__WEBPACK_IMPORTED_MODULE_1__.setI18n)(instance);\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvaW5pdFJlYWN0STE4bmV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFDQTtBQUNyQztBQUNQO0FBQ0E7QUFDQSxJQUFJLHlEQUFXO0FBQ2YsSUFBSSx5REFBTztBQUNYO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pMThuZXh0XFxkaXN0XFxlc1xcaW5pdFJlYWN0STE4bmV4dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBzZXREZWZhdWx0cyB9IGZyb20gJy4vZGVmYXVsdHMuanMnO1xuaW1wb3J0IHsgc2V0STE4biB9IGZyb20gJy4vaTE4bkluc3RhbmNlLmpzJztcbmV4cG9ydCBjb25zdCBpbml0UmVhY3RJMThuZXh0ID0ge1xuICB0eXBlOiAnM3JkUGFydHknLFxuICBpbml0KGluc3RhbmNlKSB7XG4gICAgc2V0RGVmYXVsdHMoaW5zdGFuY2Uub3B0aW9ucy5yZWFjdCk7XG4gICAgc2V0STE4bihpbnN0YW5jZSk7XG4gIH1cbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/initReactI18next.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/unescape.js":
/*!********************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/unescape.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   unescape: () => (/* binding */ unescape)\n/* harmony export */ });\nconst matchHtmlEntity = /&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g;\nconst htmlEntities = {\n  '&amp;': '&',\n  '&#38;': '&',\n  '&lt;': '<',\n  '&#60;': '<',\n  '&gt;': '>',\n  '&#62;': '>',\n  '&apos;': \"'\",\n  '&#39;': \"'\",\n  '&quot;': '\"',\n  '&#34;': '\"',\n  '&nbsp;': ' ',\n  '&#160;': ' ',\n  '&copy;': '©',\n  '&#169;': '©',\n  '&reg;': '®',\n  '&#174;': '®',\n  '&hellip;': '…',\n  '&#8230;': '…',\n  '&#x2F;': '/',\n  '&#47;': '/'\n};\nconst unescapeHtmlEntity = m => htmlEntities[m];\nconst unescape = text => text.replace(matchHtmlEntity, unescapeHtmlEntity);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvdW5lc2NhcGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLHlIQUF5SDtBQUN6SDtBQUNBLFFBQVE7QUFDUixRQUFRO0FBQ1IsT0FBTztBQUNQLFFBQVE7QUFDUixPQUFPO0FBQ1AsUUFBUTtBQUNSLFNBQVM7QUFDVCxRQUFRO0FBQ1IsU0FBUztBQUNULFFBQVE7QUFDUixTQUFTO0FBQ1QsU0FBUztBQUNULFNBQVM7QUFDVCxTQUFTO0FBQ1QsUUFBUTtBQUNSLFNBQVM7QUFDVCxXQUFXO0FBQ1gsVUFBVTtBQUNWLFNBQVM7QUFDVCxRQUFRO0FBQ1I7QUFDQTtBQUNPIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXHVuZXNjYXBlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1hdGNoSHRtbEVudGl0eSA9IC8mKD86YW1wfCMzOHxsdHwjNjB8Z3R8IzYyfGFwb3N8IzM5fHF1b3R8IzM0fG5ic3B8IzE2MHxjb3B5fCMxNjl8cmVnfCMxNzR8aGVsbGlwfCM4MjMwfCN4MkZ8IzQ3KTsvZztcbmNvbnN0IGh0bWxFbnRpdGllcyA9IHtcbiAgJyZhbXA7JzogJyYnLFxuICAnJiMzODsnOiAnJicsXG4gICcmbHQ7JzogJzwnLFxuICAnJiM2MDsnOiAnPCcsXG4gICcmZ3Q7JzogJz4nLFxuICAnJiM2MjsnOiAnPicsXG4gICcmYXBvczsnOiBcIidcIixcbiAgJyYjMzk7JzogXCInXCIsXG4gICcmcXVvdDsnOiAnXCInLFxuICAnJiMzNDsnOiAnXCInLFxuICAnJm5ic3A7JzogJyAnLFxuICAnJiMxNjA7JzogJyAnLFxuICAnJmNvcHk7JzogJ8KpJyxcbiAgJyYjMTY5Oyc6ICfCqScsXG4gICcmcmVnOyc6ICfCricsXG4gICcmIzE3NDsnOiAnwq4nLFxuICAnJmhlbGxpcDsnOiAn4oCmJyxcbiAgJyYjODIzMDsnOiAn4oCmJyxcbiAgJyYjeDJGOyc6ICcvJyxcbiAgJyYjNDc7JzogJy8nXG59O1xuY29uc3QgdW5lc2NhcGVIdG1sRW50aXR5ID0gbSA9PiBodG1sRW50aXRpZXNbbV07XG5leHBvcnQgY29uc3QgdW5lc2NhcGUgPSB0ZXh0ID0+IHRleHQucmVwbGFjZShtYXRjaEh0bWxFbnRpdHksIHVuZXNjYXBlSHRtbEVudGl0eSk7Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/unescape.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/useSSR.js":
/*!******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useSSR.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSSR: () => (/* binding */ useSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n\n\nconst useSSR = (initialI18nStore, initialLanguage, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n.options?.isClone) return;\n  if (initialI18nStore && !i18n.initializedStoreOnce) {\n    i18n.services.resourceStore.data = initialI18nStore;\n    i18n.options.ns = Object.values(initialI18nStore).reduce((mem, lngResources) => {\n      Object.keys(lngResources).forEach(ns => {\n        if (mem.indexOf(ns) < 0) mem.push(ns);\n      });\n      return mem;\n    }, i18n.options.ns);\n    i18n.initializedStoreOnce = true;\n    i18n.isInitialized = true;\n  }\n  if (initialLanguage && !i18n.initializedLanguageOnce) {\n    i18n.changeLanguage(initialLanguage);\n    i18n.initializedLanguageOnce = true;\n  }\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvdXNlU1NSLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFtQztBQUNpQjtBQUM3Qyw2REFBNkQ7QUFDcEU7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0EsSUFBSSxFQUFFLGlEQUFVLENBQUMsb0RBQVc7QUFDNUIsbURBQW1ELG9EQUFPO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xccmVhY3QtaTE4bmV4dFxcZGlzdFxcZXNcXHVzZVNTUi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0STE4biwgSTE4bkNvbnRleHQgfSBmcm9tICcuL2NvbnRleHQuanMnO1xuZXhwb3J0IGNvbnN0IHVzZVNTUiA9IChpbml0aWFsSTE4blN0b3JlLCBpbml0aWFsTGFuZ3VhZ2UsIHByb3BzID0ge30pID0+IHtcbiAgY29uc3Qge1xuICAgIGkxOG46IGkxOG5Gcm9tUHJvcHNcbiAgfSA9IHByb3BzO1xuICBjb25zdCB7XG4gICAgaTE4bjogaTE4bkZyb21Db250ZXh0XG4gIH0gPSB1c2VDb250ZXh0KEkxOG5Db250ZXh0KSB8fCB7fTtcbiAgY29uc3QgaTE4biA9IGkxOG5Gcm9tUHJvcHMgfHwgaTE4bkZyb21Db250ZXh0IHx8IGdldEkxOG4oKTtcbiAgaWYgKGkxOG4ub3B0aW9ucz8uaXNDbG9uZSkgcmV0dXJuO1xuICBpZiAoaW5pdGlhbEkxOG5TdG9yZSAmJiAhaTE4bi5pbml0aWFsaXplZFN0b3JlT25jZSkge1xuICAgIGkxOG4uc2VydmljZXMucmVzb3VyY2VTdG9yZS5kYXRhID0gaW5pdGlhbEkxOG5TdG9yZTtcbiAgICBpMThuLm9wdGlvbnMubnMgPSBPYmplY3QudmFsdWVzKGluaXRpYWxJMThuU3RvcmUpLnJlZHVjZSgobWVtLCBsbmdSZXNvdXJjZXMpID0+IHtcbiAgICAgIE9iamVjdC5rZXlzKGxuZ1Jlc291cmNlcykuZm9yRWFjaChucyA9PiB7XG4gICAgICAgIGlmIChtZW0uaW5kZXhPZihucykgPCAwKSBtZW0ucHVzaChucyk7XG4gICAgICB9KTtcbiAgICAgIHJldHVybiBtZW07XG4gICAgfSwgaTE4bi5vcHRpb25zLm5zKTtcbiAgICBpMThuLmluaXRpYWxpemVkU3RvcmVPbmNlID0gdHJ1ZTtcbiAgICBpMThuLmlzSW5pdGlhbGl6ZWQgPSB0cnVlO1xuICB9XG4gIGlmIChpbml0aWFsTGFuZ3VhZ2UgJiYgIWkxOG4uaW5pdGlhbGl6ZWRMYW5ndWFnZU9uY2UpIHtcbiAgICBpMThuLmNoYW5nZUxhbmd1YWdlKGluaXRpYWxMYW5ndWFnZSk7XG4gICAgaTE4bi5pbml0aWFsaXplZExhbmd1YWdlT25jZSA9IHRydWU7XG4gIH1cbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/useSSR.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js":
/*!**************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/useTranslation.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslation: () => (/* binding */ useTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst usePrevious = (value, ignore) => {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nconst alwaysNewT = (i18n, language, namespace, keyPrefix) => i18n.getFixedT(language, namespace, keyPrefix);\nconst useMemoizedT = (i18n, language, namespace, keyPrefix) => (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(alwaysNewT(i18n, language, namespace, keyPrefix), [i18n, language, namespace, keyPrefix]);\nconst useTranslation = (ns, props = {}) => {\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_js__WEBPACK_IMPORTED_MODULE_1__.I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || (0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getI18n)();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new _context_js__WEBPACK_IMPORTED_MODULE_1__.ReportNamespaces();\n  if (!i18n) {\n    (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'NO_I18NEXT_INSTANCE', 'useTranslation: You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(optsOrDefaultValue)) return optsOrDefaultValue;\n      if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isObject)(optsOrDefaultValue) && (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(optsOrDefaultValue.defaultValue)) return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react?.wait) (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.warnOnce)(i18n, 'DEPRECATED_OPTION', 'useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...(0,_context_js__WEBPACK_IMPORTED_MODULE_1__.getDefaults)(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options?.defaultNS;\n  namespaces = (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.isString)(namespaces) ? [namespaces] : namespaces || ['translation'];\n  i18n.reportNamespaces.addUsedNamespaces?.(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.hasLoadedNamespace)(n, i18n, i18nOptions));\n  const memoGetT = useMemoizedT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const getT = () => memoGetT;\n  const getNewT = () => alwaysNewT(i18n, props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  const [t, setT] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      } else {\n        (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => {\n          if (isMounted.current) setT(getNewT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getNewT);\n    }\n    const boundReset = () => {\n      if (isMounted.current) setT(getNewT);\n    };\n    if (bindI18n) i18n?.on(bindI18n, boundReset);\n    if (bindI18nStore) i18n?.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (i18n) bindI18n?.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isMounted.current && ready) {\n      setT(getT);\n    }\n  }, [i18n, keyPrefix, ready]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadLanguages)(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      (0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.loadNamespaces)(i18n, namespaces, () => resolve());\n    }\n  });\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/utils.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDisplayName: () => (/* binding */ getDisplayName),\n/* harmony export */   hasLoadedNamespace: () => (/* binding */ hasLoadedNamespace),\n/* harmony export */   isObject: () => (/* binding */ isObject),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   loadLanguages: () => (/* binding */ loadLanguages),\n/* harmony export */   loadNamespaces: () => (/* binding */ loadNamespaces),\n/* harmony export */   warn: () => (/* binding */ warn),\n/* harmony export */   warnOnce: () => (/* binding */ warnOnce)\n/* harmony export */ });\nconst warn = (i18n, code, msg, rest) => {\n  const args = [msg, {\n    code,\n    ...(rest || {})\n  }];\n  if (i18n?.services?.logger?.forward) {\n    return i18n.services.logger.forward(args, 'warn', 'react-i18next::', true);\n  }\n  if (isString(args[0])) args[0] = `react-i18next:: ${args[0]}`;\n  if (i18n?.services?.logger?.warn) {\n    i18n.services.logger.warn(...args);\n  } else if (console?.warn) {\n    console.warn(...args);\n  }\n};\nconst alreadyWarned = {};\nconst warnOnce = (i18n, code, msg, rest) => {\n  if (isString(msg) && alreadyWarned[msg]) return;\n  if (isString(msg)) alreadyWarned[msg] = new Date();\n  warn(i18n, code, msg, rest);\n};\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nconst loadNamespaces = (i18n, ns, cb) => {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n};\nconst loadLanguages = (i18n, lng, ns, cb) => {\n  if (isString(ns)) ns = [ns];\n  if (i18n.options.preload && i18n.options.preload.indexOf(lng) > -1) return loadNamespaces(i18n, ns, cb);\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n};\nconst hasLoadedNamespace = (ns, i18n, options = {}) => {\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce(i18n, 'NO_LANGUAGES', 'i18n.languages were undefined or empty', {\n      languages: i18n.languages\n    });\n    return true;\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n?.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n};\nconst getDisplayName = Component => Component.displayName || Component.name || (isString(Component) && Component.length > 0 ? Component : 'Unknown');\nconst isString = obj => typeof obj === 'string';\nconst isObject = obj => typeof obj === 'object' && obj !== null;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/withSSR.js":
/*!*******************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withSSR.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withSSR: () => (/* binding */ withSSR)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _useSSR_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useSSR.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useSSR.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/context.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\n\nconst withSSR = () => function Extend(WrappedComponent) {\n  function I18nextWithSSR({\n    initialI18nStore,\n    initialLanguage,\n    ...rest\n  }) {\n    (0,_useSSR_js__WEBPACK_IMPORTED_MODULE_1__.useSSR)(initialI18nStore, initialLanguage);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, {\n      ...rest\n    });\n  }\n  I18nextWithSSR.getInitialProps = (0,_context_js__WEBPACK_IMPORTED_MODULE_2__.composeInitialProps)(WrappedComponent);\n  I18nextWithSSR.displayName = `withI18nextSSR(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.getDisplayName)(WrappedComponent)})`;\n  I18nextWithSSR.WrappedComponent = WrappedComponent;\n  return I18nextWithSSR;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1pMThuZXh0L2Rpc3QvZXMvd2l0aFNTUi5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFzQztBQUNEO0FBQ2M7QUFDUDtBQUNyQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILElBQUksa0RBQU07QUFDVixXQUFXLG9EQUFhO0FBQ3hCO0FBQ0EsS0FBSztBQUNMO0FBQ0EsbUNBQW1DLGdFQUFtQjtBQUN0RCxpREFBaUQseURBQWMsbUJBQW1CO0FBQ2xGO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHJlYWN0LWkxOG5leHRcXGRpc3RcXGVzXFx3aXRoU1NSLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUVsZW1lbnQgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VTU1IgfSBmcm9tICcuL3VzZVNTUi5qcyc7XG5pbXBvcnQgeyBjb21wb3NlSW5pdGlhbFByb3BzIH0gZnJvbSAnLi9jb250ZXh0LmpzJztcbmltcG9ydCB7IGdldERpc3BsYXlOYW1lIH0gZnJvbSAnLi91dGlscy5qcyc7XG5leHBvcnQgY29uc3Qgd2l0aFNTUiA9ICgpID0+IGZ1bmN0aW9uIEV4dGVuZChXcmFwcGVkQ29tcG9uZW50KSB7XG4gIGZ1bmN0aW9uIEkxOG5leHRXaXRoU1NSKHtcbiAgICBpbml0aWFsSTE4blN0b3JlLFxuICAgIGluaXRpYWxMYW5ndWFnZSxcbiAgICAuLi5yZXN0XG4gIH0pIHtcbiAgICB1c2VTU1IoaW5pdGlhbEkxOG5TdG9yZSwgaW5pdGlhbExhbmd1YWdlKTtcbiAgICByZXR1cm4gY3JlYXRlRWxlbWVudChXcmFwcGVkQ29tcG9uZW50LCB7XG4gICAgICAuLi5yZXN0XG4gICAgfSk7XG4gIH1cbiAgSTE4bmV4dFdpdGhTU1IuZ2V0SW5pdGlhbFByb3BzID0gY29tcG9zZUluaXRpYWxQcm9wcyhXcmFwcGVkQ29tcG9uZW50KTtcbiAgSTE4bmV4dFdpdGhTU1IuZGlzcGxheU5hbWUgPSBgd2l0aEkxOG5leHRTU1IoJHtnZXREaXNwbGF5TmFtZShXcmFwcGVkQ29tcG9uZW50KX0pYDtcbiAgSTE4bmV4dFdpdGhTU1IuV3JhcHBlZENvbXBvbmVudCA9IFdyYXBwZWRDb21wb25lbnQ7XG4gIHJldHVybiBJMThuZXh0V2l0aFNTUjtcbn07Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/withSSR.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-i18next/dist/es/withTranslation.js":
/*!***************************************************************!*\
  !*** ./node_modules/react-i18next/dist/es/withTranslation.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withTranslation: () => (/* binding */ withTranslation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _useTranslation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useTranslation.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/useTranslation.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils.js */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/utils.js\");\n\n\n\nconst withTranslation = (ns, options = {}) => function Extend(WrappedComponent) {\n  function I18nextWithTranslation({\n    forwardedRef,\n    ...rest\n  }) {\n    const [t, i18n, ready] = (0,_useTranslation_js__WEBPACK_IMPORTED_MODULE_1__.useTranslation)(ns, {\n      ...rest,\n      keyPrefix: options.keyPrefix\n    });\n    const passDownProps = {\n      ...rest,\n      t,\n      i18n,\n      tReady: ready\n    };\n    if (options.withRef && forwardedRef) {\n      passDownProps.ref = forwardedRef;\n    } else if (!options.withRef && forwardedRef) {\n      passDownProps.forwardedRef = forwardedRef;\n    }\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(WrappedComponent, passDownProps);\n  }\n  I18nextWithTranslation.displayName = `withI18nextTranslation(${(0,_utils_js__WEBPACK_IMPORTED_MODULE_2__.getDisplayName)(WrappedComponent)})`;\n  I18nextWithTranslation.WrappedComponent = WrappedComponent;\n  const forwardRef = (props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(I18nextWithTranslation, Object.assign({}, props, {\n    forwardedRef: ref\n  }));\n  return options.withRef ? (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(forwardRef) : I18nextWithTranslation;\n};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-i18next/dist/es/withTranslation.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/void-elements/index.js":
/*!*********************************************!*\
  !*** ./node_modules/void-elements/index.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/**\n * This file automatically generated from `pre-publish.js`.\n * Do not manually edit.\n */\n\nmodule.exports = {\n  \"area\": true,\n  \"base\": true,\n  \"br\": true,\n  \"col\": true,\n  \"embed\": true,\n  \"hr\": true,\n  \"img\": true,\n  \"input\": true,\n  \"link\": true,\n  \"meta\": true,\n  \"param\": true,\n  \"source\": true,\n  \"track\": true,\n  \"wbr\": true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy92b2lkLWVsZW1lbnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcdm9pZC1lbGVtZW50c1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUaGlzIGZpbGUgYXV0b21hdGljYWxseSBnZW5lcmF0ZWQgZnJvbSBgcHJlLXB1Ymxpc2guanNgLlxuICogRG8gbm90IG1hbnVhbGx5IGVkaXQuXG4gKi9cblxubW9kdWxlLmV4cG9ydHMgPSB7XG4gIFwiYXJlYVwiOiB0cnVlLFxuICBcImJhc2VcIjogdHJ1ZSxcbiAgXCJiclwiOiB0cnVlLFxuICBcImNvbFwiOiB0cnVlLFxuICBcImVtYmVkXCI6IHRydWUsXG4gIFwiaHJcIjogdHJ1ZSxcbiAgXCJpbWdcIjogdHJ1ZSxcbiAgXCJpbnB1dFwiOiB0cnVlLFxuICBcImxpbmtcIjogdHJ1ZSxcbiAgXCJtZXRhXCI6IHRydWUsXG4gIFwicGFyYW1cIjogdHJ1ZSxcbiAgXCJzb3VyY2VcIjogdHJ1ZSxcbiAgXCJ0cmFja1wiOiB0cnVlLFxuICBcIndiclwiOiB0cnVlXG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/void-elements/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MediaListPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MediaListPage() {\n    _s();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast)();\n    const [mediaItems, setMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [codeSearchTerm, setCodeSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScrollToTop, setShowScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            fetchMediaItems();\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    // مراقبة التمرير لإظهار/إخفاء زر العودة لأعلى\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"MediaListPage.useEffect.handleScroll\": ()=>{\n                    setShowScrollToTop(window.scrollY > 300);\n                }\n            }[\"MediaListPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"MediaListPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"MediaListPage.useEffect\"];\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            filterAndSortItems();\n        }\n    }[\"MediaListPage.useEffect\"], [\n        mediaItems,\n        searchTerm,\n        codeSearchTerm,\n        selectedType,\n        selectedStatus,\n        sortBy\n    ]);\n    // دالة العودة لأعلى الصفحة\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    const fetchMediaItems = async ()=>{\n        try {\n            const response = await fetch('/api/media');\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(result.data);\n            } else {\n                setError(result.error);\n            }\n        } catch (error) {\n            console.error('Error fetching media items:', error);\n            setError(t('messages.networkError'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterAndSortItems = ()=>{\n        let filtered = [\n            ...mediaItems\n        ];\n        // البحث بالاسم\n        if (searchTerm) {\n            filtered = filtered.filter((item)=>item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // البحث بالكود (في السيجمانت)\n        if (codeSearchTerm) {\n            filtered = filtered.filter((item)=>{\n                // البحث في معرف المادة\n                if (item.id.toLowerCase().includes(codeSearchTerm.toLowerCase())) {\n                    return true;\n                }\n                // البحث في أكواد السيجمانت\n                if (item.segments && item.segments.length > 0) {\n                    return item.segments.some((segment)=>segment.code && segment.code.toLowerCase().includes(codeSearchTerm.toLowerCase()));\n                }\n                return false;\n            });\n        }\n        // فلترة بالنوع\n        if (selectedType !== 'ALL') {\n            filtered = filtered.filter((item)=>item.type === selectedType);\n        }\n        // فلترة بالحالة\n        if (selectedStatus !== 'ALL') {\n            filtered = filtered.filter((item)=>item.status === selectedStatus);\n        }\n        // الترتيب\n        switch(sortBy){\n            case 'newest':\n                filtered.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                break;\n            case 'oldest':\n                filtered.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n                break;\n            case 'name':\n                filtered.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n                break;\n            case 'type':\n                filtered.sort((a, b)=>a.type.localeCompare(b.type));\n                break;\n        }\n        setFilteredItems(filtered);\n    };\n    const deleteMediaItem = async (id)=>{\n        if (!confirm(t('messages.confirmDelete'))) return;\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = \"token_\".concat(user.id || 'unknown', \"_\").concat(user.role || 'unknown');\n            console.log('Sending delete request with token:', tokenWithRole);\n            const response = await fetch(\"/api/media?id=\".concat(id), {\n                method: 'DELETE',\n                headers: {\n                    'Authorization': \"Bearer \".concat(tokenWithRole)\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(mediaItems.filter((item)=>item.id !== id));\n                showSuccessToast('mediaDeleted');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error deleting media item:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const exportToExcel = async ()=>{\n        setIsExporting(true);\n        try {\n            console.log('🚀 بدء تصدير قاعدة البيانات...');\n            // إرسال الفلاتر الحالية مع طلب التصدير\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (codeSearchTerm) params.append('codeSearch', codeSearchTerm);\n            if (selectedType !== 'ALL') params.append('type', selectedType);\n            if (selectedStatus !== 'ALL') params.append('status', selectedStatus);\n            const apiUrl = \"/api/export-unified\".concat(params.toString() ? '?' + params.toString() : '');\n            console.log('📊 تصدير مع الفلاتر:', apiUrl);\n            const response = await fetch(apiUrl);\n            if (!response.ok) {\n                throw new Error(t('messages.exportError'));\n            }\n            // الحصول على الملف كـ blob\n            const blob = await response.blob();\n            // إنشاء رابط التحميل\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = downloadUrl;\n            // تحديد اسم الملف\n            const fileName = \"Media_Database_\".concat(new Date().toISOString().split('T')[0], \".xlsx\");\n            link.download = fileName;\n            // تحميل الملف\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            // تنظيف الذاكرة\n            window.URL.revokeObjectURL(downloadUrl);\n            console.log('✅ تم تصدير قاعدة البيانات بنجاح');\n            showSuccessToast('exportSuccess');\n        } catch (error) {\n            console.error('❌ خطأ في التصدير:', error);\n            showErrorToast('exportFailed');\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const getTypeLabel = (type)=>{\n        return t(\"mediaTypes.\".concat(type)) || type;\n    };\n    const getStatusLabel = (status)=>{\n        return t(\"mediaStatus.\".concat(status)) || status;\n    };\n    const getChannelLabel = (channel)=>{\n        return t(\"channels.\".concat(channel)) || channel;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"⏳ \",\n                    t('common.loading')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"❌ \",\n                    t('common.error'),\n                    \": \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'MEDIA_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: t('media.list'),\n            subtitle: t('media.title'),\n            icon: \"\\uD83C\\uDFAC\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                        padding: '20px',\n                        borderRadius: '12px',\n                        marginBottom: '25px',\n                        color: 'white',\n                        textAlign: 'center',\n                        boxShadow: '0 6px 20px rgba(102, 126, 234, 0.3)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: '0 0 10px 0',\n                                fontSize: '1.3rem'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCCA \",\n                                t('media.list')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: '0 0 8px 0',\n                                fontSize: '1rem',\n                                opacity: 0.9\n                            },\n                            children: t('media.mediaOverview')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: '0',\n                                fontSize: '0.9rem',\n                                opacity: 0.8\n                            },\n                            children: [\n                                \"✨ \",\n                                t('media.searchFilterExport')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        gap: '15px',\n                        justifyContent: 'center',\n                        marginBottom: '25px',\n                        flexWrap: 'wrap'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: exportToExcel,\n                        disabled: isExporting,\n                        style: {\n                            background: isExporting ? 'linear-gradient(45deg, #6c757d, #5a6268)' : 'linear-gradient(45deg, #17a2b8, #138496)',\n                            color: 'white',\n                            padding: '12px 25px',\n                            borderRadius: '25px',\n                            border: 'none',\n                            fontWeight: 'bold',\n                            cursor: isExporting ? 'not-allowed' : 'pointer',\n                            boxShadow: '0 4px 15px rgba(23,162,184,0.3)',\n                            fontSize: '1rem'\n                        },\n                        children: isExporting ? '⏳ ' + t('media.exporting') : '📊 ' + t('media.exportExcel')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: '25px',\n                        marginBottom: '25px',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: '#f3f4f6',\n                                marginBottom: '20px',\n                                fontSize: '1.3rem'\n                            },\n                            children: [\n                                \"\\uD83D\\uDD0D \",\n                                t('media.searchAndFilter')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.searchByName'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('media.searchPlaceholder'),\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: 'rtl',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.searchByCode'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('media.codePlaceholder'),\n                                            value: codeSearchTerm,\n                                            onChange: (e)=>setCodeSearchTerm(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: 'rtl',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.mediaType'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedType,\n                                            onChange: (e)=>setSelectedType(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ALL\",\n                                                    children: t('mediaTypes.ALL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"FILM\",\n                                                    children: t('mediaTypes.FILM')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SERIES\",\n                                                    children: t('mediaTypes.SERIES')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"PROGRAM\",\n                                                    children: t('mediaTypes.PROGRAM')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SONG\",\n                                                    children: t('mediaTypes.SONG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"FILLER\",\n                                                    children: t('mediaTypes.FILLER')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"STING\",\n                                                    children: t('mediaTypes.STING')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"PROMO\",\n                                                    children: t('mediaTypes.PROMO')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NEXT\",\n                                                    children: t('mediaTypes.NEXT')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NOW\",\n                                                    children: t('mediaTypes.NOW')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"سنعود\",\n                                                    children: t('mediaTypes.سنعود')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"عدنا\",\n                                                    children: t('mediaTypes.عدنا')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MINI\",\n                                                    children: t('mediaTypes.MINI')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CROSS\",\n                                                    children: t('mediaTypes.CROSS')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.mediaStatus'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedStatus,\n                                            onChange: (e)=>setSelectedStatus(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ALL\",\n                                                    children: t('mediaStatus.ALL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"VALID\",\n                                                    children: t('mediaStatus.VALID')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"REJECTED_CENSORSHIP\",\n                                                    children: t('mediaStatus.REJECTED_CENSORSHIP')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"REJECTED_TECHNICAL\",\n                                                    children: t('mediaStatus.REJECTED_TECHNICAL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"EXPIRED\",\n                                                    children: t('mediaStatus.EXPIRED')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"HOLD\",\n                                                    children: t('mediaStatus.HOLD')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.sortBy'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"newest\",\n                                                    children: t('media.newest')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"oldest\",\n                                                    children: t('media.oldest')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: t('media.byName')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"type\",\n                                                    children: t('media.byType')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '15px',\n                                padding: '10px',\n                                background: '#1f2937',\n                                borderRadius: '8px',\n                                textAlign: 'center',\n                                color: '#d1d5db',\n                                border: '1px solid #6b7280'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCCA \",\n                                t('media.searchStats', {\n                                    filtered: filteredItems.length,\n                                    total: mediaItems.length\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, this),\n                filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: '50px',\n                        textAlign: 'center',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: '#d1d5db',\n                                fontSize: '1.5rem'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCED \",\n                                t('media.noMediaFound')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: '#a0aec0',\n                                marginTop: '10px'\n                            },\n                            children: t('media.startAdding')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 496,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gap: '20px'\n                    },\n                    children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#4a5568',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: '1fr auto',\n                                    gap: '20px',\n                                    alignItems: 'start'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    marginBottom: '15px',\n                                                    fontSize: '1.4rem'\n                                                },\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                    gap: '15px',\n                                                    marginBottom: '15px',\n                                                    color: '#d1d5db'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('common.type'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getTypeLabel(item.type)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('media.channel'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getChannelLabel(item.channel)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('common.status'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getStatusLabel(item.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('media.segmentCount'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            item.segments.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    marginBottom: '10px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t('media.description'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \" \",\n                                                    item.description\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.segments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '15px',\n                                                    color: '#d1d5db'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t('media.segments'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'grid',\n                                                            gap: '8px',\n                                                            marginTop: '8px'\n                                                        },\n                                                        children: item.segments.map((segment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: '#1f2937',\n                                                                    padding: '8px 12px',\n                                                                    borderRadius: '8px',\n                                                                    fontSize: '0.9rem',\n                                                                    color: '#d1d5db',\n                                                                    border: '1px solid #6b7280'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            \"#\",\n                                                                            segment.segmentNumber\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    \" -\",\n                                                                    segment.code && segment.code.trim() !== '' ? \" \".concat(segment.code, \" - \") : \" [\".concat(t('media.noCode'), \"] - \"),\n                                                                    segment.timeIn,\n                                                                    \" → \",\n                                                                    segment.timeOut,\n                                                                    \" (\",\n                                                                    segment.duration,\n                                                                    \")\"\n                                                                ]\n                                                            }, \"\".concat(item.id, \"_segment_\").concat(index), true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            flexDirection: 'column',\n                                            gap: '10px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    // توجيه لصفحة التعديل مع معرف المادة\n                                                    window.location.href = \"/edit-media?id=\".concat(item.id);\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem',\n                                                    marginBottom: '5px'\n                                                },\n                                                children: [\n                                                    \"✏️ \",\n                                                    t('media.edit')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>deleteMediaItem(item.id),\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDDD1️ \",\n                                                    t('media.delete')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 17\n                            }, this)\n                        }, item.id, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 11\n                }, this),\n                showScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: scrollToTop,\n                    style: {\n                        position: 'fixed',\n                        bottom: '30px',\n                        right: '30px',\n                        width: '60px',\n                        height: '60px',\n                        borderRadius: '50%',\n                        background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                        color: 'white',\n                        border: 'none',\n                        cursor: 'pointer',\n                        fontSize: '24px',\n                        boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',\n                        zIndex: 1000,\n                        transition: 'all 0.3s ease',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                    },\n                    onMouseEnter: (e)=>{\n                        e.currentTarget.style.transform = 'scale(1.1)';\n                        e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 123, 255, 0.4)';\n                    },\n                    onMouseLeave: (e)=>{\n                        e.currentTarget.style.transform = 'scale(1)';\n                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 123, 255, 0.3)';\n                    },\n                    title: t('media.scrollToTop'),\n                    children: \"⬆️\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 613,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n_s(MediaListPage, \"w2EGwJS4qHoiqz054AuihRD/zr8=\", false, function() {\n    return [\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast\n    ];\n});\n_c = MediaListPage;\nvar _c;\n$RefreshReg$(_c, \"MediaListPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/media-list/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction AuthGuard(param) {\n    let { children, requiredPermissions = [], requiredRole, fallbackComponent } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    // المدير له صلاحيات كاملة\n    if (user.role === 'ADMIN') {\n      console.log('✅ المستخدم هو مدير النظام - تم منح جميع الصلاحيات');\n      return true;\n    }\n\n    // التحقق من الدور المطلوب\n    if (role && user.role !== role) {\n      console.log(`❌ المستخدم ليس لديه الدور المطلوب: ${role}`);\n      return false;\n    }\n\n    // التحقق من الصلاحيات المطلوبة\n    if (permissions.length > 0) {\n      const userPermissions = getUserPermissions(user.role);\n      console.log('🔍 التحقق من الصلاحيات:', {\n        required: permissions,\n        userHas: userPermissions\n      });\n      \n      const hasAllPermissions = permissions.every(permission => \n        userPermissions.includes(permission) || userPermissions.includes('ALL')\n      );\n      \n      if (!hasAllPermissions) {\n        console.log('❌ المستخدم ليس لديه جميع الصلاحيات المطلوبة');\n      } else {\n        console.log('✅ المستخدم لديه جميع الصلاحيات المطلوبة');\n      }\n      \n      return hasAllPermissions;\n    }\n\n    return true;\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user === null || user === void 0 ? void 0 : user.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user === null || user === void 0 ? void 0 : user.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n_s(AuthGuard, \"fqF8YvhaHbrPIfzSUHTCm0cPUfQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthGuard;\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    if (!user) return false;\n    if (user.role === 'ADMIN') return true;\n\n    const userPermissions = getUserPermissions(user.role);\n    return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN',\n        isMediaManager: (user === null || user === void 0 ? void 0 : user.role) === 'MEDIA_MANAGER',\n        isScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'SCHEDULER',\n        isViewer: (user === null || user === void 0 ? void 0 : user.role) === 'VIEWER',\n        isFullViewer: (user === null || user === void 0 ? void 0 : user.role) === 'FULL_VIEWER',\n        isDataEntry: (user === null || user === void 0 ? void 0 : user.role) === 'DATA_ENTRY',\n        isMapScheduler: (user === null || user === void 0 ? void 0 : user.role) === 'MAP_SCHEDULER',\n        isContentManager: (user === null || user === void 0 ? void 0 : user.role) === 'CONTENT_MANAGER'\n    };\n}\n_s1(useAuth, \"YajQB7LURzRD+QP5gw0+K2TZIWA=\");\nvar _c;\n$RefreshReg$(_c, \"AuthGuard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AuthGuard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DashboardLayout(param) {\n    let { children, title = '', subtitle = '', icon = '📊', requiredPermissions, requiredRole, fullWidth = false } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL, currentLang, changeLanguage, isReady } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const timer = setInterval({\n                \"DashboardLayout.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"DashboardLayout.useEffect.timer\"], 1000);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>clearInterval(timer)\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    const navigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: false,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            active: false,\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.unifiedSystem'),\n            icon: '📤',\n            active: false,\n            path: '/unified-system',\n            adminOnly: true,\n            superAdminOnly: true\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard'\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics'\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.superAdminOnly && ((user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN' || (user === null || user === void 0 ? void 0 : user.username) !== 'admin')) return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: requiredPermissions,\n        requiredRole: requiredRole,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"dashboard-layout\",\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: isRTL ? 'rtl' : 'ltr'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const newLang = currentLang === 'ar' ? 'en' : 'ar';\n                                        changeLanguage(newLang);\n                                    },\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '5px'\n                                    },\n                                    title: currentLang === 'ar' ? 'Switch to English' : 'التبديل للعربية',\n                                    children: [\n                                        \"\\uD83C\\uDF10\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: currentLang === 'ar' ? 'EN' : 'عر'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    title: t('auth.logout'),\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        ...isRTL ? {\n                            marginRight: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-right 0.3s ease'\n                        } : {\n                            marginLeft: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-left 0.3s ease'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: t('common.active')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: currentTime.toLocaleTimeString(isRTL ? 'ar-EG' : 'en-US')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"dashboard-card\",\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '20px',\n                                padding: '25px',\n                                border: '1px solid #4a5568',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                                maxWidth: fullWidth ? 'none' : '1000px',\n                                margin: '0 auto',\n                                width: fullWidth ? '100%' : 'auto'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardLayout, \"SMnzmLfKwm+SOwVIwM9TNlkTppg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = DashboardLayout;\nvar _c;\n$RefreshReg$(_c, \"DashboardLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DashboardLayout.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Logo(param) {\n    let { size = 'medium', className, style } = param;\n    const sizes = {\n        small: {\n            fontSize: '1rem',\n            gap: '3px',\n            xSize: '1.2rem'\n        },\n        medium: {\n            fontSize: '1.2rem',\n            gap: '5px',\n            xSize: '1.5rem'\n        },\n        large: {\n            fontSize: '2rem',\n            gap: '8px',\n            xSize: '2.5rem'\n        }\n    };\n    const currentSize = sizes[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        style: {\n            display: 'flex',\n            alignItems: 'center',\n            fontSize: currentSize.fontSize,\n            fontWeight: '900',\n            fontFamily: 'Arial, sans-serif',\n            gap: currentSize.gap,\n            direction: 'ltr',\n            ...style\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '800',\n                    letterSpacing: '1px'\n                },\n                children: \"Prime\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: '#6c757d',\n                    fontSize: '0.8em',\n                    fontWeight: '300'\n                },\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '900',\n                    fontSize: currentSize.xSize\n                },\n                children: \"X\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c = Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Logo.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Sidebar(param) {\n    let { isOpen, onToggle } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const menuItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.importSchedule'),\n            icon: '📤',\n            path: '/daily-schedule/import',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            path: '/statistics',\n            permission: null\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    ...isRTL ? {\n                        right: isOpen ? 0 : '-280px',\n                        borderLeft: '1px solid #2d3748'\n                    } : {\n                        left: isOpen ? 0 : '-280px',\n                        borderRight: '1px solid #2d3748'\n                    },\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    transition: \"\".concat(isRTL ? 'right' : 'left', \" 0.3s ease\"),\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: \"small\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                margin: 0,\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: t('dashboard.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#2d3748' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    borderTop: 'none',\n                                    borderBottom: 'none',\n                                    ...isRTL ? {\n                                        borderLeft: 'none',\n                                        borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    } : {\n                                        borderRight: 'none',\n                                        borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    },\n                                    padding: isRTL ? '12px 20px 12px 8px' : '12px 8px 12px 20px',\n                                    textAlign: isRTL ? 'right' : 'left',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    transition: 'all 0.2s ease',\n                                    direction: isRTL ? 'rtl' : 'ltr'\n                                },\n                                children: item.name\n                            }, index, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('user');\n                                localStorage.removeItem('token');\n                                router.push('/login');\n                            },\n                            style: {\n                                width: '100%',\n                                background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '10px',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '8px',\n                                fontSize: '0.9rem',\n                                fontWeight: 'bold',\n                                marginBottom: '15px'\n                            },\n                            children: [\n                                \"\\uD83D\\uDEAA \",\n                                t('navigation.logout')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"VJIKuK+HsyvOcEJPeGwsV88vEIU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nfunction Toast(param) {\n    let { message, type, duration = 3000, onClose } = param;\n    _s();\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن\n                }\n            }[\"Toast.useEffect.timer\"], duration);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        duration,\n        onClose\n    ]);\n    const getToastStyles = ()=>{\n        const baseStyles = {\n            position: 'relative',\n            padding: '15px 20px',\n            borderRadius: '10px',\n            color: 'white',\n            fontWeight: 'bold',\n            fontSize: '1rem',\n            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n            transform: isVisible ? 'translateX(0)' : 'translateX(100%)',\n            transition: 'transform 0.3s ease, opacity 0.3s ease',\n            opacity: isVisible ? 1 : 0,\n            minWidth: '300px',\n            maxWidth: '500px',\n            direction: 'rtl',\n            fontFamily: 'Cairo, Arial, sans-serif'\n        };\n        const typeStyles = {\n            success: {\n                background: 'linear-gradient(45deg, #28a745, #20c997)'\n            },\n            error: {\n                background: 'linear-gradient(45deg, #dc3545, #c82333)'\n            },\n            warning: {\n                background: 'linear-gradient(45deg, #ffc107, #e0a800)'\n            },\n            info: {\n                background: 'linear-gradient(45deg, #007bff, #0056b3)'\n            }\n        };\n        return {\n            ...baseStyles,\n            ...typeStyles[type]\n        };\n    };\n    const getIcon = ()=>{\n        const icons = {\n            success: '✅',\n            error: '❌',\n            warning: '⚠️',\n            info: 'ℹ️'\n        };\n        return icons[type];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: getToastStyles(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '10px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        fontSize: '1.2rem'\n                    },\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setIsVisible(false);\n                        setTimeout(onClose, 300);\n                    },\n                    style: {\n                        background: 'rgba(255,255,255,0.2)',\n                        border: 'none',\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '25px',\n                        height: '25px',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        marginLeft: 'auto'\n                    },\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(Toast, \"m22S9IQwDfEe/fCJY7LYj8YPDMo=\");\n_c = Toast;\n// Hook لاستخدام Toast\nfunction useToast() {\n    _s1();\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = function(message) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        // التحقق من عدم وجود رسالة مشابهة\n        const existingToast = toasts.find((toast)=>toast.message === message && toast.type === type);\n        if (existingToast) {\n            return; // لا تضيف رسالة مكررة\n        }\n        // إنشاء ID فريد باستخدام timestamp + random number\n        const id = \"toast_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        // حد أقصى 5 رسائل في نفس الوقت\n        setToasts((prev)=>prev.slice(-4)); // احتفظ بآخر 4 + الجديدة = 5\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const ToastContainer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: 'fixed',\n                top: '20px',\n                right: '20px',\n                zIndex: 1000,\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '10px'\n            },\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                    message: toast.message,\n                    type: toast.type,\n                    onClose: ()=>removeToast(toast.id)\n                }, toast.id, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 129,\n            columnNumber: 5\n        }, this);\n    return {\n        showToast,\n        ToastContainer\n    };\n}\n_s1(useToast, \"nD8TBOiFYf9ajstmZpZK2DP4rNo=\");\nvar _c;\n$RefreshReg$(_c, \"Toast\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Toast.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useAppTranslation.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAppTranslation.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppTranslation: () => (/* binding */ useAppTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// نظام ترجمة احترافي مثل التطبيقات الكبيرة\nconst useAppTranslation = ()=>{\n    const { i18n, t: i18nT } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)('common');\n    const [currentLang, setCurrentLang] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ar');\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تهيئة اللغة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAppTranslation.useEffect\": ()=>{\n            const initLanguage = {\n                \"useAppTranslation.useEffect.initLanguage\": ()=>{\n                    try {\n                        // جلب اللغة المحفوظة أو استخدام العربية كافتراضي\n                        const savedLang = localStorage.getItem('language') || 'ar';\n                        const validLang = savedLang === 'en' || savedLang === 'ar' ? savedLang : 'ar';\n                        setCurrentLang(validLang);\n                        i18n.changeLanguage(validLang);\n                        setIsReady(true);\n                        console.log('🌐 Language initialized:', validLang);\n                    } catch (error) {\n                        console.error('❌ Language initialization error:', error);\n                        setCurrentLang('ar');\n                        setIsReady(true);\n                    }\n                }\n            }[\"useAppTranslation.useEffect.initLanguage\"];\n            initLanguage();\n        }\n    }[\"useAppTranslation.useEffect\"], [\n        i18n\n    ]);\n    // مراقبة تغيير اللغة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAppTranslation.useEffect\": ()=>{\n            const handleLanguageChange = {\n                \"useAppTranslation.useEffect.handleLanguageChange\": (lng)=>{\n                    const validLang = lng === 'en' || lng === 'ar' ? lng : 'ar';\n                    setCurrentLang(validLang);\n                    localStorage.setItem('language', validLang);\n                    console.log('🔄 Language changed to:', validLang);\n                }\n            }[\"useAppTranslation.useEffect.handleLanguageChange\"];\n            i18n.on('languageChanged', handleLanguageChange);\n            return ({\n                \"useAppTranslation.useEffect\": ()=>i18n.off('languageChanged', handleLanguageChange)\n            })[\"useAppTranslation.useEffect\"];\n        }\n    }[\"useAppTranslation.useEffect\"], [\n        i18n\n    ]);\n    // دالة ترجمة آمنة ومضمونة مع interpolation\n    const t = (key, options, fallback)=>{\n        try {\n            if (!isReady) return fallback || key;\n            let translation = i18nT(key, options);\n            // إذا كانت الترجمة مفقودة، استخدم الاحتياطي أو المفتاح\n            if (translation === key && fallback) {\n                translation = fallback;\n            }\n            // معالجة interpolation يدوياً إذا لم يعمل i18next\n            if (options && translation && typeof translation === 'string') {\n                Object.keys(options).forEach((optionKey)=>{\n                    const placeholder = \"{{\".concat(optionKey, \"}}\");\n                    if (translation.includes(placeholder)) {\n                        translation = translation.replace(new RegExp(placeholder, 'g'), String(options[optionKey]));\n                    }\n                });\n            }\n            return translation || fallback || key;\n        } catch (error) {\n            console.error('❌ Translation error for key:', key, error);\n            return fallback || key;\n        }\n    };\n    // دالة تغيير اللغة\n    const changeLanguage = (newLang)=>{\n        try {\n            i18n.changeLanguage(newLang);\n        } catch (error) {\n            console.error('❌ Language change error:', error);\n        }\n    };\n    // دالة ترجمة أنواع المواد (محفوظة كما هي)\n    const tMediaType = (type)=>{\n        var _mediaTypeMap_type;\n        const mediaTypeMap = {\n            'FILM': {\n                ar: 'Film',\n                en: 'Film'\n            },\n            'SERIES': {\n                ar: 'Series',\n                en: 'Series'\n            },\n            'PROGRAM': {\n                ar: 'Program',\n                en: 'Program'\n            },\n            'SONG': {\n                ar: 'Song',\n                en: 'Song'\n            },\n            'FILLER': {\n                ar: 'Filler',\n                en: 'Filler'\n            },\n            'STING': {\n                ar: 'Sting',\n                en: 'Sting'\n            },\n            'PROMO': {\n                ar: 'Promo',\n                en: 'Promo'\n            },\n            'NEXT': {\n                ar: 'Next',\n                en: 'Next'\n            },\n            'NOW': {\n                ar: 'Now',\n                en: 'Now'\n            },\n            'سنعود': {\n                ar: 'سنعود',\n                en: 'سنعود'\n            },\n            'عدنا': {\n                ar: 'عدنا',\n                en: 'عدنا'\n            },\n            'MINI': {\n                ar: 'Mini',\n                en: 'Mini'\n            },\n            'CROSS': {\n                ar: 'Cross',\n                en: 'Cross'\n            },\n            'ALL': {\n                ar: 'جميع الأنواع',\n                en: 'All Types'\n            }\n        };\n        return ((_mediaTypeMap_type = mediaTypeMap[type]) === null || _mediaTypeMap_type === void 0 ? void 0 : _mediaTypeMap_type[currentLang]) || type;\n    };\n    return {\n        t,\n        tMediaType,\n        currentLang,\n        isRTL: currentLang === 'ar',\n        isReady,\n        changeLanguage,\n        i18n\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VBcHBUcmFuc2xhdGlvbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStDO0FBQ0g7QUFFNUMsMkNBQTJDO0FBQ3BDLE1BQU1HLG9CQUFvQjtJQUMvQixNQUFNLEVBQUVDLElBQUksRUFBRUMsR0FBR0MsS0FBSyxFQUFFLEdBQUdOLDZEQUFjQSxDQUFDO0lBQzFDLE1BQU0sQ0FBQ08sYUFBYUMsZUFBZSxHQUFHUCwrQ0FBUUEsQ0FBYztJQUM1RCxNQUFNLENBQUNRLFNBQVNDLFdBQVcsR0FBR1QsK0NBQVFBLENBQUM7SUFFdkMsOEJBQThCO0lBQzlCQyxnREFBU0E7dUNBQUM7WUFDUixNQUFNUzs0REFBZTtvQkFDbkIsSUFBSTt3QkFDRixpREFBaUQ7d0JBQ2pELE1BQU1DLFlBQVlDLGFBQWFDLE9BQU8sQ0FBQyxlQUFlO3dCQUN0RCxNQUFNQyxZQUFZLGNBQWUsUUFBUUgsY0FBYyxPQUFRQSxZQUFZO3dCQUUzRUosZUFBZU87d0JBQ2ZYLEtBQUtZLGNBQWMsQ0FBQ0Q7d0JBQ3BCTCxXQUFXO3dCQUVYTyxRQUFRQyxHQUFHLENBQUMsNEJBQTRCSDtvQkFDMUMsRUFBRSxPQUFPSSxPQUFPO3dCQUNkRixRQUFRRSxLQUFLLENBQUMsb0NBQW9DQTt3QkFDbERYLGVBQWU7d0JBQ2ZFLFdBQVc7b0JBQ2I7Z0JBQ0Y7O1lBRUFDO1FBQ0Y7c0NBQUc7UUFBQ1A7S0FBSztJQUVULHFCQUFxQjtJQUNyQkYsZ0RBQVNBO3VDQUFDO1lBQ1IsTUFBTWtCO29FQUF1QixDQUFDQztvQkFDNUIsTUFBTU4sWUFBWSxRQUFTLFFBQVFNLFFBQVEsT0FBUUEsTUFBcUI7b0JBQ3hFYixlQUFlTztvQkFDZkYsYUFBYVMsT0FBTyxDQUFDLFlBQVlQO29CQUNqQ0UsUUFBUUMsR0FBRyxDQUFDLDJCQUEyQkg7Z0JBQ3pDOztZQUVBWCxLQUFLbUIsRUFBRSxDQUFDLG1CQUFtQkg7WUFDM0I7K0NBQU8sSUFBTWhCLEtBQUtvQixHQUFHLENBQUMsbUJBQW1CSjs7UUFDM0M7c0NBQUc7UUFBQ2hCO0tBQUs7SUFFVCwyQ0FBMkM7SUFDM0MsTUFBTUMsSUFBSSxDQUFDb0IsS0FBYUMsU0FBa0NDO1FBQ3hELElBQUk7WUFDRixJQUFJLENBQUNsQixTQUFTLE9BQU9rQixZQUFZRjtZQUVqQyxJQUFJRyxjQUFjdEIsTUFBTW1CLEtBQUtDO1lBRTdCLHVEQUF1RDtZQUN2RCxJQUFJRSxnQkFBZ0JILE9BQU9FLFVBQVU7Z0JBQ25DQyxjQUFjRDtZQUNoQjtZQUVBLGtEQUFrRDtZQUNsRCxJQUFJRCxXQUFXRSxlQUFlLE9BQU9BLGdCQUFnQixVQUFVO2dCQUM3REMsT0FBT0MsSUFBSSxDQUFDSixTQUFTSyxPQUFPLENBQUNDLENBQUFBO29CQUMzQixNQUFNQyxjQUFjLEtBQWUsT0FBVkQsV0FBVTtvQkFDbkMsSUFBSUosWUFBWU0sUUFBUSxDQUFDRCxjQUFjO3dCQUNyQ0wsY0FBY0EsWUFBWU8sT0FBTyxDQUFDLElBQUlDLE9BQU9ILGFBQWEsTUFBTUksT0FBT1gsT0FBTyxDQUFDTSxVQUFVO29CQUMzRjtnQkFDRjtZQUNGO1lBRUEsT0FBT0osZUFBZUQsWUFBWUY7UUFDcEMsRUFBRSxPQUFPTixPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxnQ0FBZ0NNLEtBQUtOO1lBQ25ELE9BQU9RLFlBQVlGO1FBQ3JCO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTVQsaUJBQWlCLENBQUNzQjtRQUN0QixJQUFJO1lBQ0ZsQyxLQUFLWSxjQUFjLENBQUNzQjtRQUN0QixFQUFFLE9BQU9uQixPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyw0QkFBNEJBO1FBQzVDO0lBQ0Y7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTW9CLGFBQWEsQ0FBQ0M7WUFrQlhDO1FBakJQLE1BQU1BLGVBQTREO1lBQ2hFLFFBQVE7Z0JBQUVDLElBQUk7Z0JBQVFDLElBQUk7WUFBTztZQUNqQyxVQUFVO2dCQUFFRCxJQUFJO2dCQUFVQyxJQUFJO1lBQVM7WUFDdkMsV0FBVztnQkFBRUQsSUFBSTtnQkFBV0MsSUFBSTtZQUFVO1lBQzFDLFFBQVE7Z0JBQUVELElBQUk7Z0JBQVFDLElBQUk7WUFBTztZQUNqQyxVQUFVO2dCQUFFRCxJQUFJO2dCQUFVQyxJQUFJO1lBQVM7WUFDdkMsU0FBUztnQkFBRUQsSUFBSTtnQkFBU0MsSUFBSTtZQUFRO1lBQ3BDLFNBQVM7Z0JBQUVELElBQUk7Z0JBQVNDLElBQUk7WUFBUTtZQUNwQyxRQUFRO2dCQUFFRCxJQUFJO2dCQUFRQyxJQUFJO1lBQU87WUFDakMsT0FBTztnQkFBRUQsSUFBSTtnQkFBT0MsSUFBSTtZQUFNO1lBQzlCLFNBQVM7Z0JBQUVELElBQUk7Z0JBQVNDLElBQUk7WUFBUTtZQUNwQyxRQUFRO2dCQUFFRCxJQUFJO2dCQUFRQyxJQUFJO1lBQU87WUFDakMsUUFBUTtnQkFBRUQsSUFBSTtnQkFBUUMsSUFBSTtZQUFPO1lBQ2pDLFNBQVM7Z0JBQUVELElBQUk7Z0JBQVNDLElBQUk7WUFBUTtZQUNwQyxPQUFPO2dCQUFFRCxJQUFJO2dCQUFnQkMsSUFBSTtZQUFZO1FBQy9DO1FBRUEsT0FBT0YsRUFBQUEscUJBQUFBLFlBQVksQ0FBQ0QsS0FBSyxjQUFsQkMseUNBQUFBLGtCQUFvQixDQUFDbEMsWUFBWSxLQUFJaUM7SUFDOUM7SUFFQSxPQUFPO1FBQ0xuQztRQUNBa0M7UUFDQWhDO1FBQ0FxQyxPQUFPckMsZ0JBQWdCO1FBQ3ZCRTtRQUNBTztRQUNBWjtJQUNGO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxzcmNcXGhvb2tzXFx1c2VBcHBUcmFuc2xhdGlvbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VUcmFuc2xhdGlvbiB9IGZyb20gJ3JlYWN0LWkxOG5leHQnO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuLy8g2YbYuNin2YUg2KrYsdis2YXYqSDYp9it2KrYsdin2YHZiiDZhdir2YQg2KfZhNiq2LfYqNmK2YLYp9iqINin2YTZg9io2YrYsdipXG5leHBvcnQgY29uc3QgdXNlQXBwVHJhbnNsYXRpb24gPSAoKSA9PiB7XG4gIGNvbnN0IHsgaTE4biwgdDogaTE4blQgfSA9IHVzZVRyYW5zbGF0aW9uKCdjb21tb24nKTtcbiAgY29uc3QgW2N1cnJlbnRMYW5nLCBzZXRDdXJyZW50TGFuZ10gPSB1c2VTdGF0ZTwnYXInIHwgJ2VuJz4oJ2FyJyk7XG4gIGNvbnN0IFtpc1JlYWR5LCBzZXRJc1JlYWR5XSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDYqtmH2YrYptipINin2YTZhNi62Kkg2LnZhtivINio2K/YoSDYp9mE2KrYt9io2YrZglxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRMYW5ndWFnZSA9ICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vINis2YTYqCDYp9mE2YTYutipINin2YTZhdit2YHZiNi42Kkg2KPZiCDYp9iz2KrYrtiv2KfZhSDYp9mE2LnYsdio2YrYqSDZg9in2YHYqtix2KfYttmKXG4gICAgICAgIGNvbnN0IHNhdmVkTGFuZyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdsYW5ndWFnZScpIHx8ICdhcic7XG4gICAgICAgIGNvbnN0IHZhbGlkTGFuZyA9IChzYXZlZExhbmcgPT09ICdlbicgfHwgc2F2ZWRMYW5nID09PSAnYXInKSA/IHNhdmVkTGFuZyA6ICdhcic7XG5cbiAgICAgICAgc2V0Q3VycmVudExhbmcodmFsaWRMYW5nKTtcbiAgICAgICAgaTE4bi5jaGFuZ2VMYW5ndWFnZSh2YWxpZExhbmcpO1xuICAgICAgICBzZXRJc1JlYWR5KHRydWUpO1xuXG4gICAgICAgIGNvbnNvbGUubG9nKCfwn4yQIExhbmd1YWdlIGluaXRpYWxpemVkOicsIHZhbGlkTGFuZyk7XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgTGFuZ3VhZ2UgaW5pdGlhbGl6YXRpb24gZXJyb3I6JywgZXJyb3IpO1xuICAgICAgICBzZXRDdXJyZW50TGFuZygnYXInKTtcbiAgICAgICAgc2V0SXNSZWFkeSh0cnVlKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgaW5pdExhbmd1YWdlKCk7XG4gIH0sIFtpMThuXSk7XG5cbiAgLy8g2YXYsdin2YLYqNipINiq2LrZitmK2LEg2KfZhNmE2LrYqVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUxhbmd1YWdlQ2hhbmdlID0gKGxuZzogc3RyaW5nKSA9PiB7XG4gICAgICBjb25zdCB2YWxpZExhbmcgPSAobG5nID09PSAnZW4nIHx8IGxuZyA9PT0gJ2FyJykgPyBsbmcgYXMgJ2FyJyB8ICdlbicgOiAnYXInO1xuICAgICAgc2V0Q3VycmVudExhbmcodmFsaWRMYW5nKTtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdsYW5ndWFnZScsIHZhbGlkTGFuZyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UhCBMYW5ndWFnZSBjaGFuZ2VkIHRvOicsIHZhbGlkTGFuZyk7XG4gICAgfTtcblxuICAgIGkxOG4ub24oJ2xhbmd1YWdlQ2hhbmdlZCcsIGhhbmRsZUxhbmd1YWdlQ2hhbmdlKTtcbiAgICByZXR1cm4gKCkgPT4gaTE4bi5vZmYoJ2xhbmd1YWdlQ2hhbmdlZCcsIGhhbmRsZUxhbmd1YWdlQ2hhbmdlKTtcbiAgfSwgW2kxOG5dKTtcblxuICAvLyDYr9in2YTYqSDYqtix2KzZhdipINii2YXZhtipINmI2YXYttmF2YjZhtipINmF2LkgaW50ZXJwb2xhdGlvblxuICBjb25zdCB0ID0gKGtleTogc3RyaW5nLCBvcHRpb25zPzogeyBba2V5OiBzdHJpbmddOiBhbnkgfSwgZmFsbGJhY2s/OiBzdHJpbmcpOiBzdHJpbmcgPT4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAoIWlzUmVhZHkpIHJldHVybiBmYWxsYmFjayB8fCBrZXk7XG5cbiAgICAgIGxldCB0cmFuc2xhdGlvbiA9IGkxOG5UKGtleSwgb3B0aW9ucyk7XG5cbiAgICAgIC8vINil2LDYpyDZg9in2YbYqiDYp9mE2KrYsdis2YXYqSDZhdmB2YLZiNiv2KnYjCDYp9iz2KrYrtiv2YUg2KfZhNin2K3YqtmK2KfYt9mKINij2Ygg2KfZhNmF2YHYqtin2K1cbiAgICAgIGlmICh0cmFuc2xhdGlvbiA9PT0ga2V5ICYmIGZhbGxiYWNrKSB7XG4gICAgICAgIHRyYW5zbGF0aW9uID0gZmFsbGJhY2s7XG4gICAgICB9XG5cbiAgICAgIC8vINmF2LnYp9mE2KzYqSBpbnRlcnBvbGF0aW9uINmK2K/ZiNmK2KfZiyDYpdiw2Kcg2YTZhSDZiti52YXZhCBpMThuZXh0XG4gICAgICBpZiAob3B0aW9ucyAmJiB0cmFuc2xhdGlvbiAmJiB0eXBlb2YgdHJhbnNsYXRpb24gPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIE9iamVjdC5rZXlzKG9wdGlvbnMpLmZvckVhY2gob3B0aW9uS2V5ID0+IHtcbiAgICAgICAgICBjb25zdCBwbGFjZWhvbGRlciA9IGB7eyR7b3B0aW9uS2V5fX19YDtcbiAgICAgICAgICBpZiAodHJhbnNsYXRpb24uaW5jbHVkZXMocGxhY2Vob2xkZXIpKSB7XG4gICAgICAgICAgICB0cmFuc2xhdGlvbiA9IHRyYW5zbGF0aW9uLnJlcGxhY2UobmV3IFJlZ0V4cChwbGFjZWhvbGRlciwgJ2cnKSwgU3RyaW5nKG9wdGlvbnNbb3B0aW9uS2V5XSkpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB0cmFuc2xhdGlvbiB8fCBmYWxsYmFjayB8fCBrZXk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBUcmFuc2xhdGlvbiBlcnJvciBmb3Iga2V5OicsIGtleSwgZXJyb3IpO1xuICAgICAgcmV0dXJuIGZhbGxiYWNrIHx8IGtleTtcbiAgICB9XG4gIH07XG5cbiAgLy8g2K/Yp9mE2Kkg2KrYutmK2YrYsSDYp9mE2YTYutipXG4gIGNvbnN0IGNoYW5nZUxhbmd1YWdlID0gKG5ld0xhbmc6ICdhcicgfCAnZW4nKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGkxOG4uY2hhbmdlTGFuZ3VhZ2UobmV3TGFuZyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBMYW5ndWFnZSBjaGFuZ2UgZXJyb3I6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyDYr9in2YTYqSDYqtix2KzZhdipINij2YbZiNin2Lkg2KfZhNmF2YjYp9ivICjZhdit2YHZiNi42Kkg2YPZhdinINmH2YopXG4gIGNvbnN0IHRNZWRpYVR5cGUgPSAodHlwZTogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgICBjb25zdCBtZWRpYVR5cGVNYXA6IFJlY29yZDxzdHJpbmcsIFJlY29yZDwnYXInIHwgJ2VuJywgc3RyaW5nPj4gPSB7XG4gICAgICAnRklMTSc6IHsgYXI6ICdGaWxtJywgZW46ICdGaWxtJyB9LFxuICAgICAgJ1NFUklFUyc6IHsgYXI6ICdTZXJpZXMnLCBlbjogJ1NlcmllcycgfSxcbiAgICAgICdQUk9HUkFNJzogeyBhcjogJ1Byb2dyYW0nLCBlbjogJ1Byb2dyYW0nIH0sXG4gICAgICAnU09ORyc6IHsgYXI6ICdTb25nJywgZW46ICdTb25nJyB9LFxuICAgICAgJ0ZJTExFUic6IHsgYXI6ICdGaWxsZXInLCBlbjogJ0ZpbGxlcicgfSxcbiAgICAgICdTVElORyc6IHsgYXI6ICdTdGluZycsIGVuOiAnU3RpbmcnIH0sXG4gICAgICAnUFJPTU8nOiB7IGFyOiAnUHJvbW8nLCBlbjogJ1Byb21vJyB9LFxuICAgICAgJ05FWFQnOiB7IGFyOiAnTmV4dCcsIGVuOiAnTmV4dCcgfSxcbiAgICAgICdOT1cnOiB7IGFyOiAnTm93JywgZW46ICdOb3cnIH0sXG4gICAgICAn2LPZhti52YjYryc6IHsgYXI6ICfYs9mG2LnZiNivJywgZW46ICfYs9mG2LnZiNivJyB9LFxuICAgICAgJ9i52K/ZhtinJzogeyBhcjogJ9i52K/ZhtinJywgZW46ICfYudiv2YbYpycgfSxcbiAgICAgICdNSU5JJzogeyBhcjogJ01pbmknLCBlbjogJ01pbmknIH0sXG4gICAgICAnQ1JPU1MnOiB7IGFyOiAnQ3Jvc3MnLCBlbjogJ0Nyb3NzJyB9LFxuICAgICAgJ0FMTCc6IHsgYXI6ICfYrNmF2YrYuSDYp9mE2KPZhtmI2KfYuScsIGVuOiAnQWxsIFR5cGVzJyB9XG4gICAgfTtcblxuICAgIHJldHVybiBtZWRpYVR5cGVNYXBbdHlwZV0/LltjdXJyZW50TGFuZ10gfHwgdHlwZTtcbiAgfTtcblxuICByZXR1cm4ge1xuICAgIHQsXG4gICAgdE1lZGlhVHlwZSxcbiAgICBjdXJyZW50TGFuZyxcbiAgICBpc1JUTDogY3VycmVudExhbmcgPT09ICdhcicsXG4gICAgaXNSZWFkeSxcbiAgICBjaGFuZ2VMYW5ndWFnZSxcbiAgICBpMThuXG4gIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZVRyYW5zbGF0aW9uIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VBcHBUcmFuc2xhdGlvbiIsImkxOG4iLCJ0IiwiaTE4blQiLCJjdXJyZW50TGFuZyIsInNldEN1cnJlbnRMYW5nIiwiaXNSZWFkeSIsInNldElzUmVhZHkiLCJpbml0TGFuZ3VhZ2UiLCJzYXZlZExhbmciLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidmFsaWRMYW5nIiwiY2hhbmdlTGFuZ3VhZ2UiLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJoYW5kbGVMYW5ndWFnZUNoYW5nZSIsImxuZyIsInNldEl0ZW0iLCJvbiIsIm9mZiIsImtleSIsIm9wdGlvbnMiLCJmYWxsYmFjayIsInRyYW5zbGF0aW9uIiwiT2JqZWN0Iiwia2V5cyIsImZvckVhY2giLCJvcHRpb25LZXkiLCJwbGFjZWhvbGRlciIsImluY2x1ZGVzIiwicmVwbGFjZSIsIlJlZ0V4cCIsIlN0cmluZyIsIm5ld0xhbmciLCJ0TWVkaWFUeXBlIiwidHlwZSIsIm1lZGlhVHlwZU1hcCIsImFyIiwiZW4iLCJpc1JUTCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAppTranslation.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useTranslatedToast.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useTranslatedToast.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslatedToast: () => (/* binding */ useTranslatedToast)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Toast */ \"(app-pages-browser)/./src/components/Toast.tsx\");\n\n\nconst useTranslatedToast = ()=>{\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    const showTranslatedToast = function(messageKey) {\n        let type = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'info';\n        const message = t(\"messages.\".concat(type, \".\").concat(messageKey));\n        showToast(message, type);\n    };\n    const showSuccessToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'success');\n    };\n    const showErrorToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'error');\n    };\n    const showInfoToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'info');\n    };\n    return {\n        showTranslatedToast,\n        showSuccessToast,\n        showErrorToast,\n        showInfoToast,\n        ToastContainer\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useTranslatedToast.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);