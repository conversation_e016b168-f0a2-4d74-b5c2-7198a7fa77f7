'use client';
import React, { useState, useEffect, useLayoutEffect, useRef, useCallback } from 'react';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useAppTranslation } from '@/hooks/useAppTranslation';

interface MediaItem {
  id: string;
  name: string;
  type: string;
  duration: string;
  description?: string;
  isTemporary?: boolean;
  temporaryType?: string;
  episodeNumber?: number;
  seasonNumber?: number;
  partNumber?: number;
  isFromSchedule?: boolean;
  originalScheduleItem?: any;
}

interface ScheduleItem {
  id: string;
  mediaItemId: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  isRerun?: boolean;
  isTemporary?: boolean;
  mediaItem?: MediaItem;
  weekStart: string;
  episodeNumber?: number;
  seasonNumber?: number;
  partNumber?: number;
  createdAt?: string;
}

export default function WeeklySchedulePage() {
  const { isViewer } = useAuth();
  const { t, tMediaType, isRTL } = useAppTranslation();

  const [loading, setLoading] = useState(true);
  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([]);
  const [availableMedia, setAvailableMedia] = useState<MediaItem[]>([]);
  const [selectedWeek, setSelectedWeek] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [draggedItem, setDraggedItem] = useState<MediaItem | null>(null);
  const scrollPositionRef = useRef<number>(0);
  const shouldRestoreScroll = useRef<boolean>(false);
  const [readOnlyMode, setReadOnlyMode] = useState(false);
  
  // مرجع للجدول لتثبيت موضع التمرير
  const scheduleTableRef = useRef<HTMLDivElement>(null);
  const visibleRowRef = useRef<HTMLDivElement>(null);
  const visibleRowIndexRef = useRef<number>(-1);
  
  // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل
  useEffect(() => {
    if (isViewer) {
      setReadOnlyMode(true);
    }
  }, [isViewer]);

  // حالات المواد المؤقتة
  const [tempMediaName, setTempMediaName] = useState('');
  const [tempMediaType, setTempMediaType] = useState('PROGRAM');
  const [tempMediaDuration, setTempMediaDuration] = useState('01:00:00');
  const [tempMediaNotes, setTempMediaNotes] = useState('');
  const [tempMediaItems, setTempMediaItems] = useState<MediaItem[]>([]);

  // أيام الأسبوع
  const days = [
    t('common.sunday'),
    t('common.monday'),
    t('common.tuesday'),
    t('common.wednesday'),
    t('common.thursday'),
    t('common.friday'),
    t('common.saturday')
  ];





  // حساب تواريخ الأسبوع بالأرقام العربية العادية
  const getWeekDates = () => {
    if (!selectedWeek) return ['--/--', '--/--', '--/--', '--/--', '--/--', '--/--', '--/--'];

    // التأكد من أن selectedWeek يمثل يوم الأحد
    const inputDate = new Date(selectedWeek + 'T00:00:00'); // استخدام منتصف الليل
    console.log('📅 التاريخ المدخل:', selectedWeek);
    console.log('📅 يوم الأسبوع للتاريخ المدخل:', inputDate.getDay(), '(0=أحد)');

    // التأكد من أن نبدأ من يوم الأحد
    const sundayDate = new Date(inputDate);
    const dayOfWeek = inputDate.getDay();
    if (dayOfWeek !== 0) {
      // إذا لم يكن الأحد، نحسب الأحد السابق
      sundayDate.setDate(inputDate.getDate() - dayOfWeek);
    }

    console.log('📅 يوم الأحد المحسوب:', sundayDate.toISOString().split('T')[0]);

    const dates = [];

    for (let i = 0; i < 7; i++) {
      // إنشاء تاريخ جديد لكل يوم بدءاً من الأحد
      const currentDate = new Date(sundayDate);
      currentDate.setDate(sundayDate.getDate() + i);

      // استخدام التنسيق العربي للتاريخ (يوم/شهر/سنة)
      const day = currentDate.getDate();
      const month = currentDate.getMonth() + 1; // الشهور تبدأ من 0
      const year = currentDate.getFullYear();

      // تنسيق التاريخ بالشكل dd/mm/yyyy
      const dateStr = `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
      dates.push(dateStr);

      // التحقق من صحة اليوم
      const actualDayOfWeek = currentDate.getDay();
      console.log(`  يوم ${i} (${days[i]}): ${currentDate.toISOString().split('T')[0]} → ${dateStr} [يوم الأسبوع: ${actualDayOfWeek}]`);

      // تحذير إذا كان اليوم لا يتطابق مع المتوقع
      if (actualDayOfWeek !== i) {
        console.warn(`⚠️ عدم تطابق: متوقع يوم ${i} لكن حصلنا على ${actualDayOfWeek}`);
      }
    }
    return dates;
  };

  // استخدام useEffect لتحديث التواريخ عند تغيير الأسبوع المحدد
  const [weekDates, setWeekDates] = useState<string[]>(['--/--', '--/--', '--/--', '--/--', '--/--', '--/--', '--/--']);
  
  useEffect(() => {
    const dates = getWeekDates();
    console.log('🔄 تحديث التواريخ:', dates);
    setWeekDates(dates);
  }, [selectedWeek]);

  // الساعات من 08:00 إلى 07:00 (24 ساعة)
  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = (i + 8) % 24;
    return `${hour.toString().padStart(2, '0')}:00`;
  });

  // حساب المدة الإجمالية للمادة
  const calculateTotalDuration = (segments: any[]) => {
    if (!segments || segments.length === 0) return '01:00:00';

    let totalSeconds = 0;
    segments.forEach(segment => {
      const [hours, minutes, seconds] = segment.duration.split(':').map(Number);
      totalSeconds += hours * 3600 + minutes * 60 + seconds;
    });

    const hours_calc = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;

    return `${hours_calc.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // إنشاء نص عرض المادة مع التفاصيل
  const getMediaDisplayText = (item: MediaItem) => {
    let displayText = item.name || t('schedule.unknown');

    // إضافة تفاصيل الحلقات والأجزاء
    if (item.type === 'SERIES') {
      if (item.seasonNumber && item.episodeNumber) {
        displayText += ` - ${t('schedule.season')} ${item.seasonNumber} ${t('schedule.episode')} ${item.episodeNumber}`;
      } else if (item.episodeNumber) {
        displayText += ` - ${t('schedule.episode')} ${item.episodeNumber}`;
      }
    } else if (item.type === 'PROGRAM') {
      if (item.seasonNumber && item.episodeNumber) {
        displayText += ` - ${t('schedule.season')} ${item.seasonNumber} ${t('schedule.episode')} ${item.episodeNumber}`;
      } else if (item.episodeNumber) {
        displayText += ` - ${t('schedule.episode')} ${item.episodeNumber}`;
      }
    } else if (item.type === 'MOVIE' && item.partNumber) {
      displayText += ` - ${t('schedule.part')} ${item.partNumber}`;
    }

    return displayText;
  };

  // تحديد الأسبوع الحالي
  useEffect(() => {
    // استخدام التاريخ المحلي مع تجنب مشاكل المنطقة الزمنية
    const today = new Date();

    // تحويل إلى تاريخ محلي بدون وقت
    const year = today.getFullYear();
    const month = today.getMonth();
    const day = today.getDate();
    const localDate = new Date(year, month, day);

    // إضافة تسجيل للتحقق
    console.log('🔍 حساب الأسبوع الحالي:');
    console.log('  📅 اليوم الفعلي:', `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`);
    console.log('  📊 يوم الأسبوع:', localDate.getDay(), '(0=أحد)');

    // حساب يوم الأحد لهذا الأسبوع
    const sunday = new Date(localDate);
    sunday.setDate(localDate.getDate() - localDate.getDay());

    // تحويل إلى string بطريقة محلية
    const weekStart = `${sunday.getFullYear()}-${(sunday.getMonth() + 1).toString().padStart(2, '0')}-${sunday.getDate().toString().padStart(2, '0')}`;

    console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);
    console.log('  📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');

    setSelectedWeek(weekStart);
  }, []);

  // جلب البيانات
  useEffect(() => {
    if (selectedWeek) {
      fetchScheduleData();
    }
  }, [selectedWeek]);

  // حفظ الصف المرئي قبل تحديث البيانات
  const saveVisibleRowIndex = useCallback(() => {
    if (!scheduleTableRef.current) {
      console.log('⚠️ لم يتم العثور على مرجع الجدول');
      return;
    }
    
    // الحصول على جميع صفوف الساعات في الجدول
    const hourRows = scheduleTableRef.current.querySelectorAll('.hour-row');
    if (!hourRows.length) {
      console.log('⚠️ لم يتم العثور على صفوف الساعات');
      return;
    }
    
    console.log(`🔍 تم العثور على ${hourRows.length} صف ساعة`);
    
    // تحديد الصف المرئي حاليًا في منتصف الشاشة
    const viewportHeight = window.innerHeight;
    const viewportMiddle = window.scrollY + (viewportHeight / 2);
    
    console.log(`📏 منتصف الشاشة: ${viewportMiddle}px (ارتفاع الشاشة: ${viewportHeight}px, موضع التمرير: ${window.scrollY}px)`);
    
    let closestRow = null;
    let closestDistance = Infinity;
    let closestIndex = -1;
    
    // البحث عن أقرب صف للمنتصف
    hourRows.forEach((row, index) => {
      const rect = row.getBoundingClientRect();
      const rowTop = window.scrollY + rect.top;
      const rowBottom = rowTop + rect.height;
      const rowMiddle = rowTop + (rect.height / 2);
      const distance = Math.abs(viewportMiddle - rowMiddle);
      
      console.log(`  صف ${index} (${hours[index]}): العلوي=${rect.top.toFixed(0)}, الارتفاع=${rect.height.toFixed(0)}, المسافة=${distance.toFixed(0)}`);
      
      if (distance < closestDistance) {
        closestDistance = distance;
        closestRow = row;
        closestIndex = index;
      }
    });
    
    if (closestRow) {
      visibleRowRef.current = closestRow as HTMLDivElement;
      visibleRowIndexRef.current = closestIndex;
      console.log(`📍 تم حفظ الصف المرئي: الساعة ${hours[closestIndex]}, الفهرس ${closestIndex}, المسافة=${closestDistance.toFixed(0)}px`);
    } else {
      console.log('⚠️ لم يتم العثور على صف مرئي');
    }
  }, [hours]);
  
  // استعادة موضع التمرير بعد تحديث البيانات - نسخة مبسطة وأكثر فعالية
  useLayoutEffect(() => {
    if (shouldRestoreScroll.current && scrollPositionRef.current !== undefined) {
      console.log(`🔄 استعادة موضع التمرير: ${scrollPositionRef.current}px`);

      // تأخير للتأكد من اكتمال الرندر
      const timer = setTimeout(() => {
        const targetPosition = scrollPositionRef.current;

        // استعادة الموضع مباشرة
        window.scrollTo({
          top: targetPosition,
          behavior: 'instant'
        });

        console.log(`📍 تم استعادة الموضع إلى: ${targetPosition}px`);

        // التحقق من نجاح العملية والتصحيح إذا لزم الأمر
        setTimeout(() => {
          const currentPosition = window.scrollY;
          const difference = Math.abs(currentPosition - targetPosition);

          if (difference > 5) {
            console.log(`📍 تصحيح الموضع: ${currentPosition}px → ${targetPosition}px (فرق: ${difference}px)`);
            window.scrollTo({
              top: targetPosition,
              behavior: 'instant'
            });
          } else {
            console.log(`✅ تم تثبيت موضع التمرير بنجاح`);
          }
        }, 100);

        shouldRestoreScroll.current = false;
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [scheduleItems]);

  const fetchScheduleData = async () => {
    try {
      setLoading(true);
      console.log('🔄 بدء تحديث البيانات...');

      // التأكد من حفظ موضع التمرير إذا لم يكن محفوظاً
      if (!shouldRestoreScroll.current) {
        const currentScrollPosition = window.scrollY;
        scrollPositionRef.current = currentScrollPosition;
        shouldRestoreScroll.current = true;
        console.log(`📍 حفظ تلقائي لموضع التمرير: ${currentScrollPosition}px`);
      }

      console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');

      const url = `/api/weekly-schedule?weekStart=${selectedWeek}`;
      console.log('🌐 إرسال طلب إلى:', url);

      const response = await fetch(url);
      console.log('📡 تم استلام الاستجابة:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('📊 تم تحليل البيانات:', result.success);

      if (result.success) {
        // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)
        const allItems = result.data.scheduleItems || [];
        const apiTempItems = result.data.tempItems || [];

        // تحديث المواد المؤقتة في القائمة الجانبية
        console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map(item => ({ id: item.id, name: item.name })));
        setTempMediaItems(apiTempItems);

        setScheduleItems(allItems);
        setAvailableMedia(result.data.availableMedia || []);

        const regularItems = allItems.filter(item => !item.isTemporary && !item.isRerun);
        const tempItems = allItems.filter(item => item.isTemporary && !item.isRerun);
        const reruns = allItems.filter(item => item.isRerun);

        console.log(`📊 تم تحديث الجدول: ${regularItems.length} مادة عادية + ${tempItems.length} مؤقتة + ${reruns.length} إعادة = ${allItems.length} إجمالي`);
        console.log(`📦 تم تحديث القائمة الجانبية: ${apiTempItems.length} مادة مؤقتة`);
      } else {
        console.error('❌ خطأ في الاستجابة:', result.error);
        alert(`خطأ في تحديث البيانات: ${result.error}`);

        // الحفاظ على المواد المؤقتة حتى في حالة الخطأ
        setScheduleItems(currentTempItems);
        setAvailableMedia([]);
      }
    } catch (error) {
      console.error('❌ خطأ في جلب البيانات:', error);
      alert(`خطأ في الاتصال: ${error.message}`);

      // الحفاظ على المواد المؤقتة حتى في حالة الخطأ
      const currentTempItemsError = scheduleItems.filter(item => item.isTemporary);
      setScheduleItems(currentTempItemsError);
      setAvailableMedia([]);
    } finally {
      console.log('✅ انتهاء تحديث البيانات');
      setLoading(false);
    }
  };

  // إضافة مادة مؤقتة
  const addTempMedia = async () => {
    if (!tempMediaName.trim()) {
      alert(t('schedule.enterMediaName'));
      return;
    }

    const newTempMedia: MediaItem = {
      id: `temp_${Date.now()}`,
      name: tempMediaName.trim(),
      type: tempMediaType,
      duration: tempMediaDuration,
      description: tempMediaNotes.trim() || undefined,
      isTemporary: true,
      temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' :
                   tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'
    };

    try {
      // حفظ المادة المؤقتة في القائمة الجانبية عبر API
      const response = await fetch('/api/weekly-schedule', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'saveTempToSidebar',
          tempMedia: newTempMedia,
          weekStart: selectedWeek
        })
      });

      const result = await response.json();
      if (result.success) {
        setTempMediaItems(prev => [...prev, newTempMedia]);
        console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');
      } else {
        alert(result.error || t('messages.saveFailed'));
        return;
      }
    } catch (error) {
      console.error('❌ خطأ في حفظ المادة المؤقتة:', error);
      alert(t('messages.saveFailed'));
      return;
    }

    // إعادة تعيين النموذج
    setTempMediaName('');
    setTempMediaNotes('');
    setTempMediaDuration('01:00:00');

    console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);
  };

  // حذف مادة مؤقتة من القائمة الجانبية
  const deleteTempMedia = async (tempMediaId: string) => {
    if (!confirm(t('schedule.confirmDeleteTemp'))) {
      return;
    }

    try {
      const response = await fetch('/api/weekly-schedule', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'deleteTempFromSidebar',
          tempMediaId,
          weekStart: selectedWeek
        })
      });

      const result = await response.json();
      if (result.success) {
        setTempMediaItems(prev => prev.filter(item => item.id !== tempMediaId));
        console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');
      } else {
        alert(result.error || t('messages.deleteFailed'));
      }
    } catch (error) {
      console.error('❌ خطأ في حذف المادة المؤقتة:', error);
      alert(t('messages.deleteFailed'));
    }
  };

  // حذف مادة مؤقتة
  const removeTempMedia = (id: string) => {
    setTempMediaItems(prev => prev.filter(item => item.id !== id));
  };

  // دمج المواد العادية والمؤقتة
  const allAvailableMedia = [...availableMedia, ...tempMediaItems];

  // فلترة المواد حسب النوع والبحث
  const filteredMedia = allAvailableMedia.filter(item => {
    const matchesType = selectedType === '' || item.type === selectedType;
    const itemName = item.name || '';
    const itemType = item.type || '';
    const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         itemType.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesType && matchesSearch;
  });

  // التحقق من البرايم تايم
  const isPrimeTimeSlot = (dayOfWeek: number, timeStr: string) => {
    const hour = parseInt(timeStr.split(':')[0]);

    // الأحد-الأربعاء: 18:00-23:59
    if (dayOfWeek >= 0 && dayOfWeek <= 3) {
      return hour >= 18;
    }

    // الخميس-السبت: 18:00-23:59 أو 00:00-01:59
    if (dayOfWeek >= 4 && dayOfWeek <= 6) {
      return hour >= 18 || hour < 2;
    }

    return false;
  };

  // دالة توليد الإعادات تم إيقافها نهائياً
  const generateLocalTempReruns = (originalItem: any) => {
    console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');
    return [];
  };

  // دالة توليد الإعادات تم إيقافها نهائياً
  const generateLocalRerunsWithItems = (tempItems: any[], checkItems: any[]) => {
    console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');
    return [];
  };

  // دالة توليد الإعادات تم إيقافها نهائياً
  const generateLocalReruns = (tempItems: any[]) => {
    console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');
    return [];
  };

  // دالة توليد الإعادات تم إيقافها نهائياً
  const generateTempReruns = (originalItem: any) => {
    console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');
    return [];
  };

  // التحقق من التداخل في الأوقات
  const checkTimeConflict = (newItem: any, existingItems: any[]) => {
    try {
      const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');
      const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');

      const conflict = existingItems.some(item => {
        if (item.dayOfWeek !== newItem.dayOfWeek) return false;

        const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');
        const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');

        return (newStart < itemEnd && newEnd > itemStart);
      });

      if (conflict) {
        console.log('⚠️ تم اكتشاف تداخل:', newItem);
      }

      return conflict;
    } catch (error) {
      console.error('خطأ في فحص التداخل:', error);
      return false; // في حالة الخطأ، اسمح بالإضافة
    }
  };

  // إضافة مادة للجدول
  const addItemToSchedule = async (mediaItem: MediaItem, dayOfWeek: number, hour: string) => {
    try {
      // حفظ موضع التمرير الحالي بدقة
      const currentScrollPosition = window.scrollY;
      scrollPositionRef.current = currentScrollPosition;

      // حفظ الصف المرئي قبل التحديث
      saveVisibleRowIndex();
      shouldRestoreScroll.current = true;
      console.log(`📍 تم حفظ موضع التمرير: ${currentScrollPosition}px والصف المرئي عند إضافة مادة`);

      console.log('🎯 محاولة إضافة مادة:', {
        name: mediaItem.name,
        isTemporary: mediaItem.isTemporary,
        dayOfWeek,
        hour,
        scrollPosition: scrollPositionRef.current
      });

      const startTime = hour;
      const endTime = `${(parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0')}:00`;

      // التحقق من التداخل
      const newItem = {
        dayOfWeek,
        startTime,
        endTime
      };

      if (checkTimeConflict(newItem, scheduleItems)) {
        alert('⚠️ ' + t('schedule.timeConflict'));
        console.log('❌ تم منع الإضافة بسبب التداخل');
        return;
      }

      // التحقق من المواد المؤقتة
      if (mediaItem.isTemporary) {
        console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');
        console.log('📋 بيانات المادة:', {
          id: mediaItem.id,
          name: mediaItem.name,
          type: mediaItem.type,
          duration: mediaItem.duration,
          fullItem: mediaItem
        });

        // التحقق من صحة البيانات
        if (!mediaItem.name || mediaItem.name === 'undefined') {
          console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);
          alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');
          shouldRestoreScroll.current = false;
          return;
        }

        // حذف المادة من القائمة الجانبية محلياً أولاً
        setTempMediaItems(prev => {
          const filtered = prev.filter(item => item.id !== mediaItem.id);
          console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);
          console.log('📊 المواد المتبقية في القائمة:', filtered.length);
          return filtered;
        });

        // التأكد من صحة البيانات قبل الإرسال
        const cleanMediaItem = {
          ...mediaItem,
          name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',
          id: mediaItem.id || `temp_${Date.now()}`,
          type: mediaItem.type || 'PROGRAM',
          duration: mediaItem.duration || '01:00:00'
        };

        console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);

        // إرسال المادة المؤقتة إلى API
        const response = await fetch('/api/weekly-schedule', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            mediaItemId: cleanMediaItem.id,
            dayOfWeek,
            startTime,
            endTime,
            weekStart: selectedWeek,
            isTemporary: true,
            mediaItem: cleanMediaItem
          })
        });

        const result = await response.json();
        if (result.success) {
          console.log('✅ تم نقل المادة المؤقتة إلى الجدول');

          // حذف المادة من القائمة الجانبية في الخادم بعد النجاح
          try {
            await fetch('/api/weekly-schedule', {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                action: 'deleteTempFromSidebar',
                tempMediaId: mediaItem.id,
                weekStart: selectedWeek
              })
            });
            console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');
          } catch (error) {
            console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);
          }

          // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل
          const newScheduleItem = {
            id: result.data.id,
            mediaItemId: mediaItem.id,
            dayOfWeek,
            startTime,
            endTime,
            weekStart: selectedWeek,
            isTemporary: true,
            mediaItem: mediaItem
          };

          setScheduleItems(prev => [...prev, newScheduleItem]);
          console.log('✅ تم إضافة المادة للجدول محلياً');
        } else {
          // في حالة الفشل، أعد المادة للقائمة الجانبية
          setTempMediaItems(prev => [...prev, mediaItem]);
          alert(result.error);
          shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ
        }

        return;
      }

      // للمواد العادية - استخدام API
      const response = await fetch('/api/weekly-schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          mediaItemId: mediaItem.id,
          dayOfWeek,
          startTime,
          endTime,
          weekStart: selectedWeek,
          // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة
          episodeNumber: mediaItem.episodeNumber,
          seasonNumber: mediaItem.seasonNumber,
          partNumber: mediaItem.partNumber
        })
      });

      const result = await response.json();
      if (result.success) {
        // تحديث محلي بدلاً من إعادة تحميل كامل
        const newScheduleItem = {
          id: result.data.id,
          mediaItemId: mediaItem.id,
          dayOfWeek,
          startTime: startTime,
          endTime: endTime,
          weekStart: selectedWeek,
          mediaItem: mediaItem,
          isTemporary: mediaItem.isTemporary || false,
          episodeNumber: mediaItem.episodeNumber,
          seasonNumber: mediaItem.seasonNumber,
          partNumber: mediaItem.partNumber
        };

        // إضافة المادة للجدول محلياً
        setScheduleItems(prev => [...prev, newScheduleItem]);

        console.log('✅ تم إضافة المادة للجدول محلياً بدون إعادة تحميل');
      } else {
        alert(result.error);
      }
    } catch (error) {
      console.error('خطأ في إضافة المادة:', error);
    }
  };

  // حذف مادة مع تأكيد
  const deleteItem = async (item: ScheduleItem) => {
    const itemName = item.mediaItem?.name || t('schedule.unknown');
    const itemType = item.isRerun ? t('schedule.rerunMaterial') :
                    item.isTemporary ? t('schedule.tempMaterial') : t('schedule.originalMaterial');

    const warningMessage = item.isRerun ? t('schedule.deleteWarningRerun') :
                          item.isTemporary ? t('schedule.deleteWarningTemp') :
                          t('schedule.deleteWarningOriginal');

    const confirmed = window.confirm(
      `${t('schedule.confirmDelete', { type: itemType, name: itemName })}\n\n` +
      `${t('schedule.time')}: ${item.startTime} - ${item.endTime}\n` +
      warningMessage
    );

    if (!confirmed) return;

    try {
      // للمواد المؤقتة - حذف من الخادم أيضًا
      if (item.isTemporary) {
        console.log(`🗑️ بدء حذف المادة المؤقتة: ${itemName} (${item.id})`);
        
        try {
          // حذف المادة المؤقتة من الخادم
          console.log(`🔍 إرسال طلب حذف المادة المؤقتة: ${item.id} (${itemName})`);
          console.log(`📊 بيانات المادة المؤقتة:`, {
            id: item.id,
            mediaItemId: item.mediaItemId,
            dayOfWeek: item.dayOfWeek,
            startTime: item.startTime,
            endTime: item.endTime,
            isTemporary: item.isTemporary,
            isRerun: item.isRerun,
            mediaItemName: item.mediaItem?.name
          });
          
          const response = await fetch('/api/weekly-schedule', {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'deleteTempItem',
              tempItemId: item.id,
              mediaItemId: item.mediaItemId,
              dayOfWeek: item.dayOfWeek,
              startTime: item.startTime,
              endTime: item.endTime,
              weekStart: selectedWeek,
              mediaItemName: item.mediaItem?.name
            })
          });
          
          const result = await response.json();
          
          if (result.success) {
            console.log(`✅ تم حذف المادة المؤقتة من الخادم: ${itemName}`);
            
            // حذف محلي بعد نجاح الحذف من الخادم
            if (item.isRerun) {
              // حذف إعادة مؤقتة فقط
              setScheduleItems(prev => prev.filter(scheduleItem => scheduleItem.id !== item.id));
              console.log(`✅ تم حذف إعادة مؤقتة محليًا: ${itemName}`);
            } else {
              // حذف المادة الأصلية وجميع إعاداتها المؤقتة
              setScheduleItems(prev => prev.filter(scheduleItem =>
                scheduleItem.id !== item.id &&
                !(scheduleItem.isRerun && scheduleItem.originalId === item.id)
              ));
              console.log(`✅ تم حذف المادة المؤقتة وإعاداتها محليًا: ${itemName}`);
            }
            
            // إعادة تحميل البيانات للتأكد من التزامن
            // حفظ الصف المرئي قبل إعادة التحميل
            saveVisibleRowIndex();
            shouldRestoreScroll.current = true;
            console.log(`📍 تم حفظ الصف المرئي قبل إعادة تحميل البيانات`);
            
            await fetchScheduleData();
          } else {
            console.error(`❌ فشل حذف المادة المؤقتة من الخادم: ${result.error}`);
            alert(`فشل حذف المادة المؤقتة: ${result.error}`);
          }
        } catch (error) {
          console.error(`❌ خطأ أثناء حذف المادة المؤقتة: ${error}`);
          alert('حدث خطأ أثناء حذف المادة المؤقتة');
        }
        
        return;
      }

      // حفظ الصف المرئي قبل التحديث
      saveVisibleRowIndex();
      shouldRestoreScroll.current = true;
      console.log(`📍 تم حفظ الصف المرئي عند حذف مادة`);

      // للمواد العادية - استخدام API
      const response = await fetch(`/api/weekly-schedule?id=${item.id}&weekStart=${selectedWeek}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        // حفظ الصف المرئي قبل إعادة التحميل
        saveVisibleRowIndex();
        shouldRestoreScroll.current = true;
        console.log(`📍 تم حفظ الصف المرئي قبل إعادة تحميل البيانات بعد الحذف`);
        
        await fetchScheduleData();
        console.log(`✅ تم حذف ${itemType}: ${itemName}`);
      } else {
        const result = await response.json();
        alert(`خطأ في الحذف: ${result.error}`);
      }
    } catch (error) {
      console.error('خطأ في حذف المادة:', error);
      alert('حدث خطأ أثناء حذف المادة');
    }
  };

  // الحصول على المواد في خلية معينة
  const getItemsForCell = (dayOfWeek: number, hour: string) => {
    return scheduleItems.filter(item => 
      item.dayOfWeek === dayOfWeek && 
      item.startTime <= hour && 
      item.endTime > hour
    );
  };

  // معالجة السحب والإفلات
  const handleDragStart = (e: React.DragEvent, mediaItem: MediaItem) => {
    console.log('🖱️ بدء السحب:', {
      id: mediaItem.id,
      name: mediaItem.name,
      isTemporary: mediaItem.isTemporary,
      type: mediaItem.type,
      duration: mediaItem.duration,
      fullItem: mediaItem
    });

    // التأكد من أن جميع البيانات موجودة
    const itemToSet = {
      ...mediaItem,
      name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',
      id: mediaItem.id || `temp_${Date.now()}`
    };

    console.log('📦 المادة المحفوظة للسحب:', itemToSet);
    setDraggedItem(itemToSet);
  };

  // سحب مادة من الجدول نفسه (نسخ)
  const handleScheduleItemDragStart = (e: React.DragEvent, scheduleItem: any) => {
    // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء
    const itemToCopy = {
      ...scheduleItem.mediaItem,
      // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول
      episodeNumber: scheduleItem.episodeNumber || scheduleItem.mediaItem?.episodeNumber,
      seasonNumber: scheduleItem.seasonNumber || scheduleItem.mediaItem?.seasonNumber,
      partNumber: scheduleItem.partNumber || scheduleItem.mediaItem?.partNumber,
      isFromSchedule: true,
      originalScheduleItem: scheduleItem
    };
    setDraggedItem(itemToCopy);
    console.log('🔄 سحب مادة من الجدول:', scheduleItem.mediaItem?.name);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent, dayOfWeek: number, hour: string) => {
    e.preventDefault();
    console.log('📍 إفلات في:', { dayOfWeek, hour });

    if (draggedItem) {
      console.log('📦 المادة المسحوبة:', {
        id: draggedItem.id,
        name: draggedItem.name,
        isTemporary: draggedItem.isTemporary,
        type: draggedItem.type,
        fullItem: draggedItem
      });

      // التأكد من أن البيانات سليمة قبل الإرسال
      if (!draggedItem.name || draggedItem.name === 'undefined') {
        console.error('⚠️ اسم المادة غير صحيح:', draggedItem);
        alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');
        setDraggedItem(null);
        return;
      }

      addItemToSchedule(draggedItem, dayOfWeek, hour);
      setDraggedItem(null);
    } else {
      console.log('❌ لا توجد مادة مسحوبة');
    }
  };

  // تغيير الأسبوع
  const changeWeek = (direction: number) => {
    if (!selectedWeek) return;

    const currentDate = new Date(selectedWeek + 'T00:00:00');
    console.log('📅 تغيير الأسبوع - البداية:', {
      direction: direction > 0 ? 'التالي' : 'السابق',
      currentWeek: selectedWeek,
      currentDayOfWeek: currentDate.getDay()
    });

    // إضافة أو طرح 7 أيام
    currentDate.setDate(currentDate.getDate() + (direction * 7));

    const newWeekStart = currentDate.toISOString().split('T')[0];
    console.log('📅 تغيير الأسبوع - النتيجة:', {
      newWeek: newWeekStart,
      newDayOfWeek: currentDate.getDay()
    });

    setSelectedWeek(newWeekStart);
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <div style={{ fontSize: '1.5rem' }}>⏳ {t('schedule.loadingSchedule')}</div>
      </div>
    );
  }

  // إذا لم يتم تحديد الأسبوع بعد
  if (!selectedWeek) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <div style={{ fontSize: '1.5rem' }}>📅 {t('schedule.selectingDate')}</div>
      </div>
    );
  }

  return (
    <AuthGuard requiredPermissions={['SCHEDULE_READ']}>
      <DashboardLayout title={t('schedule.weeklySchedule')} subtitle={t('schedule.weeklySubtitle')} icon="📅" fullWidth={true}>
        <div style={{
          display: 'flex',
          height: 'calc(100vh - 120px)',
          fontFamily: 'Arial, sans-serif',
          direction: isRTL ? 'rtl' : 'ltr',
          gap: '20px'
        }}>
          {/* قائمة المواد - اليمين */}
          <div style={{
            width: '320px',
            background: '#4a5568',
            borderRadius: '15px',
            border: '1px solid #6b7280',
            padding: '20px',
            overflowY: 'auto'
          }}>
            <h3 style={{ margin: '0 0 15px 0', color: '#f3f4f6' }}>📚 {t('schedule.mediaList')}</h3>

            {/* نموذج إضافة مادة مؤقتة */}
            <div style={{
              background: '#1f2937',
              border: '2px solid #f59e0b',
              borderRadius: '8px',
              padding: '12px',
              marginBottom: '15px'
            }}>
              <h4 style={{ margin: '0 0 10px 0', color: '#fbbf24', fontSize: '0.9rem' }}>
                ⚡ {t('schedule.addTempMedia')}
              </h4>

              <input
                type="text"
                placeholder={t('schedule.mediaName')}
                value={tempMediaName}
                onChange={(e) => setTempMediaName(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #6b7280',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  fontSize: '13px',
                  color: '#333',
                  background: 'white'
                }}
              />

              <select
                value={tempMediaType}
                onChange={(e) => setTempMediaType(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #6b7280',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  fontSize: '13px',
                  color: '#333',
                  background: 'white'
                }}
              >
                <option value="PROGRAM">📻 Program</option>
                <option value="SERIES">📺 Series</option>
                <option value="FILM">🎥 Film</option>
                <option value="FILLER">⏸️ Filler</option>
                <option value="STING">⚡ Sting</option>
                <option value="PROMO">📢 Promo</option>
                <option value="NEXT">▶️ Next</option>
                <option value="NOW">🔴 Now</option>
                <option value="سنعود">⏰ سنعود</option>
                <option value="عدنا">✅ عدنا</option>
                <option value="MINI">🔸 Mini</option>
                <option value="CROSS">✖️ Cross</option>
                <option value="LIVE">🔴 برنامج هواء مباشر</option>
                <option value="PENDING">🟡 مادة قيد التسليم</option>
              </select>

              <input
                type="text"
                placeholder={t('schedule.duration')}
                value={tempMediaDuration}
                onChange={(e) => setTempMediaDuration(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #6b7280',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  fontSize: '13px',
                  color: '#333',
                  background: 'white'
                }}
              />

              <input
                type="text"
                placeholder={t('schedule.notes')}
                value={tempMediaNotes}
                onChange={(e) => setTempMediaNotes(e.target.value)}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #6b7280',
                  borderRadius: '4px',
                  marginBottom: '10px',
                  fontSize: '13px',
                  color: '#333',
                  background: 'white'
                }}
              />

              <button
                onClick={addTempMedia}
                style={{
                  width: '100%',
                  padding: '8px',
                  background: '#ff9800',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  fontSize: '13px',
                  fontWeight: 'bold',
                  cursor: 'pointer',
                  marginBottom: '8px'
                }}
              >
                ➕ {t('schedule.add')}
              </button>

              <button
                onClick={async () => {
                  console.log('🔄 تحديث الإعادات...');
                  scrollPositionRef.current = window.scrollY;
                  shouldRestoreScroll.current = true;
                  await fetchScheduleData();
                }}
                style={{
                  width: '100%',
                  padding: '6px',
                  background: '#4caf50',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  fontSize: '12px',
                  cursor: 'pointer'
                }}
              >
                ♻️ {t('schedule.updateReruns')}
              </button>
            </div>

            {/* فلتر نوع المادة */}
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #6b7280',
                borderRadius: '5px',
                marginBottom: '10px',
                fontSize: '14px',
                backgroundColor: 'white',
                color: '#333'
              }}
            >
              <option value="">🎬 {t('schedule.allTypes')}</option>
              <option value="SERIES">📺 Series</option>
              <option value="FILM">🎥 Film</option>
              <option value="PROGRAM">📻 Program</option>
              <option value="SONG">🎵 Song</option>
              <option value="PROMO">📢 Promo</option>
              <option value="STING">⚡ Sting</option>
              <option value="FILLER">⏸️ Filler</option>
              <option value="NEXT">▶️ Next</option>
              <option value="NOW">🔴 Now</option>
              <option value="سنعود">⏰ سنعود</option>
              <option value="عدنا">✅ عدنا</option>
              <option value="MINI">🔸 Mini</option>
              <option value="CROSS">✖️ Cross</option>
            </select>

            {/* البحث */}
            <input
              type="text"
              placeholder={`🔍 ${t('schedule.searchMedia')}`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '10px',
                border: '1px solid #6b7280',
                borderRadius: '5px',
                marginBottom: '15px',
                fontSize: '14px',
                color: '#333',
                background: 'white'
              }}
            />

            {/* عداد النتائج */}
            <div style={{
              fontSize: '12px',
              color: '#d1d5db',
              marginBottom: '10px',
              textAlign: 'center',
              padding: '5px',
              background: '#1f2937',
              borderRadius: '4px',
              border: '1px solid #6b7280'
            }}>
              📊 {t('schedule.resultsCount', { count: filteredMedia.length, total: allAvailableMedia.length })}
            </div>

            {/* قائمة المواد */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {filteredMedia.length === 0 ? (
                <div style={{
                  textAlign: 'center',
                  padding: '20px',
                  color: '#666',
                  background: '#f8f9fa',
                  borderRadius: '8px',
                  border: '2px dashed #dee2e6'
                }}>
                  <div style={{ fontSize: '2rem', marginBottom: '10px' }}>🔍</div>
                  <div style={{ fontWeight: 'bold', marginBottom: '5px' }}>{t('schedule.noMedia')}</div>
                  <div style={{ fontSize: '12px' }}>
                    {searchTerm || selectedType ? t('schedule.changeFilter') : t('schedule.addNewMedia')}
                  </div>
                </div>
              ) : (
                filteredMedia.map(item => {
                  // تحديد لون المادة حسب النوع
                  const getItemStyle = () => {
                    if (item.isTemporary) {
                      switch (item.type) {
                        case 'LIVE':
                          return {
                            background: '#ffebee',
                            border: '2px solid #f44336',
                            borderLeft: '5px solid #f44336'
                          };
                        case 'PENDING':
                          return {
                            background: '#fff8e1',
                            border: '2px solid #ffc107',
                            borderLeft: '5px solid #ffc107'
                          };
                        default:
                          return {
                            background: '#f3e5f5',
                            border: '2px solid #9c27b0',
                            borderLeft: '5px solid #9c27b0'
                          };
                      }
                    }
                    return {
                      background: '#fff',
                      border: '1px solid #ddd'
                    };
                  };

                  return (
                    <div
                      key={item.id}
                      draggable
                      onDragStart={(e) => handleDragStart(e, item)}
                      style={{
                        ...getItemStyle(),
                        borderRadius: '8px',
                        padding: '12px',
                        cursor: 'grab',
                        transition: 'all 0.2s',
                        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
                      }}
                    >
                      {/* زر حذف للمواد المؤقتة */}
                      {item.isTemporary && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteTempMedia(item.id);
                          }}
                          style={{
                            position: 'absolute',
                            top: '5px',
                            left: '5px',
                            background: '#f44336',
                            color: 'white',
                            border: 'none',
                            borderRadius: '50%',
                            width: '20px',
                            height: '20px',
                            fontSize: '12px',
                            cursor: 'pointer',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                          title={t('schedule.confirmDeleteTemp')}
                        >
                          ×
                        </button>
                      )}

                      <div style={{ fontWeight: 'bold', color: '#333', marginBottom: '4px' }}>
                        {item.isTemporary && (
                          <span style={{
                            fontSize: '10px',
                            background: item.type === 'LIVE' ? '#f44336' :
                                       item.type === 'PENDING' ? '#ffc107' : '#9c27b0',
                            color: 'white',
                            padding: '2px 6px',
                            borderRadius: '10px',
                            marginLeft: '5px'
                          }}>
                            {item.type === 'LIVE' ? `🔴 ${t('schedule.liveProgram')}` :
                             item.type === 'PENDING' ? `🟡 ${t('schedule.pendingDelivery')}` : `🟣 ${t('schedule.temporary')}`}
                          </span>
                        )}
                        {getMediaDisplayText(item)}
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {item.type} • {item.duration}
                      </div>
                      {item.description && (
                        <div style={{ fontSize: '11px', color: '#888', marginTop: '4px' }}>
                          {item.description}
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </div>
          </div>

          {/* الجدول الرئيسي - اليسار */}
          <div style={{ flex: 1, overflowY: 'auto' }}>
            {/* العنوان والتحكم في التاريخ */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px',
              background: '#4a5568',
              padding: '15px',
              borderRadius: '15px',
              border: '1px solid #6b7280'
            }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => window.location.href = '/daily-schedule'}
              style={{
                background: 'linear-gradient(45deg, #007bff, #0056b3)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '10px 20px',
                fontSize: '0.9rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
                transition: 'transform 0.2s ease',
                marginLeft: '10px'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              📋 {t('schedule.broadcastSchedule')}
            </button>





            <button
              onClick={async () => {
                try {
                  console.log('📊 بدء تصدير الخريطة الأسبوعية...');
                  const response = await fetch(`/api/export-schedule?weekStart=${selectedWeek}`);

                  if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                  }

                  const blob = await response.blob();
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `Weekly_Schedule_${selectedWeek}.xlsx`;
                  document.body.appendChild(a);
                  a.click();
                  window.URL.revokeObjectURL(url);
                  document.body.removeChild(a);

                  console.log('✅ تم تصدير الخريطة بنجاح');
                } catch (error) {
                  console.error('❌ خطأ في تصدير الخريطة:', error);
                  alert('فشل في تصدير الخريطة: ' + error.message);
                }
              }}
              style={{
                background: 'linear-gradient(45deg, #28a745, #20c997)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                padding: '10px 20px',
                fontSize: '0.9rem',
                fontWeight: 'bold',
                cursor: 'pointer',
                boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
                transition: 'transform 0.2s ease',
                marginLeft: '10px'
              }}
              onMouseEnter={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
              onMouseLeave={(e) => e.currentTarget.style.transform = 'translateY(0)'}
            >
              📊 {t('schedule.exportSchedule')}
            </button>



            <h2 style={{ margin: 0, color: '#f3f4f6', fontSize: '1.2rem' }}>📅 {t('schedule.selectedWeek')}</h2>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => changeWeek(-1)}
              style={{
                padding: '8px 15px',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              ← {t('schedule.previousWeek')}
            </button>

            <input
              type="date"
              value={selectedWeek}
              onChange={(e) => {
                const selectedDate = new Date(e.target.value + 'T00:00:00');
                const dayOfWeek = selectedDate.getDay();

                console.log('📅 تغيير التاريخ من التقويم:', {
                  selectedDate: e.target.value,
                  dayOfWeek: dayOfWeek,
                  dayName: ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'][dayOfWeek]
                });

                // حساب بداية الأسبوع (الأحد)
                const sunday = new Date(selectedDate);
                sunday.setHours(0, 0, 0, 0); // تصفير الوقت
                sunday.setDate(selectedDate.getDate() - dayOfWeek);
                const weekStart = sunday.toISOString().split('T')[0];

                console.log('📅 بداية الأسبوع المحسوبة:', weekStart);
                console.log('📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');

                setSelectedWeek(weekStart);
              }}
              style={{
                padding: '8px',
                border: '1px solid #6b7280',
                borderRadius: '5px',
                color: '#333',
                background: 'white'
              }}
            />

            <button
              onClick={() => changeWeek(1)}
              style={{
                padding: '8px 15px',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '5px',
                cursor: 'pointer'
              }}
            >
              {t('schedule.nextWeek')} →
            </button>
          </div>
        </div>

            {/* الجدول */}
            <div 
              ref={scheduleTableRef}
              style={{
              background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',
              borderRadius: '10px',
              overflow: 'hidden',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              minHeight: '80vh'
            }}>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            {/* رأس الجدول */}
            <thead>
              <tr style={{ background: '#f8f9fa' }}>
                <th style={{
                  padding: '12px',
                  border: '1px solid #dee2e6',
                  fontWeight: 'bold',
                  width: '80px',
                  color: '#000'
                }}>
                  {t('schedule.time')}
                </th>
                {days.map((day, index) => (
                  <th key={index} style={{
                    padding: '12px',
                    border: '1px solid #dee2e6',
                    fontWeight: 'bold',
                    width: `${100/7}%`,
                    textAlign: 'center'
                  }}>
                    <div style={{ fontSize: '1rem', marginBottom: '4px', color: '#000', fontWeight: 'bold' }}>{day}</div>
                    <div style={{ fontSize: '1rem', color: '#000', fontWeight: 'bold' }}>
                      {weekDates[index]}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>

            {/* جسم الجدول */}
            <tbody>
              {hours.map((hour, hourIndex) => (
                <tr key={hourIndex} className="hour-row">
                  <td style={{
                    background: '#f8f9fa',
                    fontWeight: 'bold',
                    textAlign: 'center',
                    padding: '8px',
                    border: '1px solid #dee2e6',
                    color: '#000'
                  }}>
                    {hour}
                  </td>
                  
                  {days.map((_, dayIndex) => {
                    const cellItems = getItemsForCell(dayIndex, hour);
                    return (
                      <td
                        key={dayIndex}
                        onDragOver={handleDragOver}
                        onDrop={(e) => handleDrop(e, dayIndex, hour)}
                        style={{
                          border: '1px solid #dee2e6',
                          padding: '8px',
                          height: '150px',
                          cursor: 'pointer',
                          background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',
                          verticalAlign: 'top'
                        }}
                      >
                        {cellItems.map(item => (
                          <div
                            key={item.id}
                            draggable
                            onDragStart={(e) => handleScheduleItemDragStart(e, item)}
                            onClick={() => deleteItem(item)}
                            style={{
                              background: item.isRerun ? '#f0f0f0' :
                                         item.isTemporary ? (
                                           item.mediaItem?.type === 'LIVE' ? '#ffebee' :
                                           item.mediaItem?.type === 'PENDING' ? '#fff8e1' : '#f3e5f5'
                                         ) : '#fff3e0',
                              border: `2px solid ${
                                item.isRerun ? '#888888' :
                                item.isTemporary ? (
                                  item.mediaItem?.type === 'LIVE' ? '#f44336' :
                                  item.mediaItem?.type === 'PENDING' ? '#ffc107' : '#9c27b0'
                                ) : '#ff9800'
                              }`,
                              borderRadius: '4px',
                              padding: '8px 6px',
                              marginBottom: '4px',
                              fontSize: '1rem',
                              cursor: 'grab',
                              transition: 'all 0.2s ease',
                              minHeight: '60px',
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'center'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.transform = 'scale(1.02)';
                              e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.transform = 'scale(1)';
                              e.currentTarget.style.boxShadow = 'none';
                            }}
                          >
                            <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '4px', color: '#000' }}>
                              {item.isRerun ? (
                                <span style={{ color: '#666' }}>
                                  ♻️ {item.rerunPart ? `ج${item.rerunPart}` : ''}{item.rerunCycle ? `(${item.rerunCycle})` : ''}
                                </span>
                              ) : item.isTemporary ? (
                                <span style={{
                                  color: item.mediaItem?.type === 'LIVE' ? '#f44336' :
                                         item.mediaItem?.type === 'PENDING' ? '#ffc107' : '#9c27b0'
                                }}>
                                  {item.mediaItem?.type === 'LIVE' ? '🔴' :
                                   item.mediaItem?.type === 'PENDING' ? '🟡' : '🟣'}
                                </span>
                              ) : (
                                <span style={{ color: '#ff9800' }}>🌟</span>
                              )}
                              <span style={{ color: '#000' }}>
                                {item.mediaItem ? getMediaDisplayText(item.mediaItem) : t('schedule.unknown')}
                              </span>
                            </div>
                            <div style={{ fontSize: '0.8rem', color: '#000', marginTop: '4px' }}>
                              {item.startTime} - {item.endTime}
                            </div>
                            {item.isRerun && (
                              <div style={{ fontSize: '0.5rem', color: '#888', fontStyle: 'italic' }}>
                                {t('schedule.rerunIndicator')}
                              </div>
                            )}
                          </div>
                        ))}
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
            </div>

            {/* تعليمات */}
            <div style={{
              background: '#4a5568',
              padding: '20px',
              borderRadius: '15px',
              marginTop: '20px',
              border: '1px solid #6b7280'
            }}>
          <h4 style={{ color: '#f3f4f6', margin: '0 0 20px 0', fontSize: '1.3rem', textAlign: 'center' }}>{t('schedule.usageInstructions')}</h4>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px', marginBottom: '20px' }}>
            <div>
              <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>{t('schedule.addMediaTitle')}</strong>
              <ul style={{ margin: '10px 0', paddingRight: '20px', fontSize: '1rem', color: '#d1d5db' }}>
                <li style={{ marginBottom: '8px' }}>{t('schedule.addMediaInstruction1')}</li>
                <li style={{ marginBottom: '8px' }}>{t('schedule.addMediaInstruction2')}</li>
                <li style={{ marginBottom: '8px' }}>{t('schedule.addMediaInstruction3')}</li>
                <li style={{ marginBottom: '8px' }}>{t('schedule.addMediaInstruction4')}</li>
              </ul>
            </div>
            <div>
              <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>{t('schedule.deleteMediaTitle')}</strong>
              <ul style={{ margin: '10px 0', paddingRight: '20px', fontSize: '1rem', color: '#d1d5db' }}>
                <li style={{ marginBottom: '8px' }}><strong>{t('schedule.originalMaterial')}:</strong> {t('schedule.deleteOriginalInfo')}</li>
                <li style={{ marginBottom: '8px' }}><strong>{t('schedule.rerunMaterial')}:</strong> {t('schedule.deleteRerunInfo')}</li>
                <li style={{ marginBottom: '8px' }}>{t('schedule.deleteConfirmInfo')}</li>
              </ul>
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
            <div>
              <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>{t('schedule.primeTimeTitle')}</strong>
              <ul style={{ margin: '10px 0', paddingRight: '20px', fontSize: '1rem', color: '#d1d5db' }}>
                <li style={{ marginBottom: '8px' }}>{t('schedule.primeTimeSchedule1')}</li>
                <li style={{ marginBottom: '8px' }}>{t('schedule.primeTimeSchedule2')}</li>
                <li style={{ marginBottom: '8px' }}><strong style={{ color: '#fbbf24' }}>{t('schedule.primeTimeColor')}</strong></li>
              </ul>
            </div>
            <div>
              <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>{t('schedule.rerunsTitle')}</strong>
              <ul style={{ margin: '10px 0', paddingRight: '20px', fontSize: '1rem', color: '#d1d5db' }}>
                <li style={{ marginBottom: '8px' }}><strong>{t('schedule.rerunsSchedule1')}</strong></li>
                <ul style={{ margin: '5px 0', paddingRight: '15px', fontSize: '0.9rem' }}>
                  <li style={{ marginBottom: '5px' }}>{t('schedule.rerunsPart1Sun')}</li>
                  <li style={{ marginBottom: '5px' }}>{t('schedule.rerunsPart2Sun')}</li>
                </ul>
                <li style={{ marginBottom: '8px' }}><strong>{t('schedule.rerunsSchedule2')}</strong></li>
                <ul style={{ margin: '5px 0', paddingRight: '15px', fontSize: '0.9rem' }}>
                  <li style={{ marginBottom: '5px' }}>{t('schedule.rerunsPart1Thu')}</li>
                  <li style={{ marginBottom: '5px' }}>{t('schedule.rerunsPart2Thu')}</li>
                </ul>
                <li style={{ marginBottom: '8px' }}><strong style={{ color: '#9ca3af' }}>{t('schedule.rerunsColor')}</strong></li>
              </ul>
            </div>
          </div>

          <div style={{ marginTop: '15px', padding: '15px', background: '#1f2937', borderRadius: '10px', border: '1px solid #f59e0b' }}>
            <strong style={{ color: '#fbbf24', fontSize: '1.1rem' }}>{t('schedule.dateManagementTitle')}</strong>
            <span style={{ color: '#d1d5db', fontSize: '1rem' }}> {t('schedule.dateManagementInfo')}</span>
          </div>

          <div style={{ marginTop: '15px', padding: '15px', background: '#1f2937', borderRadius: '10px', border: '1px solid #3b82f6' }}>
            <strong style={{ color: '#60a5fa', fontSize: '1.1rem' }}>{t('schedule.importantNoteTitle')}</strong>
            <span style={{ color: '#d1d5db', fontSize: '1rem' }}> {t('schedule.importantNoteInfo')}</span>
          </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </AuthGuard>
  );
}
