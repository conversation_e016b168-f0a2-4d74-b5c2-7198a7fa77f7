"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NavigationCard */ \"(app-pages-browser)/./src/components/NavigationCard.tsx\");\n/* harmony import */ var _components_Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _styles_dashboard_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/dashboard.css */ \"(app-pages-browser)/./src/styles/dashboard.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t, isRTL, currentLang, changeLanguage, isReady } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__.useAppTranslation)();\n    // تعريف عناصر التنقل في القائمة العلوية\n    const topNavigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            active: false,\n            path: '/reports'\n        },\n        ...(user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && (user === null || user === void 0 ? void 0 : user.username) === 'admin' ? [\n            {\n                name: t('navigation.unifiedSystem'),\n                icon: '📤',\n                active: false,\n                path: '/unified-system'\n            }\n        ] : [],\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard'\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics'\n        }\n    ];\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Dashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Dashboard.useEffect.timer\"], 1000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // بيانات الإحصائيات الحقيقية\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMedia: 0,\n        validMedia: 0,\n        rejectedMedia: 0,\n        expiredMedia: 0,\n        pendingMedia: 0,\n        activeUsers: 0,\n        onlineUsers: 0,\n        todayAdded: 0\n    });\n    // حالة تنبيه المواد المنتهية\n    const [expiredAlertShown, setExpiredAlertShown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تخزين المواد المنتهية للتنبيه\n    const [expiredMediaItems, setExpiredMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showExpiredAlert, setShowExpiredAlert] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // جلب البيانات الحقيقية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchRealStats();\n            // تحديث البيانات كل 30 ثانية\n            const interval = setInterval(fetchRealStats, 30000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(interval)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchRealStats = async ()=>{\n        try {\n            setLoading(true);\n            // جلب البيانات من API مع timeout أطول\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 15000); // 15 ثانية timeout\n            const [mediaResponse, usersResponse] = await Promise.all([\n                fetch('/api/media', {\n                    signal: controller.signal,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                }),\n                fetch('/api/users', {\n                    signal: controller.signal,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                })\n            ]);\n            clearTimeout(timeoutId);\n            let mediaData = [];\n            let userData = [];\n            if (mediaResponse.ok) {\n                try {\n                    const mediaResult = await mediaResponse.json();\n                    mediaData = mediaResult.success ? mediaResult.data : [];\n                } catch (e) {\n                    console.warn('Failed to parse media response:', e);\n                }\n            }\n            if (usersResponse.ok) {\n                try {\n                    const usersResult = await usersResponse.json();\n                    userData = usersResult.success ? usersResult.users : [];\n                } catch (e) {\n                    console.warn('Failed to parse users response:', e);\n                }\n            }\n            // حساب الإحصائيات الحقيقية\n            const totalMedia = mediaData.length;\n            const validMedia = mediaData.filter((item)=>item.status === 'VALID').length;\n            const rejectedMedia = mediaData.filter((item)=>item.status === 'REJECTED_CENSORSHIP' || item.status === 'REJECTED_TECHNICAL').length;\n            // التحقق من المواد المنتهية حسب التاريخ\n            const today = new Date();\n            const expiredByDateItems = mediaData.filter((item)=>item.endDate && new Date(item.endDate) < today);\n            // المواد المنتهية (إما بالحالة أو بالتاريخ)\n            const expiredStatusItems = mediaData.filter((item)=>item.status === 'EXPIRED');\n            const allExpiredItems = [\n                ...new Set([\n                    ...expiredByDateItems,\n                    ...expiredStatusItems\n                ])\n            ];\n            const expiredMedia = allExpiredItems.length;\n            // تحديث قائمة المواد المنتهية للتنبيه\n            setExpiredMediaItems(allExpiredItems);\n            // إظهار التنبيه مرة واحدة فقط في الجلسة\n            const alertKey = \"expired_alert_\".concat(new Date().toDateString());\n            const alertShownToday = localStorage.getItem(alertKey);\n            if (allExpiredItems.length > 0 && !alertShownToday && !expiredAlertShown) {\n                setShowExpiredAlert(true);\n                setExpiredAlertShown(true);\n                localStorage.setItem(alertKey, 'true');\n                console.log(\"⚠️ تم العثور على \".concat(allExpiredItems.length, \" مادة منتهية - عرض التنبيه\"));\n            } else {\n                setShowExpiredAlert(false);\n            }\n            const pendingMedia = mediaData.filter((item)=>item.status === 'PENDING').length;\n            // حساب المواد المضافة اليوم\n            const todayStr = new Date().toDateString();\n            const todayAdded = mediaData.filter((item)=>{\n                if (!item.createdAt) return false;\n                return new Date(item.createdAt).toDateString() === todayStr;\n            }).length;\n            setStats({\n                totalMedia,\n                validMedia,\n                rejectedMedia,\n                expiredMedia,\n                pendingMedia,\n                activeUsers: userData.length,\n                onlineUsers: userData.filter((u)=>u.isActive).length,\n                todayAdded\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // التحقق من نوع الخطأ\n            if (error instanceof Error && error.name === 'AbortError') {\n                console.log('Request was aborted due to timeout');\n            }\n            // بيانات افتراضية في حالة الخطأ\n            setStats({\n                totalMedia: 0,\n                validMedia: 0,\n                rejectedMedia: 0,\n                expiredMedia: 0,\n                pendingMedia: 0,\n                activeUsers: 4,\n                onlineUsers: 1,\n                todayAdded: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const navigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    color: 'white'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            marginBottom: '30px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: \"large\",\n                            style: {\n                                fontSize: '2rem'\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '3rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.5rem',\n                            marginBottom: '10px'\n                        },\n                        children: t('common.loadingData')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#a0aec0',\n                            fontSize: '1rem'\n                        },\n                        children: t('messages.pleaseWait')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: isRTL ? 'rtl' : 'ltr'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                showExpiredAlert && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: '20px',\n                        left: '20px',\n                        right: '20px',\n                        zIndex: 1000,\n                        background: '#ef4444',\n                        color: 'white',\n                        padding: '15px 20px',\n                        borderRadius: '8px',\n                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        maxHeight: '300px',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontWeight: 'bold',\n                                        fontSize: '1.1rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: [\n                                        \"⚠️ \",\n                                        t('messages.warning'),\n                                        \": \",\n                                        t('stats.expiredMedia'),\n                                        \" \",\n                                        expiredMediaItems.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            paddingRight: '20px',\n                                            margin: '5px 0'\n                                        },\n                                        children: [\n                                            expiredMediaItems.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        item.name,\n                                                        \" - \",\n                                                        item.endDate ? \"منتهية بتاريخ: \".concat(new Date(item.endDate).toLocaleDateString('ar-EG')) : 'منتهية'\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            expiredMediaItems.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"... والمزيد (\",\n                                                    expiredMediaItems.length - 5,\n                                                    \" مادة أخرى)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setShowExpiredAlert(false);\n                                setExpiredAlertShown(true);\n                                // حفظ في localStorage لمنع ظهور التنبيه مرة أخرى اليوم\n                                const alertKey = \"expired_alert_\".concat(new Date().toDateString());\n                                localStorage.setItem(alertKey, 'true');\n                                console.log('🔕 تم إغلاق تنبيه المواد المنتهية نهائياً لليوم');\n                            },\n                            style: {\n                                background: 'rgba(255, 255, 255, 0.2)',\n                                border: 'none',\n                                color: 'white',\n                                borderRadius: '4px',\n                                padding: '5px 10px',\n                                cursor: 'pointer',\n                                marginRight: '10px'\n                            },\n                            children: t('common.close')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: topNavigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const newLang = currentLang === 'ar' ? 'en' : 'ar';\n                                        i18n.changeLanguage(newLang);\n                                    },\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '5px'\n                                    },\n                                    title: currentLang === 'ar' ? 'Switch to English' : 'التبديل للعربية',\n                                    children: [\n                                        \"\\uD83C\\uDF10\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: currentLang === 'ar' ? 'EN' : 'عر'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    title: t('auth.logout'),\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        ...isRTL ? {\n                            marginRight: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-right 0.3s ease'\n                        } : {\n                            marginLeft: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-left 0.3s ease'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: t('dashboard.title')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: t('dashboard.subtitle')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#68d391',\n                                                        margin: '5px 0 0 0',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        t('dashboard.overview'),\n                                                        \": \",\n                                                        stats.totalMedia,\n                                                        \" \",\n                                                        t('stats.totalMedia'),\n                                                        \" - \",\n                                                        stats.activeUsers,\n                                                        \" \",\n                                                        t('stats.activeUsers')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: t('common.loading')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"المزامنة: \",\n                                                        currentTime.toLocaleTimeString('ar-EG')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83C\\uDFAC\",\n                                    title: t('navigation.mediaList'),\n                                    subtitle: t('media.list'),\n                                    path: \"/media-list\",\n                                    permission: \"MEDIA_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"➕\",\n                                    title: t('navigation.addMedia'),\n                                    subtitle: t('media.addNew'),\n                                    path: \"/add-media\",\n                                    permission: \"MEDIA_CREATE\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    title: t('navigation.weeklySchedule'),\n                                    subtitle: t('schedule.weekly'),\n                                    path: \"/weekly-schedule\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCA\",\n                                    title: t('navigation.dailySchedule'),\n                                    subtitle: t('schedule.daily'),\n                                    path: \"/daily-schedule\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCB\",\n                                    title: t('navigation.reports'),\n                                    subtitle: t('dashboard.recentActivity'),\n                                    path: \"/reports\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 13\n                                }, this),\n                                (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && (user === null || user === void 0 ? void 0 : user.username) === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCE4\",\n                                    title: t('navigation.unifiedSystem'),\n                                    subtitle: t('common.import') + '/' + t('common.export'),\n                                    path: \"/unified-system\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDC65\",\n                                    title: t('navigation.adminDashboard'),\n                                    subtitle: t('admin.users'),\n                                    path: \"/admin-dashboard\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC8\",\n                                    title: t('navigation.statistics'),\n                                    subtitle: t('dashboard.statistics'),\n                                    path: \"/statistics\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"kmUDrC2dAYColdOdCTK9uCIytcc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__.useAppTranslation\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDQTtBQUNnQjtBQUNqQjtBQUVjO0FBQ3BCO0FBQ3lCO0FBQzlCO0FBRWpCLFNBQVNTOztJQUN0QixNQUFNQyxTQUFTUiwwREFBU0E7SUFDeEIsTUFBTSxFQUFFUyxJQUFJLEVBQUVDLE1BQU0sRUFBRUMsYUFBYSxFQUFFLEdBQUdULDhEQUFPQTtJQUMvQyxNQUFNLENBQUNVLGFBQWFDLGVBQWUsR0FBR2YsK0NBQVFBLENBQUMsSUFBSWdCO0lBQ25ELE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHbEIsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxFQUFFbUIsQ0FBQyxFQUFFQyxLQUFLLEVBQUVDLFdBQVcsRUFBRUMsY0FBYyxFQUFFQyxPQUFPLEVBQUUsR0FBR2YsMkVBQWlCQTtJQUU1RSx3Q0FBd0M7SUFDeEMsTUFBTWdCLHFCQUFxQjtRQUN6QjtZQUFFQyxNQUFNTixFQUFFO1lBQXlCTyxNQUFNO1lBQU1DLFFBQVE7WUFBTUMsTUFBTTtRQUFhO1FBQ2hGO1lBQUVILE1BQU1OLEVBQUU7WUFBeUJPLE1BQU07WUFBTUMsUUFBUTtZQUFPQyxNQUFNO1FBQWM7UUFDbEY7WUFBRUgsTUFBTU4sRUFBRTtZQUF3Qk8sTUFBTTtZQUFLQyxRQUFRO1lBQU9DLE1BQU07UUFBYTtRQUMvRTtZQUFFSCxNQUFNTixFQUFFO1lBQThCTyxNQUFNO1lBQU1DLFFBQVE7WUFBT0MsTUFBTTtRQUFtQjtRQUM1RjtZQUFFSCxNQUFNTixFQUFFO1lBQTZCTyxNQUFNO1lBQU1DLFFBQVE7WUFBT0MsTUFBTTtRQUFrQjtRQUMxRjtZQUFFSCxNQUFNTixFQUFFO1lBQXVCTyxNQUFNO1lBQU1DLFFBQVE7WUFBT0MsTUFBTTtRQUFXO1dBQ3pFakIsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNa0IsSUFBSSxNQUFLLFdBQVdsQixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1tQixRQUFRLE1BQUssVUFBVTtZQUFDO2dCQUFFTCxNQUFNTixFQUFFO2dCQUE2Qk8sTUFBTTtnQkFBTUMsUUFBUTtnQkFBT0MsTUFBTTtZQUFrQjtTQUFFLEdBQUcsRUFBRTtRQUM3SjtZQUFFSCxNQUFNTixFQUFFO1lBQThCTyxNQUFNO1lBQU1DLFFBQVE7WUFBT0MsTUFBTTtRQUFtQjtRQUM1RjtZQUFFSCxNQUFNTixFQUFFO1lBQTBCTyxNQUFNO1lBQU1DLFFBQVE7WUFBT0MsTUFBTTtRQUFjO0tBQ3BGO0lBRUQsdUJBQXVCO0lBQ3ZCM0IsZ0RBQVNBOytCQUFDO1lBQ1IsTUFBTThCLFFBQVFDOzZDQUFZO29CQUN4QmpCLGVBQWUsSUFBSUM7Z0JBQ3JCOzRDQUFHO1lBRUg7dUNBQU8sSUFBTWlCLGNBQWNGOztRQUM3Qjs4QkFBRyxFQUFFO0lBRUwsNkJBQTZCO0lBQzdCLE1BQU0sQ0FBQ0csT0FBT0MsU0FBUyxHQUFHbkMsK0NBQVFBLENBQUM7UUFDakNvQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLGNBQWM7UUFDZEMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFlBQVk7SUFDZDtJQUVBLDZCQUE2QjtJQUM3QixNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUUzRCxNQUFNLENBQUM4QyxTQUFTQyxXQUFXLEdBQUcvQywrQ0FBUUEsQ0FBQztJQUV2QyxnQ0FBZ0M7SUFDaEMsTUFBTSxDQUFDZ0QsbUJBQW1CQyxxQkFBcUIsR0FBR2pELCtDQUFRQSxDQUFDLEVBQUU7SUFDN0QsTUFBTSxDQUFDa0Qsa0JBQWtCQyxvQkFBb0IsR0FBR25ELCtDQUFRQSxDQUFDO0lBRXpELHdCQUF3QjtJQUN4QkMsZ0RBQVNBOytCQUFDO1lBQ1JtRDtZQUNBLDZCQUE2QjtZQUM3QixNQUFNQyxXQUFXckIsWUFBWW9CLGdCQUFnQjtZQUM3Qzt1Q0FBTyxJQUFNbkIsY0FBY29COztRQUM3Qjs4QkFBRyxFQUFFO0lBRUwsTUFBTUQsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRkwsV0FBVztZQUVYLHNDQUFzQztZQUN0QyxNQUFNTyxhQUFhLElBQUlDO1lBQ3ZCLE1BQU1DLFlBQVlDLFdBQVcsSUFBTUgsV0FBV0ksS0FBSyxJQUFJLFFBQVEsbUJBQW1CO1lBRWxGLE1BQU0sQ0FBQ0MsZUFBZUMsY0FBYyxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztnQkFDdkRDLE1BQU0sY0FBYztvQkFDbEJDLFFBQVFWLFdBQVdVLE1BQU07b0JBQ3pCQyxTQUFTO3dCQUNQLGdCQUFnQjtvQkFDbEI7Z0JBQ0Y7Z0JBQ0FGLE1BQU0sY0FBYztvQkFDbEJDLFFBQVFWLFdBQVdVLE1BQU07b0JBQ3pCQyxTQUFTO3dCQUNQLGdCQUFnQjtvQkFDbEI7Z0JBQ0Y7YUFDRDtZQUVEQyxhQUFhVjtZQUViLElBQUlXLFlBQVksRUFBRTtZQUNsQixJQUFJQyxXQUFXLEVBQUU7WUFFakIsSUFBSVQsY0FBY1UsRUFBRSxFQUFFO2dCQUNwQixJQUFJO29CQUNGLE1BQU1DLGNBQWMsTUFBTVgsY0FBY1ksSUFBSTtvQkFDNUNKLFlBQVlHLFlBQVlFLE9BQU8sR0FBR0YsWUFBWUcsSUFBSSxHQUFHLEVBQUU7Z0JBQ3pELEVBQUUsT0FBT0MsR0FBRztvQkFDVkMsUUFBUUMsSUFBSSxDQUFDLG1DQUFtQ0Y7Z0JBQ2xEO1lBQ0Y7WUFFQSxJQUFJZCxjQUFjUyxFQUFFLEVBQUU7Z0JBQ3BCLElBQUk7b0JBQ0YsTUFBTVEsY0FBYyxNQUFNakIsY0FBY1csSUFBSTtvQkFDNUNILFdBQVdTLFlBQVlMLE9BQU8sR0FBR0ssWUFBWUMsS0FBSyxHQUFHLEVBQUU7Z0JBQ3pELEVBQUUsT0FBT0osR0FBRztvQkFDVkMsUUFBUUMsSUFBSSxDQUFDLG1DQUFtQ0Y7Z0JBQ2xEO1lBQ0Y7WUFFQSwyQkFBMkI7WUFDM0IsTUFBTXRDLGFBQWErQixVQUFVWSxNQUFNO1lBQ25DLE1BQU0xQyxhQUFhOEIsVUFBVWEsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxNQUFNLEtBQUssU0FBU0gsTUFBTTtZQUMzRSxNQUFNekMsZ0JBQWdCNkIsVUFBVWEsTUFBTSxDQUFDQyxDQUFBQSxPQUNyQ0EsS0FBS0MsTUFBTSxLQUFLLHlCQUF5QkQsS0FBS0MsTUFBTSxLQUFLLHNCQUN6REgsTUFBTTtZQUVSLHdDQUF3QztZQUN4QyxNQUFNSSxRQUFRLElBQUluRTtZQUNsQixNQUFNb0UscUJBQXFCakIsVUFBVWEsTUFBTSxDQUFDQyxDQUFBQSxPQUMxQ0EsS0FBS0ksT0FBTyxJQUFJLElBQUlyRSxLQUFLaUUsS0FBS0ksT0FBTyxJQUFJRjtZQUczQyw0Q0FBNEM7WUFDNUMsTUFBTUcscUJBQXFCbkIsVUFBVWEsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxNQUFNLEtBQUs7WUFDcEUsTUFBTUssa0JBQWtCO21CQUFJLElBQUlDLElBQUk7dUJBQUlKO3VCQUF1QkU7aUJBQW1CO2FBQUU7WUFDcEYsTUFBTS9DLGVBQWVnRCxnQkFBZ0JSLE1BQU07WUFFM0Msc0NBQXNDO1lBQ3RDOUIscUJBQXFCc0M7WUFFckIsd0NBQXdDO1lBQ3hDLE1BQU1FLFdBQVcsaUJBQTJDLE9BQTFCLElBQUl6RSxPQUFPMEUsWUFBWTtZQUN6RCxNQUFNQyxrQkFBa0JDLGFBQWFDLE9BQU8sQ0FBQ0o7WUFFN0MsSUFBSUYsZ0JBQWdCUixNQUFNLEdBQUcsS0FBSyxDQUFDWSxtQkFBbUIsQ0FBQy9DLG1CQUFtQjtnQkFDeEVPLG9CQUFvQjtnQkFDcEJOLHFCQUFxQjtnQkFDckIrQyxhQUFhRSxPQUFPLENBQUNMLFVBQVU7Z0JBQy9CZCxRQUFRb0IsR0FBRyxDQUFDLG9CQUEyQyxPQUF2QlIsZ0JBQWdCUixNQUFNLEVBQUM7WUFDekQsT0FBTztnQkFDTDVCLG9CQUFvQjtZQUN0QjtZQUVBLE1BQU1YLGVBQWUyQixVQUFVYSxNQUFNLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLE1BQU0sS0FBSyxXQUFXSCxNQUFNO1lBRS9FLDRCQUE0QjtZQUM1QixNQUFNaUIsV0FBVyxJQUFJaEYsT0FBTzBFLFlBQVk7WUFDeEMsTUFBTS9DLGFBQWF3QixVQUFVYSxNQUFNLENBQUNDLENBQUFBO2dCQUNsQyxJQUFJLENBQUNBLEtBQUtnQixTQUFTLEVBQUUsT0FBTztnQkFDNUIsT0FBTyxJQUFJakYsS0FBS2lFLEtBQUtnQixTQUFTLEVBQUVQLFlBQVksT0FBT007WUFDckQsR0FBR2pCLE1BQU07WUFFVDVDLFNBQVM7Z0JBQ1BDO2dCQUNBQztnQkFDQUM7Z0JBQ0FDO2dCQUNBQztnQkFDQUMsYUFBYTJCLFNBQVNXLE1BQU07Z0JBQzVCckMsYUFBYTBCLFNBQVNZLE1BQU0sQ0FBQ2tCLENBQUFBLElBQUtBLEVBQUVDLFFBQVEsRUFBRXBCLE1BQU07Z0JBQ3BEcEM7WUFDRjtRQUNGLEVBQUUsT0FBT3lELE9BQU87WUFDZHpCLFFBQVF5QixLQUFLLENBQUMseUJBQXlCQTtZQUV2QyxzQkFBc0I7WUFDdEIsSUFBSUEsaUJBQWlCQyxTQUFTRCxNQUFNM0UsSUFBSSxLQUFLLGNBQWM7Z0JBQ3pEa0QsUUFBUW9CLEdBQUcsQ0FBQztZQUNkO1lBRUEsZ0NBQWdDO1lBQ2hDNUQsU0FBUztnQkFDUEMsWUFBWTtnQkFDWkMsWUFBWTtnQkFDWkMsZUFBZTtnQkFDZkMsY0FBYztnQkFDZEMsY0FBYztnQkFDZEMsYUFBYTtnQkFDYkMsYUFBYTtnQkFDYkMsWUFBWTtZQUNkO1FBQ0YsU0FBVTtZQUNSSSxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU11RCxrQkFBa0I7UUFDdEI7WUFBRTdFLE1BQU1OLEVBQUU7WUFBeUJPLE1BQU07WUFBTUMsUUFBUTtZQUFNQyxNQUFNO1FBQWE7UUFDaEY7WUFBRUgsTUFBTU4sRUFBRTtZQUF5Qk8sTUFBTTtZQUFNQyxRQUFRO1lBQU9DLE1BQU07WUFBZTJFLFlBQVk7UUFBYTtRQUM1RztZQUFFOUUsTUFBTU4sRUFBRTtZQUF3Qk8sTUFBTTtZQUFLQyxRQUFRO1lBQU9DLE1BQU07WUFBYzJFLFlBQVk7UUFBZTtRQUMzRztZQUFFOUUsTUFBTU4sRUFBRTtZQUE4Qk8sTUFBTTtZQUFNQyxRQUFRO1lBQU9DLE1BQU07WUFBb0IyRSxZQUFZO1FBQWdCO1FBQ3pIO1lBQUU5RSxNQUFNTixFQUFFO1lBQThCTyxNQUFNO1lBQU1DLFFBQVE7WUFBT0MsTUFBTTtZQUFvQjRFLFdBQVc7UUFBSztRQUM3RztZQUFFL0UsTUFBTU4sRUFBRTtZQUEwQk8sTUFBTTtZQUFNQyxRQUFRO1lBQU9DLE1BQU07WUFBZTRFLFdBQVc7UUFBSztLQUVyRyxDQUFDeEIsTUFBTSxDQUFDQyxDQUFBQTtRQUNQLElBQUlBLEtBQUt1QixTQUFTLElBQUk3RixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1rQixJQUFJLE1BQUssU0FBUyxPQUFPO1FBQ3JELElBQUlvRCxLQUFLc0IsVUFBVSxJQUFJLENBQUMxRixjQUFjb0UsS0FBS3NCLFVBQVUsR0FBRyxPQUFPO1FBQy9ELE9BQU87SUFDVDtJQUVBLElBQUl6RCxTQUFTO1FBQ1gscUJBQ0UsOERBQUMyRDtZQUFJQyxPQUFPO2dCQUNWQyxXQUFXO2dCQUNYQyxZQUFZO2dCQUNaQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxnQkFBZ0I7Z0JBQ2hCQyxZQUFZO2dCQUNaQyxXQUFXO1lBQ2I7c0JBQ0UsNEVBQUNSO2dCQUFJQyxPQUFPO29CQUNWUSxXQUFXO29CQUNYQyxPQUFPO2dCQUNUOztrQ0FDRSw4REFBQ1Y7d0JBQUlDLE9BQU87NEJBQ1ZHLFNBQVM7NEJBQ1RDLFlBQVk7NEJBQ1pDLGdCQUFnQjs0QkFDaEJLLGNBQWM7d0JBQ2hCO2tDQUNFLDRFQUFDN0csd0RBQUlBOzRCQUFDOEcsTUFBSzs0QkFBUVgsT0FBTztnQ0FBRVksVUFBVTs0QkFBTzs7Ozs7Ozs7Ozs7a0NBRS9DLDhEQUFDYjt3QkFBSUMsT0FBTzs0QkFBRVksVUFBVTs0QkFBUUYsY0FBYzt3QkFBTztrQ0FBRzs7Ozs7O2tDQUN4RCw4REFBQ1g7d0JBQUlDLE9BQU87NEJBQUVZLFVBQVU7NEJBQVVGLGNBQWM7d0JBQU87a0NBQUlqRyxFQUFFOzs7Ozs7a0NBQzdELDhEQUFDc0Y7d0JBQUlDLE9BQU87NEJBQUVTLE9BQU87NEJBQVdHLFVBQVU7d0JBQU87a0NBQUluRyxFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztJQUkvRDtJQUVBLHFCQUNFLDhEQUFDaEIsNERBQVNBO2tCQUNSLDRFQUFDc0c7WUFBSUMsT0FBTztnQkFDVkMsV0FBVztnQkFDWEMsWUFBWTtnQkFDWk8sT0FBTztnQkFDUEgsWUFBWTtnQkFDWkMsV0FBVzdGLFFBQVEsUUFBUTtZQUM3Qjs7OEJBRUUsOERBQUNmLDJEQUFPQTtvQkFBQ2tILFFBQVF0RztvQkFBYXVHLFVBQVUsSUFBTXRHLGVBQWUsQ0FBQ0Q7Ozs7OztnQkFHN0RpQyxrQ0FDQyw4REFBQ3VEO29CQUFJQyxPQUFPO3dCQUNWZSxVQUFVO3dCQUNWQyxLQUFLO3dCQUNMQyxNQUFNO3dCQUNOQyxPQUFPO3dCQUNQQyxRQUFRO3dCQUNSakIsWUFBWTt3QkFDWk8sT0FBTzt3QkFDUFcsU0FBUzt3QkFDVEMsY0FBYzt3QkFDZEMsV0FBVzt3QkFDWG5CLFNBQVM7d0JBQ1RFLGdCQUFnQjt3QkFDaEJELFlBQVk7d0JBQ1ptQixXQUFXO3dCQUNYQyxVQUFVO29CQUNaOztzQ0FDRSw4REFBQ3pCOzs4Q0FDQyw4REFBQ0E7b0NBQUlDLE9BQU87d0NBQUV5QixZQUFZO3dDQUFRYixVQUFVO3dDQUFVRixjQUFjO29DQUFPOzt3Q0FBRzt3Q0FDeEVqRyxFQUFFO3dDQUFvQjt3Q0FBR0EsRUFBRTt3Q0FBc0I7d0NBQUU2QixrQkFBa0IrQixNQUFNOzs7Ozs7OzhDQUVqRiw4REFBQzBCO29DQUFJQyxPQUFPO3dDQUFFWSxVQUFVO29DQUFTOzhDQUMvQiw0RUFBQ2M7d0NBQUcxQixPQUFPOzRDQUFFMkIsY0FBYzs0Q0FBUUMsUUFBUTt3Q0FBUTs7NENBQ2hEdEYsa0JBQWtCdUYsS0FBSyxDQUFDLEdBQUcsR0FBR0MsR0FBRyxDQUFDLENBQUN2RCxNQUFNd0Qsc0JBQ3hDLDhEQUFDQzs7d0RBQ0V6RCxLQUFLeEQsSUFBSTt3REFBQzt3REFBSXdELEtBQUtJLE9BQU8sR0FBRyxrQkFBcUUsT0FBbkQsSUFBSXJFLEtBQUtpRSxLQUFLSSxPQUFPLEVBQUVzRCxrQkFBa0IsQ0FBQyxZQUFhOzttREFEaEdGOzs7Ozs0Q0FJVnpGLGtCQUFrQitCLE1BQU0sR0FBRyxtQkFDMUIsOERBQUMyRDs7b0RBQUc7b0RBQWMxRixrQkFBa0IrQixNQUFNLEdBQUc7b0RBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLdkQsOERBQUM2RDs0QkFDQ0MsU0FBUztnQ0FDUDFGLG9CQUFvQjtnQ0FDcEJOLHFCQUFxQjtnQ0FDckIsdURBQXVEO2dDQUN2RCxNQUFNNEMsV0FBVyxpQkFBMkMsT0FBMUIsSUFBSXpFLE9BQU8wRSxZQUFZO2dDQUN6REUsYUFBYUUsT0FBTyxDQUFDTCxVQUFVO2dDQUMvQmQsUUFBUW9CLEdBQUcsQ0FBQzs0QkFDZDs0QkFDQVcsT0FBTztnQ0FDTEUsWUFBWTtnQ0FDWmtDLFFBQVE7Z0NBQ1IzQixPQUFPO2dDQUNQWSxjQUFjO2dDQUNkRCxTQUFTO2dDQUNUaUIsUUFBUTtnQ0FDUkMsYUFBYTs0QkFDZjtzQ0FFQzdILEVBQUU7Ozs7Ozs7Ozs7Ozs4QkFNVCw4REFBQ3NGO29CQUFJQyxPQUFPO3dCQUNWRSxZQUFZO3dCQUNaa0IsU0FBUzt3QkFDVG1CLGNBQWM7d0JBQ2RwQyxTQUFTO3dCQUNURSxnQkFBZ0I7d0JBQ2hCRCxZQUFZO29CQUNkOztzQ0FFRSw4REFBQ0w7NEJBQUlDLE9BQU87Z0NBQUVHLFNBQVM7Z0NBQVFDLFlBQVk7Z0NBQVVvQyxLQUFLOzRCQUFPOzs4Q0FDL0QsOERBQUNOO29DQUNDQyxTQUFTLElBQU0zSCxlQUFlLENBQUNEO29DQUMvQnlGLE9BQU87d0NBQ0xFLFlBQVk7d0NBQ1prQyxRQUFRO3dDQUNSM0IsT0FBTzt3Q0FDUEcsVUFBVTt3Q0FDVnlCLFFBQVE7d0NBQ1JqQixTQUFTO29DQUNYOzhDQUNEOzs7Ozs7OENBR0QsOERBQUN2SCx3REFBSUE7b0NBQUM4RyxNQUFLOzs7Ozs7Ozs7Ozs7c0NBSWIsOERBQUNaOzRCQUFJQyxPQUFPO2dDQUFFRyxTQUFTO2dDQUFRcUMsS0FBSzs0QkFBTTtzQ0FDdkMxSCxtQkFBbUJnSCxHQUFHLENBQUMsQ0FBQ3ZELE1BQU13RCxzQkFDN0IsOERBQUNHO29DQUVDQyxTQUFTLElBQU1uSSxPQUFPeUksSUFBSSxDQUFDbEUsS0FBS3JELElBQUk7b0NBQ3BDOEUsT0FBTzt3Q0FDTEUsWUFBWTNCLEtBQUt0RCxNQUFNLEdBQUcsWUFBWTt3Q0FDdEN3RixPQUFPbEMsS0FBS3RELE1BQU0sR0FBRyxVQUFVO3dDQUMvQm1ILFFBQVE7d0NBQ1JmLGNBQWM7d0NBQ2RELFNBQVM7d0NBQ1RpQixRQUFRO3dDQUNSekIsVUFBVTt3Q0FDVmEsWUFBWTt3Q0FDWnRCLFNBQVM7d0NBQ1RDLFlBQVk7d0NBQ1pvQyxLQUFLO3dDQUNMRSxZQUFZO29DQUNkO29DQUNBQyxjQUFjLENBQUMzRTt3Q0FDYixJQUFJLENBQUNPLEtBQUt0RCxNQUFNLEVBQUU7NENBQ2hCK0MsRUFBRTRFLE1BQU0sQ0FBQzVDLEtBQUssQ0FBQ0UsVUFBVSxHQUFHOzRDQUM1QmxDLEVBQUU0RSxNQUFNLENBQUM1QyxLQUFLLENBQUNTLEtBQUssR0FBRzt3Q0FDekI7b0NBQ0Y7b0NBQ0FvQyxjQUFjLENBQUM3RTt3Q0FDYixJQUFJLENBQUNPLEtBQUt0RCxNQUFNLEVBQUU7NENBQ2hCK0MsRUFBRTRFLE1BQU0sQ0FBQzVDLEtBQUssQ0FBQ0UsVUFBVSxHQUFHOzRDQUM1QmxDLEVBQUU0RSxNQUFNLENBQUM1QyxLQUFLLENBQUNTLEtBQUssR0FBRzt3Q0FDekI7b0NBQ0Y7O3NEQUVBLDhEQUFDcUM7c0RBQU12RSxLQUFLdkQsSUFBSTs7Ozs7O3dDQUNmdUQsS0FBS3hELElBQUk7O21DQTlCTGdIOzs7Ozs7Ozs7O3NDQW9DWCw4REFBQ2hDOzRCQUFJQyxPQUFPO2dDQUFFRyxTQUFTO2dDQUFRQyxZQUFZO2dDQUFVb0MsS0FBSzs0QkFBTzs7OENBRS9ELDhEQUFDTjtvQ0FDQ0MsU0FBUzt3Q0FDUCxNQUFNWSxVQUFVcEksZ0JBQWdCLE9BQU8sT0FBTzt3Q0FDOUNxSSxLQUFLcEksY0FBYyxDQUFDbUk7b0NBQ3RCO29DQUNBL0MsT0FBTzt3Q0FDTEUsWUFBWTt3Q0FDWmtDLFFBQVE7d0NBQ1IzQixPQUFPO3dDQUNQRyxVQUFVO3dDQUNWeUIsUUFBUTt3Q0FDUmxDLFNBQVM7d0NBQ1RDLFlBQVk7d0NBQ1pvQyxLQUFLO29DQUNQO29DQUNBUyxPQUFPdEksZ0JBQWdCLE9BQU8sc0JBQXNCOzt3Q0FDckQ7c0RBRUMsOERBQUNtSTs0Q0FBSzlDLE9BQU87Z0RBQUVZLFVBQVU7NENBQVM7c0RBQy9CakcsZ0JBQWdCLE9BQU8sT0FBTzs7Ozs7Ozs7Ozs7OzhDQUduQyw4REFBQ3VIO29DQUNDQyxTQUFTakk7b0NBQ1Q4RixPQUFPO3dDQUNMRSxZQUFZO3dDQUNaa0MsUUFBUTt3Q0FDUjNCLE9BQU87d0NBQ1BHLFVBQVU7d0NBQ1Z5QixRQUFRO29DQUNWO29DQUNBWSxPQUFPeEksRUFBRTs4Q0FDVjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU9MLDhEQUFDc0Y7b0JBQUlDLE9BQU87d0JBQ1ZvQixTQUFTO3dCQUNULEdBQUkxRyxRQUFROzRCQUNWNEgsYUFBYS9ILGNBQWMsVUFBVTs0QkFDckNtSSxZQUFZO3dCQUNkLElBQUk7NEJBQ0ZRLFlBQVkzSSxjQUFjLFVBQVU7NEJBQ3BDbUksWUFBWTt3QkFDZCxDQUFDO29CQUNIOztzQ0FFRSw4REFBQzNDOzRCQUFJQyxPQUFPO2dDQUNWRyxTQUFTO2dDQUNURSxnQkFBZ0I7Z0NBQ2hCRCxZQUFZO2dDQUNaTSxjQUFjOzRCQUNoQjs7OENBQ0UsOERBQUNYO29DQUFJQyxPQUFPO3dDQUFFRyxTQUFTO3dDQUFRQyxZQUFZO3dDQUFVb0MsS0FBSztvQ0FBTzs7c0RBQy9ELDhEQUFDekM7NENBQUlDLE9BQU87Z0RBQ1ZtRCxPQUFPO2dEQUNQQyxRQUFRO2dEQUNSbEQsWUFBWTtnREFDWm1CLGNBQWM7Z0RBQ2RsQixTQUFTO2dEQUNUQyxZQUFZO2dEQUNaQyxnQkFBZ0I7Z0RBQ2hCTyxVQUFVOzRDQUNaO3NEQUFHOzs7Ozs7c0RBR0gsOERBQUNiOzs4REFDQyw4REFBQ3NEO29EQUFHckQsT0FBTzt3REFDVFksVUFBVTt3REFDVmEsWUFBWTt3REFDWkcsUUFBUTt3REFDUjFCLFlBQVk7d0RBQ1pvRCxzQkFBc0I7d0RBQ3RCQyxxQkFBcUI7b0RBQ3ZCOzhEQUNHOUksRUFBRTs7Ozs7OzhEQUVMLDhEQUFDK0k7b0RBQUV4RCxPQUFPO3dEQUNSUyxPQUFPO3dEQUNQbUIsUUFBUTt3REFDUmhCLFVBQVU7b0RBQ1o7OERBQ0duRyxFQUFFOzs7Ozs7OERBRUwsOERBQUMrSTtvREFBRXhELE9BQU87d0RBQ1JTLE9BQU87d0RBQ1BtQixRQUFRO3dEQUNSaEIsVUFBVTtvREFDWjs7d0RBQ0duRyxFQUFFO3dEQUFzQjt3REFBR2UsTUFBTUUsVUFBVTt3REFBQzt3REFBRWpCLEVBQUU7d0RBQW9CO3dEQUFJZSxNQUFNTyxXQUFXO3dEQUFDO3dEQUFFdEIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLckcsOERBQUNzRjtvQ0FBSUMsT0FBTzt3Q0FDVkcsU0FBUzt3Q0FDVEMsWUFBWTt3Q0FDWm9DLEtBQUs7d0NBQ0wvQixPQUFPO29DQUNUOztzREFDRSw4REFBQ1Y7NENBQUlDLE9BQU87Z0RBQUVHLFNBQVM7Z0RBQVFDLFlBQVk7Z0RBQVVvQyxLQUFLOzRDQUFNOzs4REFDOUQsOERBQUN6QztvREFBSUMsT0FBTzt3REFDVm1ELE9BQU87d0RBQ1BDLFFBQVE7d0RBQ1JsRCxZQUFZO3dEQUNabUIsY0FBYztvREFDaEI7Ozs7Ozs4REFDQSw4REFBQ3lCO29EQUFLOUMsT0FBTzt3REFBRVksVUFBVTtvREFBUzs4REFBSW5HLEVBQUU7Ozs7Ozs7Ozs7OztzREFFMUMsOERBQUNzRjs0Q0FBSUMsT0FBTztnREFBRUcsU0FBUztnREFBUUMsWUFBWTtnREFBVW9DLEtBQUs7NENBQU07OzhEQUM5RCw4REFBQ007OERBQUs7Ozs7Ozs4REFDTiw4REFBQ0E7b0RBQUs5QyxPQUFPO3dEQUFFWSxVQUFVO29EQUFTOzt3REFBRzt3REFDeEJ4RyxZQUFZcUosa0JBQWtCLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBT2xELDhEQUFDMUQ7NEJBQUlDLE9BQU87Z0NBQ1ZHLFNBQVM7Z0NBQ1R1RCxxQkFBcUI7Z0NBQ3JCbEIsS0FBSztnQ0FDTDlCLGNBQWM7NEJBQ2hCOzs4Q0FDRSw4REFBQzlHLGtFQUFjQTtvQ0FDYm9CLE1BQUs7b0NBQ0xpSSxPQUFPeEksRUFBRTtvQ0FDVGtKLFVBQVVsSixFQUFFO29DQUNaUyxNQUFLO29DQUNMMkUsWUFBVztvQ0FDWHVELFFBQU87Ozs7Ozs4Q0FHVCw4REFBQ3hKLGtFQUFjQTtvQ0FDYm9CLE1BQUs7b0NBQ0xpSSxPQUFPeEksRUFBRTtvQ0FDVGtKLFVBQVVsSixFQUFFO29DQUNaUyxNQUFLO29DQUNMMkUsWUFBVztvQ0FDWHVELFFBQU87Ozs7Ozs4Q0FHVCw4REFBQ3hKLGtFQUFjQTtvQ0FDYm9CLE1BQUs7b0NBQ0xpSSxPQUFPeEksRUFBRTtvQ0FDVGtKLFVBQVVsSixFQUFFO29DQUNaUyxNQUFLO29DQUNMMkUsWUFBVztvQ0FDWHVELFFBQU87Ozs7Ozs4Q0FHVCw4REFBQ3hKLGtFQUFjQTtvQ0FDYm9CLE1BQUs7b0NBQ0xpSSxPQUFPeEksRUFBRTtvQ0FDVGtKLFVBQVVsSixFQUFFO29DQUNaUyxNQUFLO29DQUNMMkUsWUFBVztvQ0FDWHVELFFBQU87Ozs7Ozs7Ozs7OztzQ0FLWCw4REFBQ3JEOzRCQUFJQyxPQUFPO2dDQUNWRyxTQUFTO2dDQUNUdUQscUJBQXFCO2dDQUNyQmxCLEtBQUs7Z0NBQ0w5QixjQUFjOzRCQUNoQjs7OENBQ0UsOERBQUM5RyxrRUFBY0E7b0NBQ2JvQixNQUFLO29DQUNMaUksT0FBT3hJLEVBQUU7b0NBQ1RrSixVQUFVbEosRUFBRTtvQ0FDWlMsTUFBSztvQ0FDTDJFLFlBQVc7b0NBQ1h1RCxRQUFPOzs7Ozs7Z0NBSVJuSixDQUFBQSxpQkFBQUEsMkJBQUFBLEtBQU1rQixJQUFJLE1BQUssV0FBV2xCLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTW1CLFFBQVEsTUFBSyx5QkFDNUMsOERBQUN4QixrRUFBY0E7b0NBQ2JvQixNQUFLO29DQUNMaUksT0FBT3hJLEVBQUU7b0NBQ1RrSixVQUFVbEosRUFBRSxtQkFBbUIsTUFBTUEsRUFBRTtvQ0FDdkNTLE1BQUs7b0NBQ0xrSSxRQUFPOzs7Ozs7OENBSVgsOERBQUN4SixrRUFBY0E7b0NBQ2JvQixNQUFLO29DQUNMaUksT0FBT3hJLEVBQUU7b0NBQ1RrSixVQUFVbEosRUFBRTtvQ0FDWlMsTUFBSztvQ0FDTGtJLFFBQU87Ozs7Ozs4Q0FHVCw4REFBQ3hKLGtFQUFjQTtvQ0FDYm9CLE1BQUs7b0NBQ0xpSSxPQUFPeEksRUFBRTtvQ0FDVGtKLFVBQVVsSixFQUFFO29DQUNaUyxNQUFLO29DQUNMa0ksUUFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVFiLDhEQUFDckQ7b0JBQUlDLE9BQU87d0JBQ1ZlLFVBQVU7d0JBQ1Y2QyxRQUFRO3dCQUNSM0MsTUFBTTt3QkFDTlIsT0FBTzt3QkFDUEcsVUFBVTt3QkFDVk4sWUFBWTt3QkFDWkMsV0FBVztvQkFDYjs4QkFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNWDtHQWxsQndCeEc7O1FBQ1BQLHNEQUFTQTtRQUNnQkUsMERBQU9BO1FBR1lJLHVFQUFpQkE7OztLQUx0REMiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxhcHBcXGRhc2hib2FyZFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IEF1dGhHdWFyZCwgdXNlQXV0aCB9IGZyb20gJ0AvY29tcG9uZW50cy9BdXRoR3VhcmQnO1xuaW1wb3J0IFNpZGViYXIgZnJvbSAnQC9jb21wb25lbnRzL1NpZGViYXInO1xuaW1wb3J0IFN0YXRzQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvU3RhdHNDYXJkJztcbmltcG9ydCBOYXZpZ2F0aW9uQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvTmF2aWdhdGlvbkNhcmQnO1xuaW1wb3J0IExvZ28gZnJvbSAnQC9jb21wb25lbnRzL0xvZ28nO1xuaW1wb3J0IHsgdXNlQXBwVHJhbnNsYXRpb24gfSBmcm9tICdAL2hvb2tzL3VzZUFwcFRyYW5zbGF0aW9uJztcbmltcG9ydCAnQC9zdHlsZXMvZGFzaGJvYXJkLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERhc2hib2FyZCgpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHsgdXNlciwgbG9nb3V0LCBoYXNQZXJtaXNzaW9uIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IFtjdXJyZW50VGltZSwgc2V0Q3VycmVudFRpbWVdID0gdXNlU3RhdGUobmV3IERhdGUoKSk7XG4gIGNvbnN0IFtzaWRlYmFyT3Blbiwgc2V0U2lkZWJhck9wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCB7IHQsIGlzUlRMLCBjdXJyZW50TGFuZywgY2hhbmdlTGFuZ3VhZ2UsIGlzUmVhZHkgfSA9IHVzZUFwcFRyYW5zbGF0aW9uKCk7XG5cbiAgLy8g2KrYudix2YrZgSDYudmG2KfYtdixINin2YTYqtmG2YLZhCDZgdmKINin2YTZgtin2KbZhdipINin2YTYudmE2YjZitipXG4gIGNvbnN0IHRvcE5hdmlnYXRpb25JdGVtcyA9IFtcbiAgICB7IG5hbWU6IHQoJ25hdmlnYXRpb24uZGFzaGJvYXJkJyksIGljb246ICfwn5OKJywgYWN0aXZlOiB0cnVlLCBwYXRoOiAnL2Rhc2hib2FyZCcgfSxcbiAgICB7IG5hbWU6IHQoJ25hdmlnYXRpb24ubWVkaWFMaXN0JyksIGljb246ICfwn46sJywgYWN0aXZlOiBmYWxzZSwgcGF0aDogJy9tZWRpYS1saXN0JyB9LFxuICAgIHsgbmFtZTogdCgnbmF2aWdhdGlvbi5hZGRNZWRpYScpLCBpY29uOiAn4p6VJywgYWN0aXZlOiBmYWxzZSwgcGF0aDogJy9hZGQtbWVkaWEnIH0sXG4gICAgeyBuYW1lOiB0KCduYXZpZ2F0aW9uLndlZWtseVNjaGVkdWxlJyksIGljb246ICfwn5OFJywgYWN0aXZlOiBmYWxzZSwgcGF0aDogJy93ZWVrbHktc2NoZWR1bGUnIH0sXG4gICAgeyBuYW1lOiB0KCduYXZpZ2F0aW9uLmRhaWx5U2NoZWR1bGUnKSwgaWNvbjogJ/Cfk4onLCBhY3RpdmU6IGZhbHNlLCBwYXRoOiAnL2RhaWx5LXNjaGVkdWxlJyB9LFxuICAgIHsgbmFtZTogdCgnbmF2aWdhdGlvbi5yZXBvcnRzJyksIGljb246ICfwn5OLJywgYWN0aXZlOiBmYWxzZSwgcGF0aDogJy9yZXBvcnRzJyB9LFxuICAgIC4uLih1c2VyPy5yb2xlID09PSAnQURNSU4nICYmIHVzZXI/LnVzZXJuYW1lID09PSAnYWRtaW4nID8gW3sgbmFtZTogdCgnbmF2aWdhdGlvbi51bmlmaWVkU3lzdGVtJyksIGljb246ICfwn5OkJywgYWN0aXZlOiBmYWxzZSwgcGF0aDogJy91bmlmaWVkLXN5c3RlbScgfV0gOiBbXSksXG4gICAgeyBuYW1lOiB0KCduYXZpZ2F0aW9uLmFkbWluRGFzaGJvYXJkJyksIGljb246ICfwn5GlJywgYWN0aXZlOiBmYWxzZSwgcGF0aDogJy9hZG1pbi1kYXNoYm9hcmQnIH0sXG4gICAgeyBuYW1lOiB0KCduYXZpZ2F0aW9uLnN0YXRpc3RpY3MnKSwgaWNvbjogJ/Cfk4gnLCBhY3RpdmU6IGZhbHNlLCBwYXRoOiAnL3N0YXRpc3RpY3MnIH1cbiAgXTtcblxuICAvLyDYqtit2K/ZitirINin2YTZiNmC2Kog2YPZhCDYq9in2YbZitipXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdGltZXIgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBzZXRDdXJyZW50VGltZShuZXcgRGF0ZSgpKTtcbiAgICB9LCAxMDAwKTtcblxuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKHRpbWVyKTtcbiAgfSwgW10pO1xuXG4gIC8vINio2YrYp9mG2KfYqiDYp9mE2KXYrdi12KfYptmK2KfYqiDYp9mE2K3ZgtmK2YLZitipXG4gIGNvbnN0IFtzdGF0cywgc2V0U3RhdHNdID0gdXNlU3RhdGUoe1xuICAgIHRvdGFsTWVkaWE6IDAsXG4gICAgdmFsaWRNZWRpYTogMCxcbiAgICByZWplY3RlZE1lZGlhOiAwLFxuICAgIGV4cGlyZWRNZWRpYTogMCxcbiAgICBwZW5kaW5nTWVkaWE6IDAsXG4gICAgYWN0aXZlVXNlcnM6IDAsXG4gICAgb25saW5lVXNlcnM6IDAsXG4gICAgdG9kYXlBZGRlZDogMFxuICB9KTtcblxuICAvLyDYrdin2YTYqSDYqtmG2KjZitmHINin2YTZhdmI2KfYryDYp9mE2YXZhtiq2YfZitipXG4gIGNvbnN0IFtleHBpcmVkQWxlcnRTaG93biwgc2V0RXhwaXJlZEFsZXJ0U2hvd25dID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuXG4gIC8vINiq2K7YstmK2YYg2KfZhNmF2YjYp9ivINin2YTZhdmG2KrZh9mK2Kkg2YTZhNiq2YbYqNmK2YdcbiAgY29uc3QgW2V4cGlyZWRNZWRpYUl0ZW1zLCBzZXRFeHBpcmVkTWVkaWFJdGVtc10gPSB1c2VTdGF0ZShbXSk7XG4gIGNvbnN0IFtzaG93RXhwaXJlZEFsZXJ0LCBzZXRTaG93RXhwaXJlZEFsZXJ0XSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDYrNmE2Kgg2KfZhNio2YrYp9mG2KfYqiDYp9mE2K3ZgtmK2YLZitipXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgZmV0Y2hSZWFsU3RhdHMoKTtcbiAgICAvLyDYqtit2K/ZitirINin2YTYqNmK2KfZhtin2Kog2YPZhCAzMCDYq9in2YbZitipXG4gICAgY29uc3QgaW50ZXJ2YWwgPSBzZXRJbnRlcnZhbChmZXRjaFJlYWxTdGF0cywgMzAwMDApO1xuICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGZldGNoUmVhbFN0YXRzID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuXG4gICAgICAvLyDYrNmE2Kgg2KfZhNio2YrYp9mG2KfYqiDZhdmGIEFQSSDZhdi5IHRpbWVvdXQg2KPYt9mI2YRcbiAgICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IGNvbnRyb2xsZXIuYWJvcnQoKSwgMTUwMDApOyAvLyAxNSDYq9in2YbZitipIHRpbWVvdXRcblxuICAgICAgY29uc3QgW21lZGlhUmVzcG9uc2UsIHVzZXJzUmVzcG9uc2VdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBmZXRjaCgnL2FwaS9tZWRpYScsIHtcbiAgICAgICAgICBzaWduYWw6IGNvbnRyb2xsZXIuc2lnbmFsLFxuICAgICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgfVxuICAgICAgICB9KSxcbiAgICAgICAgZmV0Y2goJy9hcGkvdXNlcnMnLCB7XG4gICAgICAgICAgc2lnbmFsOiBjb250cm9sbGVyLnNpZ25hbCxcbiAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgIH1cbiAgICAgICAgfSlcbiAgICAgIF0pO1xuXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKTtcblxuICAgICAgbGV0IG1lZGlhRGF0YSA9IFtdO1xuICAgICAgbGV0IHVzZXJEYXRhID0gW107XG5cbiAgICAgIGlmIChtZWRpYVJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3QgbWVkaWFSZXN1bHQgPSBhd2FpdCBtZWRpYVJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICBtZWRpYURhdGEgPSBtZWRpYVJlc3VsdC5zdWNjZXNzID8gbWVkaWFSZXN1bHQuZGF0YSA6IFtdO1xuICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKCdGYWlsZWQgdG8gcGFyc2UgbWVkaWEgcmVzcG9uc2U6JywgZSk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgaWYgKHVzZXJzUmVzcG9uc2Uub2spIHtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCB1c2Vyc1Jlc3VsdCA9IGF3YWl0IHVzZXJzUmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgIHVzZXJEYXRhID0gdXNlcnNSZXN1bHQuc3VjY2VzcyA/IHVzZXJzUmVzdWx0LnVzZXJzIDogW107XG4gICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBwYXJzZSB1c2VycyByZXNwb25zZTonLCBlKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyDYrdiz2KfYqCDYp9mE2KXYrdi12KfYptmK2KfYqiDYp9mE2K3ZgtmK2YLZitipXG4gICAgICBjb25zdCB0b3RhbE1lZGlhID0gbWVkaWFEYXRhLmxlbmd0aDtcbiAgICAgIGNvbnN0IHZhbGlkTWVkaWEgPSBtZWRpYURhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5zdGF0dXMgPT09ICdWQUxJRCcpLmxlbmd0aDtcbiAgICAgIGNvbnN0IHJlamVjdGVkTWVkaWEgPSBtZWRpYURhdGEuZmlsdGVyKGl0ZW0gPT5cbiAgICAgICAgaXRlbS5zdGF0dXMgPT09ICdSRUpFQ1RFRF9DRU5TT1JTSElQJyB8fCBpdGVtLnN0YXR1cyA9PT0gJ1JFSkVDVEVEX1RFQ0hOSUNBTCdcbiAgICAgICkubGVuZ3RoO1xuICAgICAgXG4gICAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYp9mE2YXZiNin2K8g2KfZhNmF2YbYqtmH2YrYqSDYrdiz2Kgg2KfZhNiq2KfYsdmK2K5cbiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTtcbiAgICAgIGNvbnN0IGV4cGlyZWRCeURhdGVJdGVtcyA9IG1lZGlhRGF0YS5maWx0ZXIoaXRlbSA9PiBcbiAgICAgICAgaXRlbS5lbmREYXRlICYmIG5ldyBEYXRlKGl0ZW0uZW5kRGF0ZSkgPCB0b2RheVxuICAgICAgKTtcbiAgICAgIFxuICAgICAgLy8g2KfZhNmF2YjYp9ivINin2YTZhdmG2KrZh9mK2KkgKNil2YXYpyDYqNin2YTYrdin2YTYqSDYo9mIINio2KfZhNiq2KfYsdmK2K4pXG4gICAgICBjb25zdCBleHBpcmVkU3RhdHVzSXRlbXMgPSBtZWRpYURhdGEuZmlsdGVyKGl0ZW0gPT4gaXRlbS5zdGF0dXMgPT09ICdFWFBJUkVEJyk7XG4gICAgICBjb25zdCBhbGxFeHBpcmVkSXRlbXMgPSBbLi4ubmV3IFNldChbLi4uZXhwaXJlZEJ5RGF0ZUl0ZW1zLCAuLi5leHBpcmVkU3RhdHVzSXRlbXNdKV07XG4gICAgICBjb25zdCBleHBpcmVkTWVkaWEgPSBhbGxFeHBpcmVkSXRlbXMubGVuZ3RoO1xuICAgICAgXG4gICAgICAvLyDYqtit2K/ZitirINmC2KfYptmF2Kkg2KfZhNmF2YjYp9ivINin2YTZhdmG2KrZh9mK2Kkg2YTZhNiq2YbYqNmK2YdcbiAgICAgIHNldEV4cGlyZWRNZWRpYUl0ZW1zKGFsbEV4cGlyZWRJdGVtcyk7XG5cbiAgICAgIC8vINil2LjZh9in2LEg2KfZhNiq2YbYqNmK2Ycg2YXYsdipINmI2KfYrdiv2Kkg2YHZgti3INmB2Yog2KfZhNis2YTYs9ipXG4gICAgICBjb25zdCBhbGVydEtleSA9IGBleHBpcmVkX2FsZXJ0XyR7bmV3IERhdGUoKS50b0RhdGVTdHJpbmcoKX1gO1xuICAgICAgY29uc3QgYWxlcnRTaG93blRvZGF5ID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oYWxlcnRLZXkpO1xuXG4gICAgICBpZiAoYWxsRXhwaXJlZEl0ZW1zLmxlbmd0aCA+IDAgJiYgIWFsZXJ0U2hvd25Ub2RheSAmJiAhZXhwaXJlZEFsZXJ0U2hvd24pIHtcbiAgICAgICAgc2V0U2hvd0V4cGlyZWRBbGVydCh0cnVlKTtcbiAgICAgICAgc2V0RXhwaXJlZEFsZXJ0U2hvd24odHJ1ZSk7XG4gICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGFsZXJ0S2V5LCAndHJ1ZScpO1xuICAgICAgICBjb25zb2xlLmxvZyhg4pqg77iPINiq2YUg2KfZhNi52KvZiNixINi52YTZiSAke2FsbEV4cGlyZWRJdGVtcy5sZW5ndGh9INmF2KfYr9ipINmF2YbYqtmH2YrYqSAtINi52LHYtiDYp9mE2KrZhtio2YrZh2ApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0U2hvd0V4cGlyZWRBbGVydChmYWxzZSk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IHBlbmRpbmdNZWRpYSA9IG1lZGlhRGF0YS5maWx0ZXIoaXRlbSA9PiBpdGVtLnN0YXR1cyA9PT0gJ1BFTkRJTkcnKS5sZW5ndGg7XG5cbiAgICAgIC8vINit2LPYp9ioINin2YTZhdmI2KfYryDYp9mE2YXYttin2YHYqSDYp9mE2YrZiNmFXG4gICAgICBjb25zdCB0b2RheVN0ciA9IG5ldyBEYXRlKCkudG9EYXRlU3RyaW5nKCk7XG4gICAgICBjb25zdCB0b2RheUFkZGVkID0gbWVkaWFEYXRhLmZpbHRlcihpdGVtID0+IHtcbiAgICAgICAgaWYgKCFpdGVtLmNyZWF0ZWRBdCkgcmV0dXJuIGZhbHNlO1xuICAgICAgICByZXR1cm4gbmV3IERhdGUoaXRlbS5jcmVhdGVkQXQpLnRvRGF0ZVN0cmluZygpID09PSB0b2RheVN0cjtcbiAgICAgIH0pLmxlbmd0aDtcblxuICAgICAgc2V0U3RhdHMoe1xuICAgICAgICB0b3RhbE1lZGlhLFxuICAgICAgICB2YWxpZE1lZGlhLFxuICAgICAgICByZWplY3RlZE1lZGlhLFxuICAgICAgICBleHBpcmVkTWVkaWEsXG4gICAgICAgIHBlbmRpbmdNZWRpYSxcbiAgICAgICAgYWN0aXZlVXNlcnM6IHVzZXJEYXRhLmxlbmd0aCxcbiAgICAgICAgb25saW5lVXNlcnM6IHVzZXJEYXRhLmZpbHRlcih1ID0+IHUuaXNBY3RpdmUpLmxlbmd0aCxcbiAgICAgICAgdG9kYXlBZGRlZFxuICAgICAgfSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHN0YXRzOicsIGVycm9yKTtcblxuICAgICAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2YbZiNi5INin2YTYrti32KNcbiAgICAgIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yICYmIGVycm9yLm5hbWUgPT09ICdBYm9ydEVycm9yJykge1xuICAgICAgICBjb25zb2xlLmxvZygnUmVxdWVzdCB3YXMgYWJvcnRlZCBkdWUgdG8gdGltZW91dCcpO1xuICAgICAgfVxuXG4gICAgICAvLyDYqNmK2KfZhtin2Kog2KfZgdiq2LHYp9i22YrYqSDZgdmKINit2KfZhNipINin2YTYrti32KNcbiAgICAgIHNldFN0YXRzKHtcbiAgICAgICAgdG90YWxNZWRpYTogMCxcbiAgICAgICAgdmFsaWRNZWRpYTogMCxcbiAgICAgICAgcmVqZWN0ZWRNZWRpYTogMCxcbiAgICAgICAgZXhwaXJlZE1lZGlhOiAwLFxuICAgICAgICBwZW5kaW5nTWVkaWE6IDAsXG4gICAgICAgIGFjdGl2ZVVzZXJzOiA0LFxuICAgICAgICBvbmxpbmVVc2VyczogMSxcbiAgICAgICAgdG9kYXlBZGRlZDogMFxuICAgICAgfSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBuYXZpZ2F0aW9uSXRlbXMgPSBbXG4gICAgeyBuYW1lOiB0KCduYXZpZ2F0aW9uLmRhc2hib2FyZCcpLCBpY29uOiAn8J+TiicsIGFjdGl2ZTogdHJ1ZSwgcGF0aDogJy9kYXNoYm9hcmQnIH0sXG4gICAgeyBuYW1lOiB0KCduYXZpZ2F0aW9uLm1lZGlhTGlzdCcpLCBpY29uOiAn8J+OrCcsIGFjdGl2ZTogZmFsc2UsIHBhdGg6ICcvbWVkaWEtbGlzdCcsIHBlcm1pc3Npb246ICdNRURJQV9SRUFEJyB9LFxuICAgIHsgbmFtZTogdCgnbmF2aWdhdGlvbi5hZGRNZWRpYScpLCBpY29uOiAn4p6VJywgYWN0aXZlOiBmYWxzZSwgcGF0aDogJy9hZGQtbWVkaWEnLCBwZXJtaXNzaW9uOiAnTUVESUFfQ1JFQVRFJyB9LFxuICAgIHsgbmFtZTogdCgnbmF2aWdhdGlvbi53ZWVrbHlTY2hlZHVsZScpLCBpY29uOiAn8J+ThScsIGFjdGl2ZTogZmFsc2UsIHBhdGg6ICcvd2Vla2x5LXNjaGVkdWxlJywgcGVybWlzc2lvbjogJ1NDSEVEVUxFX1JFQUQnIH0sXG4gICAgeyBuYW1lOiB0KCduYXZpZ2F0aW9uLmFkbWluRGFzaGJvYXJkJyksIGljb246ICfwn5GlJywgYWN0aXZlOiBmYWxzZSwgcGF0aDogJy9hZG1pbi1kYXNoYm9hcmQnLCBhZG1pbk9ubHk6IHRydWUgfSxcbiAgICB7IG5hbWU6IHQoJ25hdmlnYXRpb24uc3RhdGlzdGljcycpLCBpY29uOiAn8J+TiCcsIGFjdGl2ZTogZmFsc2UsIHBhdGg6ICcvc3RhdGlzdGljcycsIGFkbWluT25seTogdHJ1ZSB9LFxuXG4gIF0uZmlsdGVyKGl0ZW0gPT4ge1xuICAgIGlmIChpdGVtLmFkbWluT25seSAmJiB1c2VyPy5yb2xlICE9PSAnQURNSU4nKSByZXR1cm4gZmFsc2U7XG4gICAgaWYgKGl0ZW0ucGVybWlzc2lvbiAmJiAhaGFzUGVybWlzc2lvbihpdGVtLnBlcm1pc3Npb24pKSByZXR1cm4gZmFsc2U7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0pO1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgbWluSGVpZ2h0OiAnMTAwdmgnLFxuICAgICAgICBiYWNrZ3JvdW5kOiAnIzFhMWQyOScsXG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgZm9udEZhbWlseTogJ0NhaXJvLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgICAgIGRpcmVjdGlvbjogJ3J0bCdcbiAgICAgIH19PlxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJyxcbiAgICAgICAgICBjb2xvcjogJ3doaXRlJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzMwcHgnXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICA8TG9nbyBzaXplPVwibGFyZ2VcIiBzdHlsZT17eyBmb250U2l6ZTogJzJyZW0nIH19IC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzNyZW0nLCBtYXJnaW5Cb3R0b206ICcyMHB4JyB9fT7ij7M8L2Rpdj5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGZvbnRTaXplOiAnMS41cmVtJywgbWFyZ2luQm90dG9tOiAnMTBweCcgfX0+e3QoJ2NvbW1vbi5sb2FkaW5nRGF0YScpfTwvZGl2PlxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICcjYTBhZWMwJywgZm9udFNpemU6ICcxcmVtJyB9fT57dCgnbWVzc2FnZXMucGxlYXNlV2FpdCcpfTwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxBdXRoR3VhcmQ+XG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIG1pbkhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgYmFja2dyb3VuZDogJyMxYTFkMjknLFxuICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgZm9udEZhbWlseTogJ0NhaXJvLCBBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgICAgIGRpcmVjdGlvbjogaXNSVEwgPyAncnRsJyA6ICdsdHInXG4gICAgICB9fT5cbiAgICAgICAgey8qINin2YTYtNix2YrYtyDYp9mE2KzYp9mG2KjZiiAqL31cbiAgICAgICAgPFNpZGViYXIgaXNPcGVuPXtzaWRlYmFyT3Blbn0gb25Ub2dnbGU9eygpID0+IHNldFNpZGViYXJPcGVuKCFzaWRlYmFyT3Blbil9IC8+XG4gICAgICAgIFxuICAgICAgICB7Lyog2KrZhtio2YrZhyDYp9mE2YXZiNin2K8g2KfZhNmF2YbYqtmH2YrYqSAqL31cbiAgICAgICAge3Nob3dFeHBpcmVkQWxlcnQgJiYgKFxuICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLFxuICAgICAgICAgICAgdG9wOiAnMjBweCcsXG4gICAgICAgICAgICBsZWZ0OiAnMjBweCcsXG4gICAgICAgICAgICByaWdodDogJzIwcHgnLFxuICAgICAgICAgICAgekluZGV4OiAxMDAwLFxuICAgICAgICAgICAgYmFja2dyb3VuZDogJyNlZjQ0NDQnLFxuICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICBwYWRkaW5nOiAnMTVweCAyMHB4JyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCA2cHggcmdiYSgwLCAwLCAwLCAwLjEpJyxcbiAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsXG4gICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgIG1heEhlaWdodDogJzMwMHB4JyxcbiAgICAgICAgICAgIG92ZXJmbG93OiAnYXV0bydcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZm9udFdlaWdodDogJ2JvbGQnLCBmb250U2l6ZTogJzEuMXJlbScsIG1hcmdpbkJvdHRvbTogJzEwcHgnIH19PlxuICAgICAgICAgICAgICAgIOKaoO+4jyB7dCgnbWVzc2FnZXMud2FybmluZycpfToge3QoJ3N0YXRzLmV4cGlyZWRNZWRpYScpfSB7ZXhwaXJlZE1lZGlhSXRlbXMubGVuZ3RofVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBmb250U2l6ZTogJzAuOXJlbScgfX0+XG4gICAgICAgICAgICAgICAgPHVsIHN0eWxlPXt7IHBhZGRpbmdSaWdodDogJzIwcHgnLCBtYXJnaW46ICc1cHggMCcgfX0+XG4gICAgICAgICAgICAgICAgICB7ZXhwaXJlZE1lZGlhSXRlbXMuc2xpY2UoMCwgNSkubWFwKChpdGVtLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpbmRleH0+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX0gLSB7aXRlbS5lbmREYXRlID8gYNmF2YbYqtmH2YrYqSDYqNiq2KfYsdmK2K46ICR7bmV3IERhdGUoaXRlbS5lbmREYXRlKS50b0xvY2FsZURhdGVTdHJpbmcoJ2FyLUVHJyl9YCA6ICfZhdmG2KrZh9mK2KknfVxuICAgICAgICAgICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICB7ZXhwaXJlZE1lZGlhSXRlbXMubGVuZ3RoID4gNSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxsaT4uLi4g2YjYp9mE2YXYstmK2K8gKHtleHBpcmVkTWVkaWFJdGVtcy5sZW5ndGggLSA1fSDZhdin2K/YqSDYo9iu2LHZiSk8L2xpPlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgc2V0U2hvd0V4cGlyZWRBbGVydChmYWxzZSk7XG4gICAgICAgICAgICAgICAgc2V0RXhwaXJlZEFsZXJ0U2hvd24odHJ1ZSk7XG4gICAgICAgICAgICAgICAgLy8g2K3Zgdi4INmB2YogbG9jYWxTdG9yYWdlINmE2YXZhti5INi42YfZiNixINin2YTYqtmG2KjZitmHINmF2LHYqSDYo9iu2LHZiSDYp9mE2YrZiNmFXG4gICAgICAgICAgICAgICAgY29uc3QgYWxlcnRLZXkgPSBgZXhwaXJlZF9hbGVydF8ke25ldyBEYXRlKCkudG9EYXRlU3RyaW5nKCl9YDtcbiAgICAgICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShhbGVydEtleSwgJ3RydWUnKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UlSDYqtmFINil2LrZhNin2YIg2KrZhtio2YrZhyDYp9mE2YXZiNin2K8g2KfZhNmF2YbYqtmH2YrYqSDZhtmH2KfYptmK2KfZiyDZhNmE2YrZiNmFJyk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3JnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKScsXG4gICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnNHB4JyxcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAnNXB4IDEwcHgnLFxuICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgIG1hcmdpblJpZ2h0OiAnMTBweCdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge3QoJ2NvbW1vbi5jbG9zZScpfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG5cbiAgICAgICAgey8qINi02LHZiti3INin2YTYqtmG2YLZhCDYp9mE2LnZhNmI2YogKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzFhMWQyOScsXG4gICAgICAgICAgcGFkZGluZzogJzE1cHggMzBweCcsXG4gICAgICAgICAgYm9yZGVyQm90dG9tOiAnMXB4IHNvbGlkICMyZDM3NDgnLFxuICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxuICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInXG4gICAgICAgIH19PlxuICAgICAgICAgIHsvKiDYstixINin2YTZgtin2KbZhdipINmI2KfZhNmE2YjYrNmIICovfVxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTVweCcgfX0+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNpZGViYXJPcGVuKCFzaWRlYmFyT3Blbil9XG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICBjb2xvcjogJyNhMGFlYzAnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS41cmVtJyxcbiAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICBwYWRkaW5nOiAnNXB4J1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDimLBcbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPExvZ28gc2l6ZT1cIm1lZGl1bVwiIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog2LnZhtin2LXYsSDYp9mE2KrZhtmC2YQgKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzVweCcgfX0+XG4gICAgICAgICAgICB7dG9wTmF2aWdhdGlvbkl0ZW1zLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goaXRlbS5wYXRoKX1cbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogaXRlbS5hY3RpdmUgPyAnIzQyOTllMScgOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6IGl0ZW0uYWN0aXZlID8gJ3doaXRlJyA6ICcjYTBhZWMwJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICc4cHggMTZweCcsXG4gICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICAgICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgICAgZ2FwOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycydcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgIGlmICghaXRlbS5hY3RpdmUpIHtcbiAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuYmFja2dyb3VuZCA9ICcjMmQzNzQ4JztcbiAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnd2hpdGUnO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgb25Nb3VzZUxlYXZlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgaWYgKCFpdGVtLmFjdGl2ZSkge1xuICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5zdHlsZS5iYWNrZ3JvdW5kID0gJ3RyYW5zcGFyZW50JztcbiAgICAgICAgICAgICAgICAgICAgZS50YXJnZXQuc3R5bGUuY29sb3IgPSAnI2EwYWVjMCc7XG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzcGFuPntpdGVtLmljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgIHtpdGVtLm5hbWV9XG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog2KPYr9mI2KfYqiDYp9mE2YXYs9iq2K7Yr9mFICovfVxuICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTVweCcgfX0+XG4gICAgICAgICAgICB7Lyog2KPZitmC2YjZhtipINiq2LrZitmK2LEg2KfZhNmE2LrYqSAqL31cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IG5ld0xhbmcgPSBjdXJyZW50TGFuZyA9PT0gJ2FyJyA/ICdlbicgOiAnYXInO1xuICAgICAgICAgICAgICAgIGkxOG4uY2hhbmdlTGFuZ3VhZ2UobmV3TGFuZyk7XG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJ3RyYW5zcGFyZW50JyxcbiAgICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgICBjb2xvcjogJyNhMGFlYzAnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS4ycmVtJyxcbiAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAgZ2FwOiAnNXB4J1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICB0aXRsZT17Y3VycmVudExhbmcgPT09ICdhcicgPyAnU3dpdGNoIHRvIEVuZ2xpc2gnIDogJ9in2YTYqtio2K/ZitmEINmE2YTYudix2KjZitipJ31cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg8J+MkFxuICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250U2l6ZTogJzAuOHJlbScgfX0+XG4gICAgICAgICAgICAgICAge2N1cnJlbnRMYW5nID09PSAnYXInID8gJ0VOJyA6ICfYudixJ31cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e2xvZ291dH1cbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnI2EwYWVjMCcsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjJyZW0nLFxuICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIHRpdGxlPXt0KCdhdXRoLmxvZ291dCcpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICDwn5qqXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qINin2YTZhdit2KrZiNmJINin2YTYsdim2YrYs9mKICovfVxuICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgcGFkZGluZzogJzMwcHgnLFxuICAgICAgICAgIC4uLihpc1JUTCA/IHtcbiAgICAgICAgICAgIG1hcmdpblJpZ2h0OiBzaWRlYmFyT3BlbiA/ICcyODBweCcgOiAnMCcsXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiAnbWFyZ2luLXJpZ2h0IDAuM3MgZWFzZSdcbiAgICAgICAgICB9IDoge1xuICAgICAgICAgICAgbWFyZ2luTGVmdDogc2lkZWJhck9wZW4gPyAnMjgwcHgnIDogJzAnLFxuICAgICAgICAgICAgdHJhbnNpdGlvbjogJ21hcmdpbi1sZWZ0IDAuM3MgZWFzZSdcbiAgICAgICAgICB9KVxuICAgICAgICB9fT5cbiAgICAgICAgICB7Lyog2LHYo9izINin2YTYtdmB2K3YqSAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxuICAgICAgICAgICAgYWxpZ25JdGVtczogJ2ZsZXgtc3RhcnQnLFxuICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMzBweCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAnMTVweCcgfX0+XG4gICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICB3aWR0aDogJzUwcHgnLFxuICAgICAgICAgICAgICAgIGhlaWdodDogJzUwcHgnLFxuICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpJyxcbiAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxMnB4JyxcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMS41cmVtJ1xuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICDwn5OKXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcycmVtJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6ICdib2xkJyxcbiAgICAgICAgICAgICAgICAgIG1hcmdpbjogJzAgMCA1cHggMCcsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKScsXG4gICAgICAgICAgICAgICAgICBXZWJraXRCYWNrZ3JvdW5kQ2xpcDogJ3RleHQnLFxuICAgICAgICAgICAgICAgICAgV2Via2l0VGV4dEZpbGxDb2xvcjogJ3RyYW5zcGFyZW50J1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAge3QoJ2Rhc2hib2FyZC50aXRsZScpfVxuICAgICAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICAgICAgPHAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnI2EwYWVjMCcsXG4gICAgICAgICAgICAgICAgICBtYXJnaW46IDAsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nXG4gICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICB7dCgnZGFzaGJvYXJkLnN1YnRpdGxlJyl9XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgIDxwIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICBjb2xvcjogJyM2OGQzOTEnLFxuICAgICAgICAgICAgICAgICAgbWFyZ2luOiAnNXB4IDAgMCAwJyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJ1xuICAgICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgICAge3QoJ2Rhc2hib2FyZC5vdmVydmlldycpfToge3N0YXRzLnRvdGFsTWVkaWF9IHt0KCdzdGF0cy50b3RhbE1lZGlhJyl9IC0ge3N0YXRzLmFjdGl2ZVVzZXJzfSB7dCgnc3RhdHMuYWN0aXZlVXNlcnMnKX1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAgICAgICAgZ2FwOiAnMjBweCcsXG4gICAgICAgICAgICAgIGNvbG9yOiAnI2EwYWVjMCdcbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGdhcDogJzhweCcgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjNjhkMzkxJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJSdcbiAgICAgICAgICAgICAgICB9fT48L2Rpdj5cbiAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBmb250U2l6ZTogJzAuOXJlbScgfX0+e3QoJ2NvbW1vbi5sb2FkaW5nJyl9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBnYXA6ICc4cHgnIH19PlxuICAgICAgICAgICAgICAgIDxzcGFuPvCflIQ8L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gc3R5bGU9e3sgZm9udFNpemU6ICcwLjlyZW0nIH19PlxuICAgICAgICAgICAgICAgICAg2KfZhNmF2LLYp9mF2YbYqToge2N1cnJlbnRUaW1lLnRvTG9jYWxlVGltZVN0cmluZygnYXItRUcnKX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog2KjYt9in2YLYp9iqINin2YTYqtmG2YLZhCAtINin2YTYtdmBINin2YTYo9mI2YQgKDQg2KjYt9in2YLYp9iqKSAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBkaXNwbGF5OiAnZ3JpZCcsXG4gICAgICAgICAgICBncmlkVGVtcGxhdGVDb2x1bW5zOiAncmVwZWF0KDQsIDFmciknLFxuICAgICAgICAgICAgZ2FwOiAnMjBweCcsXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcyMHB4J1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPE5hdmlnYXRpb25DYXJkXG4gICAgICAgICAgICAgIGljb249XCLwn46sXCJcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ25hdmlnYXRpb24ubWVkaWFMaXN0Jyl9XG4gICAgICAgICAgICAgIHN1YnRpdGxlPXt0KCdtZWRpYS5saXN0Jyl9XG4gICAgICAgICAgICAgIHBhdGg9XCIvbWVkaWEtbGlzdFwiXG4gICAgICAgICAgICAgIHBlcm1pc3Npb249XCJNRURJQV9SRUFEXCJcbiAgICAgICAgICAgICAgaGVpZ2h0PVwiMjgwcHhcIlxuICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgPE5hdmlnYXRpb25DYXJkXG4gICAgICAgICAgICAgIGljb249XCLinpVcIlxuICAgICAgICAgICAgICB0aXRsZT17dCgnbmF2aWdhdGlvbi5hZGRNZWRpYScpfVxuICAgICAgICAgICAgICBzdWJ0aXRsZT17dCgnbWVkaWEuYWRkTmV3Jyl9XG4gICAgICAgICAgICAgIHBhdGg9XCIvYWRkLW1lZGlhXCJcbiAgICAgICAgICAgICAgcGVybWlzc2lvbj1cIk1FRElBX0NSRUFURVwiXG4gICAgICAgICAgICAgIGhlaWdodD1cIjI4MHB4XCJcbiAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgIDxOYXZpZ2F0aW9uQ2FyZFxuICAgICAgICAgICAgICBpY29uPVwi8J+ThVwiXG4gICAgICAgICAgICAgIHRpdGxlPXt0KCduYXZpZ2F0aW9uLndlZWtseVNjaGVkdWxlJyl9XG4gICAgICAgICAgICAgIHN1YnRpdGxlPXt0KCdzY2hlZHVsZS53ZWVrbHknKX1cbiAgICAgICAgICAgICAgcGF0aD1cIi93ZWVrbHktc2NoZWR1bGVcIlxuICAgICAgICAgICAgICBwZXJtaXNzaW9uPVwiU0NIRURVTEVfUkVBRFwiXG4gICAgICAgICAgICAgIGhlaWdodD1cIjI4MHB4XCJcbiAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgIDxOYXZpZ2F0aW9uQ2FyZFxuICAgICAgICAgICAgICBpY29uPVwi8J+TilwiXG4gICAgICAgICAgICAgIHRpdGxlPXt0KCduYXZpZ2F0aW9uLmRhaWx5U2NoZWR1bGUnKX1cbiAgICAgICAgICAgICAgc3VidGl0bGU9e3QoJ3NjaGVkdWxlLmRhaWx5Jyl9XG4gICAgICAgICAgICAgIHBhdGg9XCIvZGFpbHktc2NoZWR1bGVcIlxuICAgICAgICAgICAgICBwZXJtaXNzaW9uPVwiU0NIRURVTEVfUkVBRFwiXG4gICAgICAgICAgICAgIGhlaWdodD1cIjI4MHB4XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog2KfZhNi12YEg2KfZhNir2KfZhtmKIC0gNCDYqNi32KfZgtin2KogKi99XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgZGlzcGxheTogJ2dyaWQnLFxuICAgICAgICAgICAgZ3JpZFRlbXBsYXRlQ29sdW1uczogJ3JlcGVhdCg0LCAxZnIpJyxcbiAgICAgICAgICAgIGdhcDogJzIwcHgnLFxuICAgICAgICAgICAgbWFyZ2luQm90dG9tOiAnMjBweCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxOYXZpZ2F0aW9uQ2FyZFxuICAgICAgICAgICAgICBpY29uPVwi8J+Ti1wiXG4gICAgICAgICAgICAgIHRpdGxlPXt0KCduYXZpZ2F0aW9uLnJlcG9ydHMnKX1cbiAgICAgICAgICAgICAgc3VidGl0bGU9e3QoJ2Rhc2hib2FyZC5yZWNlbnRBY3Rpdml0eScpfVxuICAgICAgICAgICAgICBwYXRoPVwiL3JlcG9ydHNcIlxuICAgICAgICAgICAgICBwZXJtaXNzaW9uPVwiU0NIRURVTEVfUkVBRFwiXG4gICAgICAgICAgICAgIGhlaWdodD1cIjI4MHB4XCJcbiAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgIHsvKiDYstixINin2YTYp9iz2KrZitix2KfYry/Yp9mE2KrYtdiv2YrYsSAtINmE2YTYo9iv2YXZhiDZhdiv2YrYsSDYp9mE2KrYrdmD2YUg2YHZgti3ICovfVxuICAgICAgICAgICAge3VzZXI/LnJvbGUgPT09ICdBRE1JTicgJiYgdXNlcj8udXNlcm5hbWUgPT09ICdhZG1pbicgJiYgKFxuICAgICAgICAgICAgICA8TmF2aWdhdGlvbkNhcmRcbiAgICAgICAgICAgICAgICBpY29uPVwi8J+TpFwiXG4gICAgICAgICAgICAgICAgdGl0bGU9e3QoJ25hdmlnYXRpb24udW5pZmllZFN5c3RlbScpfVxuICAgICAgICAgICAgICAgIHN1YnRpdGxlPXt0KCdjb21tb24uaW1wb3J0JykgKyAnLycgKyB0KCdjb21tb24uZXhwb3J0Jyl9XG4gICAgICAgICAgICAgICAgcGF0aD1cIi91bmlmaWVkLXN5c3RlbVwiXG4gICAgICAgICAgICAgICAgaGVpZ2h0PVwiMjgwcHhcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgPE5hdmlnYXRpb25DYXJkXG4gICAgICAgICAgICAgIGljb249XCLwn5GlXCJcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ25hdmlnYXRpb24uYWRtaW5EYXNoYm9hcmQnKX1cbiAgICAgICAgICAgICAgc3VidGl0bGU9e3QoJ2FkbWluLnVzZXJzJyl9XG4gICAgICAgICAgICAgIHBhdGg9XCIvYWRtaW4tZGFzaGJvYXJkXCJcbiAgICAgICAgICAgICAgaGVpZ2h0PVwiMjgwcHhcIlxuICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgPE5hdmlnYXRpb25DYXJkXG4gICAgICAgICAgICAgIGljb249XCLwn5OIXCJcbiAgICAgICAgICAgICAgdGl0bGU9e3QoJ25hdmlnYXRpb24uc3RhdGlzdGljcycpfVxuICAgICAgICAgICAgICBzdWJ0aXRsZT17dCgnZGFzaGJvYXJkLnN0YXRpc3RpY3MnKX1cbiAgICAgICAgICAgICAgcGF0aD1cIi9zdGF0aXN0aWNzXCJcbiAgICAgICAgICAgICAgaGVpZ2h0PVwiMjgwcHhcIlxuICAgICAgICAgICAgLz5cblxuXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDYp9mE2YbYtSDYp9mE2LPZgdmE2YogKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBwb3NpdGlvbjogJ2ZpeGVkJyxcbiAgICAgICAgICBib3R0b206ICcyMHB4JyxcbiAgICAgICAgICBsZWZ0OiAnMjBweCcsXG4gICAgICAgICAgY29sb3I6ICcjNmM3NTdkJyxcbiAgICAgICAgICBmb250U2l6ZTogJzAuNzVyZW0nLFxuICAgICAgICAgIGZvbnRGYW1pbHk6ICdBcmlhbCwgc2Fucy1zZXJpZicsXG4gICAgICAgICAgZGlyZWN0aW9uOiAnbHRyJ1xuICAgICAgICB9fT5cbiAgICAgICAgICBQb3dlcmVkIEJ5IE1haG1vdWQgSXNtYWlsXG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9BdXRoR3VhcmQ+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJBdXRoR3VhcmQiLCJ1c2VBdXRoIiwiU2lkZWJhciIsIk5hdmlnYXRpb25DYXJkIiwiTG9nbyIsInVzZUFwcFRyYW5zbGF0aW9uIiwiRGFzaGJvYXJkIiwicm91dGVyIiwidXNlciIsImxvZ291dCIsImhhc1Blcm1pc3Npb24iLCJjdXJyZW50VGltZSIsInNldEN1cnJlbnRUaW1lIiwiRGF0ZSIsInNpZGViYXJPcGVuIiwic2V0U2lkZWJhck9wZW4iLCJ0IiwiaXNSVEwiLCJjdXJyZW50TGFuZyIsImNoYW5nZUxhbmd1YWdlIiwiaXNSZWFkeSIsInRvcE5hdmlnYXRpb25JdGVtcyIsIm5hbWUiLCJpY29uIiwiYWN0aXZlIiwicGF0aCIsInJvbGUiLCJ1c2VybmFtZSIsInRpbWVyIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwic3RhdHMiLCJzZXRTdGF0cyIsInRvdGFsTWVkaWEiLCJ2YWxpZE1lZGlhIiwicmVqZWN0ZWRNZWRpYSIsImV4cGlyZWRNZWRpYSIsInBlbmRpbmdNZWRpYSIsImFjdGl2ZVVzZXJzIiwib25saW5lVXNlcnMiLCJ0b2RheUFkZGVkIiwiZXhwaXJlZEFsZXJ0U2hvd24iLCJzZXRFeHBpcmVkQWxlcnRTaG93biIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXhwaXJlZE1lZGlhSXRlbXMiLCJzZXRFeHBpcmVkTWVkaWFJdGVtcyIsInNob3dFeHBpcmVkQWxlcnQiLCJzZXRTaG93RXhwaXJlZEFsZXJ0IiwiZmV0Y2hSZWFsU3RhdHMiLCJpbnRlcnZhbCIsImNvbnRyb2xsZXIiLCJBYm9ydENvbnRyb2xsZXIiLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiYWJvcnQiLCJtZWRpYVJlc3BvbnNlIiwidXNlcnNSZXNwb25zZSIsIlByb21pc2UiLCJhbGwiLCJmZXRjaCIsInNpZ25hbCIsImhlYWRlcnMiLCJjbGVhclRpbWVvdXQiLCJtZWRpYURhdGEiLCJ1c2VyRGF0YSIsIm9rIiwibWVkaWFSZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsImRhdGEiLCJlIiwiY29uc29sZSIsIndhcm4iLCJ1c2Vyc1Jlc3VsdCIsInVzZXJzIiwibGVuZ3RoIiwiZmlsdGVyIiwiaXRlbSIsInN0YXR1cyIsInRvZGF5IiwiZXhwaXJlZEJ5RGF0ZUl0ZW1zIiwiZW5kRGF0ZSIsImV4cGlyZWRTdGF0dXNJdGVtcyIsImFsbEV4cGlyZWRJdGVtcyIsIlNldCIsImFsZXJ0S2V5IiwidG9EYXRlU3RyaW5nIiwiYWxlcnRTaG93blRvZGF5IiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInNldEl0ZW0iLCJsb2ciLCJ0b2RheVN0ciIsImNyZWF0ZWRBdCIsInUiLCJpc0FjdGl2ZSIsImVycm9yIiwiRXJyb3IiLCJuYXZpZ2F0aW9uSXRlbXMiLCJwZXJtaXNzaW9uIiwiYWRtaW5Pbmx5IiwiZGl2Iiwic3R5bGUiLCJtaW5IZWlnaHQiLCJiYWNrZ3JvdW5kIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImZvbnRGYW1pbHkiLCJkaXJlY3Rpb24iLCJ0ZXh0QWxpZ24iLCJjb2xvciIsIm1hcmdpbkJvdHRvbSIsInNpemUiLCJmb250U2l6ZSIsImlzT3BlbiIsIm9uVG9nZ2xlIiwicG9zaXRpb24iLCJ0b3AiLCJsZWZ0IiwicmlnaHQiLCJ6SW5kZXgiLCJwYWRkaW5nIiwiYm9yZGVyUmFkaXVzIiwiYm94U2hhZG93IiwibWF4SGVpZ2h0Iiwib3ZlcmZsb3ciLCJmb250V2VpZ2h0IiwidWwiLCJwYWRkaW5nUmlnaHQiLCJtYXJnaW4iLCJzbGljZSIsIm1hcCIsImluZGV4IiwibGkiLCJ0b0xvY2FsZURhdGVTdHJpbmciLCJidXR0b24iLCJvbkNsaWNrIiwiYm9yZGVyIiwiY3Vyc29yIiwibWFyZ2luUmlnaHQiLCJib3JkZXJCb3R0b20iLCJnYXAiLCJwdXNoIiwidHJhbnNpdGlvbiIsIm9uTW91c2VFbnRlciIsInRhcmdldCIsIm9uTW91c2VMZWF2ZSIsInNwYW4iLCJuZXdMYW5nIiwiaTE4biIsInRpdGxlIiwibWFyZ2luTGVmdCIsIndpZHRoIiwiaGVpZ2h0IiwiaDEiLCJXZWJraXRCYWNrZ3JvdW5kQ2xpcCIsIldlYmtpdFRleHRGaWxsQ29sb3IiLCJwIiwidG9Mb2NhbGVUaW1lU3RyaW5nIiwiZ3JpZFRlbXBsYXRlQ29sdW1ucyIsInN1YnRpdGxlIiwiYm90dG9tIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});