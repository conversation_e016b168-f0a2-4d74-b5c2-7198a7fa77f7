/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/media-list/page";
exports.ids = ["app/media-list/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmedia-list%2Fpage&page=%2Fmedia-list%2Fpage&appPaths=%2Fmedia-list%2Fpage&pagePath=private-next-app-dir%2Fmedia-list%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmedia-list%2Fpage&page=%2Fmedia-list%2Fpage&appPaths=%2Fmedia-list%2Fpage&pagePath=private-next-app-dir%2Fmedia-list%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/media-list/page.tsx */ \"(rsc)/./src/app/media-list/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'media-list',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/media-list/page\",\n        pathname: \"/media-list\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmedia-list%2Fpage&page=%2Fmedia-list%2Fpage&appPaths=%2Fmedia-list%2Fpage&pagePath=private-next-app-dir%2Fmedia-list%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/media-list/page.tsx */ \"(rsc)/./src/app/media-list/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtZWRpYS1saXN0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUE4SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccHJvamVjdCBzcG9ydFxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbWVkaWEtbGlzdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\media-list\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/media-list/page.tsx */ \"(ssr)/./src/app/media-list/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtZWRpYS1saXN0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUE4SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccHJvamVjdCBzcG9ydFxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbWVkaWEtbGlzdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./public/locales/ar/common.json":
/*!***************************************!*\
  !*** ./public/locales/ar/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"navigation":{"dashboard":"لوحة التحكم","mediaList":"المواد الإعلامية","addMedia":"إضافة مادة","weeklySchedule":"الخريطة البرامجية","dailySchedule":"جدول الإذاعة اليومي","reports":"تقارير البث","unifiedSystem":"استيراد/تصدير","adminDashboard":"المستخدمين","statistics":"الإحصائيات"},"common":{"welcome":"مرحباً","loading":"جاري التحميل...","loadingSchedule":"جاري تحميل الجدول الأسبوعي...","loadingData":"جاري تحميل البيانات...","save":"حفظ","cancel":"إلغاء","delete":"حذف","edit":"تعديل","add":"إضافة","search":"بحث","filter":"فلترة","export":"تصدير","import":"استيراد","yes":"نعم","no":"لا","ok":"موافق","close":"إغلاق","back":"رجوع","next":"التالي","previous":"السابق","submit":"إرسال","reset":"إعادة تعيين","clear":"مسح","select":"اختيار","selectDate":"اختر التاريخ","selectTime":"اختر الوقت","actions":"الإجراءات","status":"الحالة","type":"النوع","name":"الاسم","description":"الوصف","duration":"المدة","startTime":"وقت البداية","endTime":"وقت النهاية","date":"التاريخ","time":"الوقت","content":"المحتوى","code":"الكود","episode":"الحلقة","season":"الموسم","part":"الجزء","segments":"السيجمنتات","available":"متاح","unavailable":"غير متاح","active":"نشط","inactive":"غير نشط","valid":"صالح","invalid":"غير صالح","expired":"منتهي الصلاحية","pending":"في الانتظار","approved":"موافق عليه","rejected":"مرفوض","total":"الإجمالي","count":"العدد","items":"عنصر","user":"مستخدم","noData":"لا توجد بيانات","noResults":"لا توجد نتائج","error":"خطأ","success":"نجح","warning":"تحذير","info":"معلومات"},"mediaTypes":{"ALL":"جميع الأنواع","PROGRAM":"برنامج","SERIES":"مسلسل","FILM":"فيلم","SONG":"أغنية","PROMO":"إعلان ترويجي","STING":"فاصل","FILLER":"مادة مالئة","NEXT":"التالي","NOW":"الآن","MINI":"Mini","CROSS":"Cross","سنعود":"سنعود","عدنا":"عدنا"},"mediaStatus":{"ALL":"جميع الحالات","VALID":"صالح","REJECTED_CENSORSHIP":"مرفوض رقابياً","REJECTED_TECHNICAL":"مرفوض هندسياً","EXPIRED":"منتهي الصلاحية","HOLD":"معلق"},"channels":{"DOCUMENTARY":"الوثائقية","NEWS":"الأخبار","OTHER":"أخرى"},"dashboard":{"title":"لوحة التحكم","subtitle":"نظام إدارة المواد الإعلامية","totalMedia":"إجمالي المواد","activeSchedules":"الجداول النشطة","todayBroadcast":"إذاعة اليوم","systemUsers":"مستخدمي النظام","recentActivity":"النشاط الأخير","quickActions":"إجراءات سريعة","statistics":"الإحصائيات","overview":"نظرة عامة"},"media":{"title":"إدارة المواد","addNew":"إضافة مادة جديدة","list":"قائمة المواد الإعلامية","details":"تفاصيل المادة","segments":"السيجمنتات","addSegment":"إضافة سيجمنت","segmentCode":"كود السيجمنت","timeIn":"وقت الدخول","timeOut":"وقت الخروج","hardDrive":"القرص الصلب","server":"الخادم","notes":"ملاحظات","startDate":"تاريخ البداية","endDate":"تاريخ النهاية","showInTX":"عرض في TX","episodeNumber":"رقم الحلقة","seasonNumber":"رقم الموسم","partNumber":"رقم الجزء","totalSegments":"إجمالي السيجمنتات","validMedia":"مواد صالحة","expiredMedia":"مواد منتهية الصلاحية","pendingMedia":"مواد في الانتظار","searchByName":"البحث بالاسم أو الوصف","searchByCode":"البحث بكود المادة أو السيجمنت","searchPlaceholder":"ابحث عن مادة إعلامية...","codePlaceholder":"ابحث بالكود...","mediaType":"نوع المادة","mediaStatus":"الحالة","sortBy":"ترتيب حسب","newest":"الأحدث أولاً","oldest":"الأقدم أولاً","byName":"الاسم (أ-ي)","byType":"النوع","searchStats":"عرض {{filtered}} من أصل {{total}} مادة إعلامية","noMediaFound":"لا توجد مواد إعلامية محفوظة","startAdding":"ابدأ بإضافة مادة إعلامية جديدة","exportExcel":"تصدير Excel","exporting":"جاري التصدير...","searchAndFilter":"البحث والفلترة","mediaOverview":"عرض وإدارة المحتوى الإعلامي","searchFilterExport":"يمكنك البحث والفلترة وتصدير البيانات","channel":"القناة","segmentCount":"عدد السيجمنت","description":"الوصف","noCode":"[لا يوجد كود]","edit":"تعديل","delete":"حذف","scrollToTop":"العودة لأعلى الصفحة"},"schedule":{"title":"إدارة الجداول","weekly":"الخريطة البرامجية الأسبوعية","daily":"الجدول الإذاعي اليومي","import":"استيراد جدول","export":"تصدير جدول","broadcast":"الإذاعة","rerun":"إعادة","prime":"برايم","filler":"مالئ","empty":"فارغ","addRow":"إضافة صف","deleteRow":"حذف صف","moveUp":"تحريك لأعلى","moveDown":"تحريك لأسفل","showMap":"عرض الخريطة","hideMap":"إخفاء الخريطة","saveChanges":"حفظ التعديلات","discardChanges":"تجاهل التعديلات","importFromTime":"استيراد من هذا الوقت","broadcastTime":"وقت الإذاعة","programMap":"خريطة البرامج","scheduleItems":"عناصر الجدول","availableMedia":"المواد المتاحة","scheduledMedia":"البرامج المجدولة اليوم","weeklySchedule":"الخريطة البرامجية الأسبوعية","weeklySubtitle":"جدولة البرامج الأسبوعية","importTitle":"استيراد الجدول الإذاعي","importSubtitle":"استيراد الجدول من وقت محدد","importInstructions":"اختر التاريخ → حدد الوقت → استيراد → تعديل المواد → تصدير Excel","importSchedule":"استيراد الجدول","importInstructionsLong":"اختر التاريخ والوقت ثم اضغط \\"استيراد من هذا الوقت\\" لعرض الجدول","loadingSchedule":"جاري تحميل الجدول الأسبوعي...","selectingDate":"جاري تحديد التاريخ...","mediaList":"قائمة المواد","addTempMedia":"إضافة مادة مؤقتة","mediaName":"اسم المادة...","duration":"المدة (مثل: 01:30:00)","notes":"ملاحظات (اختياري)...","add":"إضافة","updateReruns":"تحديث الإعادات","allTypes":"جميع الأنواع","searchMedia":"البحث في المواد...","resultsCount":"{{count}} من {{total}} مادة","noMedia":"لا توجد مواد","changeFilter":"جرب تغيير الفلتر أو البحث","addNewMedia":"أضف مواد جديدة من صفحة المستخدم","deleteTempMedia":"حذف المادة المؤقتة","liveProgram":"هواء","pendingDelivery":"قيد التسليم","temporary":"مؤقت","broadcastSchedule":"الجدول الإذاعي","exportSchedule":"تصدير الخريطة","selectedWeek":"الأسبوع المحدد","previousWeek":"← الأسبوع السابق","nextWeek":"الأسبوع التالي →","time":"الوقت","rerunIndicator":"إعادة - يمكن الحذف للتعديل","hideSchedule":"إخفاء الخريطة","showSchedule":"عرض الخريطة","unknown":"غير معروف","season":"الموسم","episode":"الحلقة","part":"الجزء","confirmDelete":"هل أنت متأكد من حذف {{type}}: \\"{{name}}\\"?","deleteWarningOriginal":"تحذير: حذف المادة الأصلية سيحذف جميع إعاداتها","deleteWarningRerun":"تحذير: حذف الإعادة لن يؤثر على المادة الأصلية","deleteWarningTemp":"سيتم حذف المادة المؤقتة من الجدول","originalMaterial":"مادة أصلية","rerunMaterial":"إعادة","tempMaterial":"مادة مؤقتة","timeConflict":"يوجد تداخل في الأوقات! اختر وقت آخر.","enterMediaName":"يرجى إدخال اسم المادة","confirmDeleteTemp":"هل تريد حذف هذه المادة المؤقتة؟","deleteMediaTitle":"🗑️ حذف المواد:","deleteOriginalInfo":"المواد الأصلية: حذف نهائي مع جميع إعاداتها","deleteRerunInfo":"الإعادات: حذف مع ترك الحقل فارغ للتعديل","deleteConfirmInfo":"سيظهر تأكيد قبل الحذف","confirmDeleteSegment":"هل أنت متأكد من حذف هذا السيجمنت؟","usageInstructions":"📋 تعليمات الاستخدام:","addMediaTitle":"🎯 إضافة المواد:","addMediaInstruction1":"اسحب المواد من القائمة اليمنى إلى الجدول","addMediaInstruction2":"🔄 اسحب المواد من الجدول نفسه لنسخها لمواعيد أخرى","addMediaInstruction3":"🎬 استخدم فلتر النوع للتصفية حسب نوع المادة","addMediaInstruction4":"🔍 استخدم البحث للعثور على المواد بسرعة","primeTimeTitle":"🌟 المواد الأصلية (البرايم تايم):","primeTimeSchedule1":"الأحد-الأربعاء: 18:00-00:00","primeTimeSchedule2":"الخميس-السبت: 18:00-02:00","primeTimeColor":"🟡 لون ذهبي في الجدول","rerunsTitle":"♻️ الإعادات التلقائية (جزئين):","rerunsSchedule1":"الأحد-الأربعاء:","rerunsSchedule2":"الخميس-السبت:","rerunsPart1Sun":"ج1: نفس العمود 00:00-07:59","rerunsPart2Sun":"ج2: العمود التالي 08:00-17:59","rerunsPart1Thu":"ج1: نفس العمود 02:00-07:59","rerunsPart2Thu":"ج2: العمود التالي 08:00-17:59","rerunsColor":"🔘 لون رمادي - يمكن حذفها للتعديل","dateManagementTitle":"📅 إدارة التواريخ:","dateManagementInfo":"استخدم الكالندر والأزرار للتنقل بين الأسابيع • كل أسبوع يُحفظ بشكل منفصل","importantNoteTitle":"💡 ملاحظة مهمة:","importantNoteInfo":"عند إضافة مواد قليلة (١-٣ مواد) في البرايم، ستظهر مع فواصل زمنية في الإعادات لتجنب التكرار المفرط. أضف المزيد من المواد للحصول على تنوع أكبر.","weeklyScheduleTitle":"الخريطة البرامجية","noWeeklyData":"لا توجد بيانات للخريطة البرامجية","types":{"program":"برنامج","series":"مسلسل","film":"فيلم","song":"أغنية","sting":"استنج","fillIn":"فيل إن","filler":"فيلر","promo":"برومو","next":"نكست","now":"ناو","snawod":"سنعود","odna":"عدنا","mini":"Mini","cross":"Cross"},"startTime":"وقت البداية"},"days":{"sunday":"الأحد","monday":"الاثنين","tuesday":"الثلاثاء","wednesday":"الأربعاء","thursday":"الخميس","friday":"الجمعة","saturday":"السبت"},"months":{"january":"يناير","february":"فبراير","march":"مارس","april":"أبريل","may":"مايو","june":"يونيو","july":"يوليو","august":"أغسطس","september":"سبتمبر","october":"أكتوبر","november":"نوفمبر","december":"ديسمبر"},"auth":{"login":"تسجيل الدخول","logout":"تسجيل الخروج","username":"اسم المستخدم","password":"كلمة المرور","loginButton":"دخول","loginError":"خطأ في تسجيل الدخول","accessDenied":"تم رفض الوصول","createUserError":"خطأ في إنشاء المستخدم","deleteUserSuccess":"تم حذف المستخدم بنجاح!","deleteUserError":"خطأ في حذف المستخدم","fillRequiredFields":"يرجى ملء جميع الحقول المطلوبة","updateUserSuccess":"تم تحديث المستخدم بنجاح!","updateUserError":"خطأ في تحديث المستخدم","fullName":"الاسم الكامل","email":"البريد الإلكتروني","phone":"رقم الهاتف","role":"الدور","permissions":"الصلاحيات","allPermissions":"جميع الصلاحيات","manageMedia":"إدارة المواد","manageSchedules":"إدارة الجداول","viewMedia":"عرض المواد","viewSchedules":"عرض الجداول","viewMap":"عرض الخريطة","viewBroadcast":"عرض البث","manageMap":"إدارة الخريطة","roles":{"admin":"مدير النظام","contentManager":"مدير المحتوى","mediaManager":"مدير قاعدة البيانات","scheduler":"مجدول البرامج","fullViewer":"مستخدم رؤية كاملة","dataEntry":"مدخل بيانات","mapScheduler":"مسؤول الخريطة والجدول","viewer":"مستخدم عرض","adminDesc":"صلاحيات كاملة لجميع أجزاء النظام + إدارة المستخدمين","contentManagerDesc":"إدارة كاملة للمواد والجداول (بدون إدارة المستخدمين)","mediaManagerDesc":"إدارة المواد الإعلامية فقط (إضافة، تعديل، حذف)","schedulerDesc":"إدارة الجداول الإذاعية والخريطة البرامجية فقط","fullViewerDesc":"رؤية التطبيق كامل بدون إمكانية التعديل أو الإضافة","dataEntryDesc":"إدخال البيانات والتعديل عليها فقط دون رؤية باقي التطبيق","mapSchedulerDesc":"إدارة الخريطة وجدول البث اليومي مع إمكانية رؤية قاعدة البيانات دون التعديل عليها","viewerDesc":"عرض المحتوى فقط بدون إمكانية التعديل أو الإضافة"},"invalidCredentials":"بيانات الدخول غير صحيحة","welcomeBack":"مرحباً بعودتك","pleaseLogin":"يرجى تسجيل الدخول للمتابعة","userRoles":"أدوار المستخدمين","adminDesc":"صلاحيات كاملة","contentManagerDesc":"إدارة المواد الإعلامية","schedulerDesc":"إدارة الجداول الإذاعية","viewerDesc":"تصفح فقط","sessionExpired":"انتهت صلاحية الجلسة","insufficientPermissions":"صلاحيات غير كافية","status":{"valid":"صالح","rejectedCensorship":"مرفوض رقابي","rejectedTechnical":"مرفوض هندسي","waiting":"في الانتظار"},"segments":"سيجمانت"},"admin":{"title":"إدارة المستخدمين","subtitle":"إضافة وتعديل المستخدمين","users":"المستخدمون","permissions":"الصلاحيات","addUser":"إضافة مستخدم","editUser":"تعديل مستخدم","deleteUser":"حذف مستخدم","userRole":"دور المستخدم","userStatus":"حالة المستخدم","lastLogin":"آخر دخول","createdAt":"تاريخ الإنشاء","updatedAt":"تاريخ التحديث","activeUsers":"المستخدمون النشطون","inactiveUsers":"المستخدمون غير النشطين","totalUsers":"إجمالي المستخدمين","loadingData":"جاري تحميل البيانات...","userManagement":"إدارة المستخدمين","addNewUser":"إضافة مستخدم جديد","username":"اسم المستخدم","password":"كلمة المرور","fullName":"الاسم الكامل","email":"البريد الإلكتروني","phone":"رقم الهاتف","role":"الدور","status":"الحالة","actions":"الإجراءات","active":"نشط","inactive":"غير نشط","createUser":"إنشاء المستخدم","cancel":"إلغاء","saveChanges":"حفظ التغييرات","edit":"تعديل","delete":"حذف","noUsers":"لا يوجد مستخدمين","addUsersMessage":"قم بإضافة مستخدمين جدد باستخدام زر \\"إضافة مستخدم جديد\\"","rolesExplanation":"شرح الأدوار والصلاحيات","allPermissions":"جميع الصلاحيات","mediaManagement":"إدارة المواد","scheduleManagement":"إدارة الجداول","viewMedia":"عرض المواد","viewSchedules":"عرض الجداول","viewMap":"عرض الخريطة","viewBroadcast":"عرض البث","mapManagement":"إدارة الخريطة","permissionsLabel":"الصلاحيات:","noLoginYet":"لم يسجل الدخول بعد","editingUser":"تعديل المستخدم","passwordNote":"(اتركها فارغة للاحتفاظ بالحالية)","confirmDelete":"هل أنت متأكد من حذف هذا المستخدم؟","userCreated":"تم إنشاء المستخدم بنجاح!","userUpdated":"تم تحديث المستخدم بنجاح!","userDeleted":"تم حذف المستخدم بنجاح!","fillRequired":"يرجى ملء جميع الحقول المطلوبة","createError":"خطأ في إنشاء المستخدم","updateError":"خطأ في تحديث المستخدم","deleteError":"خطأ في حذف المستخدم","fetchError":"خطأ في جلب بيانات المستخدمين","serverError":"خطأ في الاتصال بالخادم"},"permissions":{"MEDIA_READ":"قراءة المواد","MEDIA_CREATE":"إنشاء المواد","MEDIA_UPDATE":"تحديث المواد","MEDIA_DELETE":"حذف المواد","SCHEDULE_READ":"قراءة الجداول","SCHEDULE_CREATE":"إنشاء الجداول","SCHEDULE_UPDATE":"تحديث الجداول","SCHEDULE_DELETE":"حذف الجداول","USER_MANAGEMENT":"إدارة المستخدمين","SYSTEM_ADMIN":"إدارة النظام"},"stats":{"totalMedia":"إجمالي المواد","validMedia":"المواد الصالحة","maintenanceMedia":"قيد الصيانة","activeUsers":"المستخدمين النشطين","efficiency":"الكفاءة العامة","operationalCost":"التكلفة التشغيلية","processingTime":"متوسط وقت المعالجة","activeOperations":"العمليات النشطة","growthRate":"معدل النمو","healthRate":"معدل الصحة","issueRate":"معدل المشاكل","activityRate":"معدل النشاط","improvement":"تحسن","dailyAverage":"المتوسط اليومي","loadingDummyData":"تحميل البيانات الوهمية"},"messages":{"success":{"mediaAdded":"تم إضافة المادة بنجاح","mediaUpdated":"تم تحديث المادة بنجاح","mediaDeleted":"تم حذف المادة بنجاح","exportSuccess":"تم التصدير بنجاح","importSuccess":"تم الاستيراد بنجاح","scheduleUpdated":"تم تحديث الجدول بنجاح","userCreated":"تم إنشاء المستخدم بنجاح","userUpdated":"تم تحديث المستخدم بنجاح","userDeleted":"تم حذف المستخدم بنجاح","changesSaved":"تم حفظ التغييرات بنجاح"},"error":{"serverConnection":"خطأ في الاتصال بالخادم","mediaNotFound":"المادة غير موجودة","invalidData":"بيانات غير صحيحة","permissionDenied":"ليس لديك صلاحية","exportFailed":"فشل في التصدير","importFailed":"فشل في الاستيراد","unknownError":"حدث خطأ غير معروف","timeFormatError":"خطأ في تنسيق الوقت","calculationError":"خطأ في الحساب"},"info":{"loading":"جاري التحميل...","saving":"جاري الحفظ...","processing":"جاري المعالجة...","exporting":"جاري التصدير...","importing":"جاري الاستيراد..."}},"home":{"title":"Prime-X","subtitle":"نظام إدارة المواد الإعلامية","loading":"جاري التحميل...","autoRedirect":"أو انتظر للتوجه التلقائي للوحة التحكم","quickNavigation":"التنقل السريع","dashboard":"لوحة التحكم","dailySchedule":"الجدول الإذاعي اليومي","weeklySchedule":"الخريطة البرامجية","mediaList":"المواد الإعلامية","addMedia":"إضافة مادة","adminPanel":"إدارة المستخدمين"},"reports":{"title":"تقارير البث","subtitle":"بحث وإحصاء المواد المذاعة","searchFilters":"فلاتر البحث","mediaType":"نوع المادة","allTypes":"جميع الأنواع","mediaName":"اسم المادة","mediaCode":"كود المادة","source":"المصدر","both":"كلاهما","weekly":"الخريطة الأسبوعية","daily":"الجدول اليومي","dateFrom":"من تاريخ","dateTo":"إلى تاريخ","search":"بحث","exportExcel":"تصدير Excel","showStatistics":"عرض الإحصائيات","hideStatistics":"إخفاء الإحصائيات","searchResults":"نتائج البحث","noResults":"لا توجد نتائج","searchMessage":"استخدم الفلاتر أعلاه للبحث عن المواد المذاعة","resultsCount":"{{count}} نتيجة","statistics":"إحصائيات المواد","totalItems":"إجمالي المواد","totalDuration":"إجمالي المدة","count":"العدد","duration":"المدة","percentage":"النسبة المئوية","loadingMedia":"جاري تحميل المواد...","searchError":"خطأ في البحث","exportError":"خطأ في التصدير","exportSuccess":"تم التصدير بنجاح","date":"التاريخ","time":"الوقت","type":"النوع","name":"الاسم","code":"الكود","rerun":"إعادة","temporary":"مؤقت","original":"أصلي","programsFilmsSeries":"البرامج والأفلام والمسلسلات","promosFillersSting":"البرومو والفيلر والستنج","detailedStatistics":"إحصائيات مفصلة"},"unified":{"title":"النظام الموحد للاستيراد والتصدير","subtitle":"إدارة استيراد وتصدير بيانات المواد الإعلامية","importExport":"استيراد/تصدير","selectFile":"يرجى اختيار ملف","uploadFile":"رفع الملف","exportData":"تصدير البيانات","fileSelected":"تم اختيار الملف","processing":"جاري المعالجة...","success":"تمت العملية بنجاح","error":"حدث خطأ","noFileSelected":"لم يتم اختيار ملف","invalidFileType":"نوع الملف غير صحيح","mediaLibrary":"مكتبة المواد","totalMedia":"إجمالي المواد","searchMedia":"البحث في المواد...","filterByType":"فلترة حسب النوع","loadingMedia":"جاري تحميل المواد...","importSuccess":"تم الاستيراد بنجاح","itemsImported":"مادة تم استيرادها","importError":"حدث خطأ في الاستيراد","exportSuccess":"تم تصدير الملف بنجاح","exportError":"خطأ في التصدير","noMedia":"لا توجد مواد متاحة","result":"النتيجة","searchDescription":"ابحث في الخريطة البرامجية وجدول الإذاعة اليومي بسهولة","noResultsMessage":"لا توجد نتائج للعرض. يرجى تحديد معايير البحث والضغط على زر \\"بحث وإحصاء\\".","searchAndStats":"بحث وإحصاء","dailyScheduleHint":"اختر \\"الجدول اليومي\\" للبحث عن البرومو والفواصل والستينغ","broadcastDayHint":"يوم البث يبدأ في الساعة 08:00 صباحاً","broadcastDayEndHint":"يوم البث ينتهي في الساعة 07:59 صباحاً من اليوم التالي","date":"التاريخ","time":"الوقت","type":"النوع","name":"الاسم","code":"الكود","rerun":"إعادة","temporary":"مؤقت"},"statistics":{"title":"إحصائيات النظام","subtitle":"تقارير وإحصائيات مفصلة","loadingStats":"جاري تحميل الإحصائيات...","totalMedia":"إجمالي المواد","allRegisteredMedia":"جميع المواد المسجلة","totalSegments":"إجمالي السيجمانت","allSegments":"جميع السيجمانت","differentTypes":"أنواع مختلفة","mediaTypes":"أنواع المواد","averageSegments":"متوسط السيجمانت","perMedia":"لكل مادة","distributionByType":"توزيع المواد حسب النوع","distributionByStatus":"توزيع المواد حسب الحالة","recentlyAdded":"المواد المضافة حديثاً","status":{"valid":"صالح للبث","rejectedCensorship":"مرفوض رقابياً - يحتاج مراجعة المحتوى","rejectedTechnical":"مرفوض هندسياً - مشاكل تقنية في الجودة","waiting":"في انتظار المراجعة"}},"addMedia":{"title":"إضافة مادة إعلامية جديدة","subtitle":"بعد حفظ المادة، ستبقى في هذه الصفحة لإضافة مادة جديدة","basicInfo":"المعلومات الأساسية","hardDiskNumber":"رقم الهارد","selectType":"اختر النوع","channel":"القناة","selectChannel":"اختر القناة","selectStatus":"اختر الحالة","source":"المصدر","startDate":"تاريخ البداية","endDate":"تاريخ الانتهاء","segments":"السيجمانت","addSegment":"إضافة سيجمانت","segment":"سيجمانت","segmentCode":"كود السيجمانت","saveAndAddNew":"حفظ وإضافة مادة جديدة","clearFields":"مسح الحقول","description":"وصف المادة","notes":"ملاحظات","additionalNotes":"ملاحظات إضافية","showInSchedule":"إظهار في قائمة الخريطة وجدول الإذاعة","txDescription":"عند تفعيل هذا الخيار، ستظهر المادة في القائمة الجانبية لجدول الخريطة وجدول الإذاعة","episodeNumber":"رقم الحلقة","seasonNumber":"رقم الموسم","partNumber":"رقم الجزء","durationAutoCalculated":"المدة (تحسب تلقائياً)"}}');

/***/ }),

/***/ "(ssr)/./public/locales/en/common.json":
/*!***************************************!*\
  !*** ./public/locales/en/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"navigation":{"dashboard":"Dashboard","mediaList":"Media List","addMedia":"Add Media","weeklySchedule":"Weekly Schedule","dailySchedule":"Daily Schedule","reports":"Reports","unifiedSystem":"Import/Export","adminDashboard":"Users","statistics":"Statistics"},"common":{"welcome":"Welcome","loading":"Loading...","loadingSchedule":"Loading weekly schedule...","loadingData":"Loading data...","save":"Save","cancel":"Cancel","delete":"Delete","edit":"Edit","add":"Add","search":"Search","filter":"Filter","export":"Export","import":"Import","yes":"Yes","no":"No","ok":"OK","close":"Close","back":"Back","next":"Next","previous":"Previous","submit":"Submit","reset":"Reset","clear":"Clear","select":"Select","selectDate":"Select Date","selectTime":"Select Time","actions":"Actions","status":"Status","type":"Type","name":"Name","description":"Description","duration":"Duration","startTime":"Start Time","endTime":"End Time","date":"Date","time":"Time","content":"Content","code":"Code","episode":"Episode","season":"Season","part":"Part","segments":"Segments","available":"Available","unavailable":"Unavailable","active":"Active","inactive":"Inactive","valid":"Valid","invalid":"Invalid","expired":"Expired","pending":"Pending","approved":"Approved","rejected":"Rejected","total":"Total","count":"Count","items":"Items","user":"User","noData":"No Data Available","noResults":"No Results Found","error":"Error","success":"Success","warning":"Warning","info":"Information"},"mediaTypes":{"ALL":"All Types","PROGRAM":"Program","SERIES":"Series","FILM":"Film","SONG":"Song","PROMO":"Promo","STING":"Sting","FILLER":"Filler","NEXT":"Next","NOW":"Now","MINI":"Mini","CROSS":"Cross","سنعود":"We\'ll Be Back","عدنا":"We\'re Back"},"roles":{"ADMIN":"System Administrator","CONTENT_MANAGER":"Content Manager","MEDIA_MANAGER":"Database Manager","SCHEDULER":"Program Scheduler","FULL_VIEWER":"Full View User","DATA_ENTRY":"Data Entry","MAP_SCHEDULER":"Map & Schedule Manager","VIEWER":"Viewer","EDITOR":"Editor","OPERATOR":"Operator"},"roleDescriptions":{"ADMIN":"Full system administration and user management permissions","CONTENT_MANAGER":"Manage content and media materials","MEDIA_MANAGER":"Manage media database","SCHEDULER":"Create and edit program schedules","FULL_VIEWER":"View all data and reports","DATA_ENTRY":"Enter and edit basic data","MAP_SCHEDULER":"Manage program map and schedules","VIEWER":"View basic data only"},"mediaStatus":{"ALL":"All Status","VALID":"Valid","REJECTED_CENSORSHIP":"Rejected - Censorship","REJECTED_TECHNICAL":"Rejected - Technical","EXPIRED":"Expired","HOLD":"On Hold"},"channels":{"DOCUMENTARY":"Documentary","NEWS":"News","OTHER":"Other"},"dashboard":{"title":"Dashboard","subtitle":"Media Management System","totalMedia":"Total Media","activeSchedules":"Active Schedules","todayBroadcast":"Today\'s Broadcast","systemUsers":"System Users","recentActivity":"Recent Activity","quickActions":"Quick Actions","statistics":"Statistics","overview":"Overview"},"media":{"title":"Media Management","addNew":"Add New Media","list":"Media List","details":"Media Details","segments":"Segments","addSegment":"Add Segment","segmentCode":"Segment Code","timeIn":"Time In","timeOut":"Time Out","hardDrive":"Hard Drive","server":"Server","notes":"Notes","startDate":"Start Date","endDate":"End Date","showInTX":"Show in TX","episodeNumber":"Episode Number","seasonNumber":"Season Number","partNumber":"Part Number","totalSegments":"Total Segments","validMedia":"Valid Media","expiredMedia":"Expired Media","pendingMedia":"Pending Media","searchByName":"Search by name or description","searchByCode":"Search by media or segment code","searchPlaceholder":"Search for media...","codePlaceholder":"Search by code...","mediaType":"Media Type","mediaStatus":"Status","sortBy":"Sort By","newest":"Newest First","oldest":"Oldest First","byName":"Name (A-Z)","byType":"Type","searchStats":"Showing {{filtered}} of {{total}} media items","noMediaFound":"No media items found","startAdding":"Start by adding a new media item","exportExcel":"Export Excel","exporting":"Exporting...","searchAndFilter":"Search & Filter","mediaOverview":"View and manage media content","searchFilterExport":"You can search, filter and export data","channel":"Channel","segmentCount":"Segment Count","description":"Description","noCode":"[No Code]","edit":"Edit","delete":"Delete","scrollToTop":"Back to top"},"schedule":{"title":"Schedule Management","weekly":"Weekly Program Schedule","daily":"Daily Broadcast Schedule","import":"Import Schedule","export":"Export Schedule","broadcast":"Broadcast","rerun":"Rerun","prime":"Prime","filler":"Filler","empty":"Empty","addRow":"Add Row","deleteRow":"Delete Row","moveUp":"Move Up","moveDown":"Move Down","showMap":"Show Map","hideMap":"Hide Map","saveChanges":"Save Changes","discardChanges":"Discard Changes","importFromTime":"Import from this time","broadcastTime":"Broadcast Time","programMap":"Program Map","scheduleItems":"Schedule Items","availableMedia":"Available Media","scheduledMedia":"Today\'s Scheduled Programs","weeklySchedule":"Weekly Program Schedule","weeklySubtitle":"Weekly program scheduling","importTitle":"Import Broadcast Schedule","importSubtitle":"Import schedule from specific time","importInstructions":"Select Date → Set Time → Import → Edit Media → Export Excel","importSchedule":"Import Schedule","importInstructionsLong":"Select date and time then click \\"Import from this time\\" to display the schedule","loadingSchedule":"Loading weekly schedule...","selectingDate":"Selecting date...","mediaList":"Media List","addTempMedia":"Add Temporary Media","mediaName":"Media name...","duration":"Duration (e.g.: 01:30:00)","notes":"Notes (optional)...","add":"Add","updateReruns":"Update Reruns","allTypes":"All Types","searchMedia":"Search media...","resultsCount":"{{count}} of {{total}} media","noMedia":"No media found","changeFilter":"Try changing filter or search","addNewMedia":"Add new media from user page","deleteTempMedia":"Delete temporary media","liveProgram":"Live","pendingDelivery":"Pending Delivery","temporary":"Temporary","broadcastSchedule":"Broadcast Schedule","exportSchedule":"Export Schedule","selectedWeek":"Selected Week","previousWeek":"← Previous Week","nextWeek":"Next Week →","time":"Time","rerunIndicator":"Rerun - can be deleted for editing","hideSchedule":"Hide Schedule","showSchedule":"Show Schedule","unknown":"Unknown","season":"Season","episode":"Episode","part":"Part","confirmDelete":"Are you sure you want to delete {{type}}: \\"{{name}}\\"?","deleteWarningOriginal":"Warning: Deleting original media will delete all its reruns","deleteWarningRerun":"Warning: Deleting rerun will not affect original media","deleteWarningTemp":"Temporary media will be deleted from schedule","originalMaterial":"Original Material","rerunMaterial":"Rerun","tempMaterial":"Temporary Media","timeConflict":"Time conflict detected! Choose another time.","enterMediaName":"Please enter media name","confirmDeleteTemp":"Do you want to delete this temporary media?","deleteMediaTitle":"🗑️ Delete Media:","deleteOriginalInfo":"Original Media: Permanent deletion with all reruns","deleteRerunInfo":"Reruns: Delete leaving field empty for editing","deleteConfirmInfo":"Confirmation will appear before deletion","confirmDeleteSegment":"Are you sure you want to delete this segment?","usageInstructions":"📋 Usage Instructions:","addMediaTitle":"🎯 Adding Media:","addMediaInstruction1":"Drag media from the right panel to the schedule","addMediaInstruction2":"🔄 Drag media within the schedule to copy to other time slots","addMediaInstruction3":"🎬 Use type filter to filter by media type","addMediaInstruction4":"🔍 Use search to find media quickly","primeTimeTitle":"🌟 Original Media (Prime Time):","primeTimeSchedule1":"Sunday-Wednesday: 18:00-00:00","primeTimeSchedule2":"Thursday-Saturday: 18:00-02:00","primeTimeColor":"🟡 Golden color in schedule","rerunsTitle":"♻️ Automatic Reruns (Two Parts):","rerunsSchedule1":"Sunday-Wednesday:","rerunsSchedule2":"Thursday-Saturday:","rerunsPart1Sun":"Part 1: Same column 00:00-07:59","rerunsPart2Sun":"Part 2: Next column 08:00-17:59","rerunsPart1Thu":"Part 1: Same column 02:00-07:59","rerunsPart2Thu":"Part 2: Next column 08:00-17:59","rerunsColor":"🔘 Gray color - can be deleted for editing","dateManagementTitle":"📅 Date Management:","dateManagementInfo":"Use calendar and buttons to navigate between weeks • Each week is saved separately","importantNoteTitle":"💡 Important Note:","importantNoteInfo":"When adding few media items (1-3 items) in prime time, they will appear with time gaps in reruns to avoid excessive repetition. Add more media for greater variety.","weeklyScheduleTitle":"Weekly Schedule","noWeeklyData":"No data available for weekly schedule","types":{"program":"Program","series":"Series","film":"Film","song":"Song","sting":"Sting","fillIn":"Fill In","filler":"Filler","promo":"Promo","next":"Next","now":"Now","snawod":"We\'ll Be Back","odna":"We\'re Back","mini":"Mini","cross":"Cross"},"startTime":"Start Time"},"days":{"sunday":"Sunday","monday":"Monday","tuesday":"Tuesday","wednesday":"Wednesday","thursday":"Thursday","friday":"Friday","saturday":"Saturday"},"months":{"january":"January","february":"February","march":"March","april":"April","may":"May","june":"June","july":"July","august":"August","september":"September","october":"October","november":"November","december":"December"},"auth":{"login":"Login","logout":"Logout","username":"Username","password":"Password","loginButton":"Sign In","loginError":"Login Error","accessDenied":"Access denied","createUserError":"Error creating user","deleteUserSuccess":"User deleted successfully!","deleteUserError":"Error deleting user","fillRequiredFields":"Please fill all required fields","updateUserSuccess":"User updated successfully!","updateUserError":"Error updating user","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","permissions":"Permissions","allPermissions":"All Permissions","manageMedia":"Manage Media","manageSchedules":"Manage Schedules","viewMedia":"View Media","viewSchedules":"View Schedules","viewMap":"View Map","viewBroadcast":"View Broadcast","manageMap":"Manage Map","roles":{"admin":"System Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Scheduler","fullViewer":"Full View User","dataEntry":"Data Entry","mapScheduler":"Map & Schedule Manager","viewer":"Viewer","adminDesc":"Full permissions for all system parts + user management","contentManagerDesc":"Full media and schedule management (without user management)","mediaManagerDesc":"Media management only (add, edit, delete)","schedulerDesc":"Broadcast schedules and program map management only","fullViewerDesc":"View entire application without editing or adding capabilities","dataEntryDesc":"Data entry and editing only without viewing other parts of the application","mapSchedulerDesc":"Map and daily broadcast schedule management with database viewing without editing","viewerDesc":"View content only without editing or adding capabilities"},"invalidCredentials":"Invalid credentials","welcomeBack":"Welcome back","pleaseLogin":"Please login to continue","userRoles":"User Roles","adminDesc":"Full permissions","contentManagerDesc":"Media management","schedulerDesc":"Schedule management","viewerDesc":"View only","sessionExpired":"Session expired","insufficientPermissions":"Insufficient permissions","status":{"valid":"Valid","rejectedCensorship":"Rejected - Censorship","rejectedTechnical":"Rejected - Technical","waiting":"Waiting"},"segments":"segments"},"admin":{"title":"User Management","subtitle":"Add and edit users","users":"Users","permissions":"Permissions","addUser":"Add User","editUser":"Edit User","deleteUser":"Delete User","userRole":"User Role","userStatus":"User Status","lastLogin":"Last Login","createdAt":"Created At","updatedAt":"Updated At","activeUsers":"Active Users","inactiveUsers":"Inactive Users","totalUsers":"Total Users","loadingData":"Loading data...","userManagement":"User Management","addNewUser":"Add New User","username":"Username","password":"Password","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","status":"Status","actions":"Actions","active":"Active","inactive":"Inactive","createUser":"Create User","cancel":"Cancel","saveChanges":"Save Changes","edit":"Edit","delete":"Delete","noUsers":"No users found","addUsersMessage":"Add new users using the \\"Add New User\\" button","rolesExplanation":"Roles and Permissions Explanation","allPermissions":"All Permissions","mediaManagement":"Media Management","scheduleManagement":"Schedule Management","viewMedia":"View Media","viewSchedules":"View Schedules","viewMap":"View Map","viewBroadcast":"View Broadcast","mapManagement":"Map Management","permissionsLabel":"Permissions:","noLoginYet":"No login yet","editingUser":"Edit User","passwordNote":"(leave empty to keep current)","confirmDelete":"Are you sure you want to delete this user?","userCreated":"User created successfully!","userUpdated":"User updated successfully!","userDeleted":"User deleted successfully!","fillRequired":"Please fill all required fields","createError":"Error creating user","updateError":"Error updating user","deleteError":"Error deleting user","fetchError":"Error fetching user data","serverError":"Server connection error","roles":{"admin":"System Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Program Scheduler","fullViewer":"Full Viewer","dataEntry":"Data Entry","mapScheduler":"Map & Schedule Manager","viewer":"Viewer","adminDesc":"Full system administration and user management permissions","contentManagerDesc":"Manage content and media materials","mediaManagerDesc":"Manage media database","schedulerDesc":"Create and edit program schedules","fullViewerDesc":"View all data and reports","dataEntryDesc":"Enter and edit basic data","mapSchedulerDesc":"Manage program map and schedules","viewerDesc":"View basic data only"}},"permissions":{"MEDIA_READ":"Read Media","MEDIA_CREATE":"Create Media","MEDIA_UPDATE":"Update Media","MEDIA_DELETE":"Delete Media","SCHEDULE_READ":"Read Schedules","SCHEDULE_CREATE":"Create Schedules","SCHEDULE_UPDATE":"Update Schedules","SCHEDULE_DELETE":"Delete Schedules","USER_MANAGEMENT":"User Management","SYSTEM_ADMIN":"System Administration"},"stats":{"totalMedia":"Total Media","validMedia":"Valid Media","maintenanceMedia":"Under Maintenance","activeUsers":"Active Users","efficiency":"Overall Efficiency","operationalCost":"Operational Cost","processingTime":"Average Processing Time","activeOperations":"Active Operations","growthRate":"Growth Rate","healthRate":"Health Rate","issueRate":"Issue Rate","activityRate":"Activity Rate","improvement":"Improvement","dailyAverage":"Daily Average","loadingDummyData":"Loading dummy data"},"messages":{"success":{"mediaAdded":"Media added successfully","mediaUpdated":"Media updated successfully","mediaDeleted":"Media deleted successfully","exportSuccess":"Export completed successfully","importSuccess":"Import completed successfully","scheduleUpdated":"Schedule updated successfully","userCreated":"User created successfully","userUpdated":"User updated successfully","userDeleted":"User deleted successfully","changesSaved":"Changes saved successfully"},"error":{"serverConnection":"Server connection error","mediaNotFound":"Media not found","invalidData":"Invalid data","permissionDenied":"Permission denied","exportFailed":"Export failed","importFailed":"Import failed","unknownError":"Unknown error occurred","timeFormatError":"Time format error","calculationError":"Calculation error"},"info":{"loading":"Loading...","saving":"Saving...","processing":"Processing...","exporting":"Exporting...","importing":"Importing..."},"admin":{"title":"User Management","subtitle":"Manage system users and permissions","loadingData":"Loading data...","userManagement":"User Management","addNewUser":"Add New User","username":"Username","password":"Password","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","createUser":"Create User","cancel":"Cancel","lastLogin":"Last Login","actions":"Actions","noLoginYet":"No login yet","confirmDelete":"Are you sure you want to delete this user?","noUsers":"No users found","addUsersMessage":"Start by adding new users to the system","rolesExplanation":"Roles and Permissions","permissions":"Permissions","allPermissions":"All Permissions","manageMedia":"Manage Media","manageSchedules":"Manage Schedules","viewSchedules":"View Schedules","viewMedia":"View Media","viewMap":"View Map","viewBroadcast":"View Broadcast","manageMap":"Manage Map","editingUser":"Editing User","passwordNote":"(leave empty to keep current)","status":"Status","active":"Active","inactive":"Inactive","saveChanges":"Save Changes","roles":{"admin":"Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Scheduler","fullViewer":"Full Viewer","dataEntry":"Data Entry","mapScheduler":"Map Scheduler","viewer":"Viewer","adminDesc":"Full system access and user management","contentManagerDesc":"Manage media content and schedules","mediaManagerDesc":"Manage media library and content","schedulerDesc":"Create and manage broadcast schedules","fullViewerDesc":"View all system data and reports","dataEntryDesc":"Add and edit media content","mapSchedulerDesc":"Manage program maps and schedules","viewerDesc":"Basic viewing permissions"}}},"home":{"title":"Prime-X","subtitle":"Media Management System","loading":"Loading...","autoRedirect":"Or wait for automatic redirect to dashboard","quickNavigation":"Quick Navigation","dashboard":"Dashboard","dailySchedule":"Daily Broadcast Schedule","weeklySchedule":"Program Schedule","mediaList":"Media List","addMedia":"Add Media","adminPanel":"User Management"},"reports":{"title":"Broadcast Reports","subtitle":"Search and analyze broadcast content","searchFilters":"Search Filters","mediaType":"Media Type","allTypes":"All Types","mediaName":"Media Name","mediaCode":"Media Code","source":"Source","both":"Both","weekly":"Weekly Schedule","daily":"Daily Schedule","dateFrom":"From Date","dateTo":"To Date","search":"Search","exportExcel":"Export Excel","showStatistics":"Show Statistics","hideStatistics":"Hide Statistics","searchResults":"Search Results","noResults":"No results found","searchMessage":"Use the filters above to search for broadcast content","resultsCount":"{{count}} results","statistics":"Media Statistics","totalItems":"Total Items","totalDuration":"Total Duration","count":"Count","duration":"Duration","percentage":"Percentage","loadingMedia":"Loading media...","searchError":"Search error","exportError":"Export error","exportSuccess":"Export successful","date":"Date","time":"Time","type":"Type","name":"Name","code":"Code","rerun":"Rerun","temporary":"Temporary","original":"Original","programsFilmsSeries":"Programs, Films & Series","promosFillersSting":"Promos, Fillers & Stings","detailedStatistics":"Detailed Statistics"},"unified":{"title":"Unified Import/Export System","subtitle":"Manage import and export of media data","importExport":"Import/Export","selectFile":"Please select a file","uploadFile":"Upload File","exportData":"Export Data","fileSelected":"File Selected","processing":"Processing...","success":"Operation Successful","error":"An Error Occurred","noFileSelected":"No File Selected","invalidFileType":"Invalid File Type","mediaLibrary":"Media Library","totalMedia":"Total Media","searchMedia":"Search media...","filterByType":"Filter by Type","loadingMedia":"Loading media...","importSuccess":"Import successful","itemsImported":"items imported","importError":"Import error occurred","exportSuccess":"File exported successfully","exportError":"Export error","noMedia":"No media available","result":"Result","searchDescription":"Search the program schedule and daily broadcast schedule easily","noResultsMessage":"No results to display. Please set search criteria and click \\"Search & Statistics\\".","searchAndStats":"Search & Statistics","dailyScheduleHint":"Choose \\"Daily Schedule\\" to search for promos, fillers, and stings","broadcastDayHint":"Broadcast day starts at 08:00 AM","broadcastDayEndHint":"Broadcast day ends at 07:59 AM next day","date":"Date","time":"Time","type":"Type","name":"Name","code":"Code","rerun":"Rerun","temporary":"Temporary"},"statistics":{"title":"System Statistics","subtitle":"Detailed reports and statistics","loadingStats":"Loading statistics...","totalMedia":"Total Media","allRegisteredMedia":"All registered media","totalSegments":"Total Segments","allSegments":"All segments","differentTypes":"Different Types","mediaTypes":"Media types","averageSegments":"Average Segments","perMedia":"Per media","distributionByType":"Distribution by Type","distributionByStatus":"Distribution by Status","recentlyAdded":"Recently Added","status":{"valid":"Valid for broadcast","rejectedCensorship":"Rejected (Censorship) - Content review needed","rejectedTechnical":"Rejected (Technical) - Quality issues","waiting":"Awaiting review"}},"addMedia":{"title":"Add New Media","subtitle":"After saving, you\'ll stay on this page to add another media","basicInfo":"Basic Information","hardDiskNumber":"Hard Disk Number","selectType":"Select Type","channel":"Channel","selectChannel":"Select Channel","selectStatus":"Select Status","source":"Source","startDate":"Start Date","endDate":"End Date","segments":"Segments","addSegment":"Add Segment","segment":"Segment","segmentCode":"Segment Code","saveAndAddNew":"Save and Add New","clearFields":"Clear Fields","description":"Media Description","notes":"Notes","additionalNotes":"Additional notes","showInSchedule":"Show in Schedule and Broadcast Lists","txDescription":"When enabled, this media will appear in the sidebar of schedule and broadcast tables","episodeNumber":"Episode Number","seasonNumber":"Season Number","partNumber":"Part Number","durationAutoCalculated":"Duration (Auto-calculated)"}}');

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5b8c52149260\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjViOGM1MjE0OTI2MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/i18n */ \"(ssr)/./src/lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n // Initialize i18n\nfunction RootLayout({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RootLayout.useEffect\": ()=>{\n            // Set initial language and direction from localStorage or default to Arabic\n            const savedLang = localStorage.getItem('language') || 'ar';\n            document.documentElement.lang = savedLang;\n            document.documentElement.dir = savedLang === 'ar' ? 'rtl' : 'ltr';\n            // Add language class to body for CSS targeting\n            document.body.className = `lang-${savedLang}`;\n            // Set font family based on language\n            if (savedLang === 'ar') {\n                document.body.style.fontFamily = 'Cairo, Arial, sans-serif';\n            } else {\n                document.body.style.fontFamily = 'Inter, Arial, sans-serif';\n            }\n        }\n    }[\"RootLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"نظام إدارة المحتوى الإعلامي | Media Management System\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"نظام متكامل لإدارة المحتوى الإعلامي والخريطة البرامجية | Integrated Media Content Management System\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"black-translucent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                style: {\n                    margin: 0,\n                    padding: 0,\n                    fontFamily: 'Cairo, Inter, Arial, sans-serif'\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MediaListPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(ssr)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(ssr)/./src/hooks/useTranslatedToast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction MediaListPage() {\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast)();\n    const [mediaItems, setMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [codeSearchTerm, setCodeSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScrollToTop, setShowScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            fetchMediaItems();\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    // مراقبة التمرير لإظهار/إخفاء زر العودة لأعلى\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"MediaListPage.useEffect.handleScroll\": ()=>{\n                    setShowScrollToTop(window.scrollY > 300);\n                }\n            }[\"MediaListPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"MediaListPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"MediaListPage.useEffect\"];\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            filterAndSortItems();\n        }\n    }[\"MediaListPage.useEffect\"], [\n        mediaItems,\n        searchTerm,\n        codeSearchTerm,\n        selectedType,\n        selectedStatus,\n        sortBy\n    ]);\n    // دالة العودة لأعلى الصفحة\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    const fetchMediaItems = async ()=>{\n        try {\n            const response = await fetch('/api/media');\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(result.data);\n            } else {\n                setError(result.error);\n            }\n        } catch (error) {\n            console.error('Error fetching media items:', error);\n            setError(t('messages.networkError'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterAndSortItems = ()=>{\n        let filtered = [\n            ...mediaItems\n        ];\n        // البحث بالاسم\n        if (searchTerm) {\n            filtered = filtered.filter((item)=>item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // البحث بالكود (في السيجمانت)\n        if (codeSearchTerm) {\n            filtered = filtered.filter((item)=>{\n                // البحث في معرف المادة\n                if (item.id.toLowerCase().includes(codeSearchTerm.toLowerCase())) {\n                    return true;\n                }\n                // البحث في أكواد السيجمانت\n                if (item.segments && item.segments.length > 0) {\n                    return item.segments.some((segment)=>segment.code && segment.code.toLowerCase().includes(codeSearchTerm.toLowerCase()));\n                }\n                return false;\n            });\n        }\n        // فلترة بالنوع\n        if (selectedType !== 'ALL') {\n            filtered = filtered.filter((item)=>item.type === selectedType);\n        }\n        // فلترة بالحالة\n        if (selectedStatus !== 'ALL') {\n            filtered = filtered.filter((item)=>item.status === selectedStatus);\n        }\n        // الترتيب\n        switch(sortBy){\n            case 'newest':\n                filtered.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                break;\n            case 'oldest':\n                filtered.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n                break;\n            case 'name':\n                filtered.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n                break;\n            case 'type':\n                filtered.sort((a, b)=>a.type.localeCompare(b.type));\n                break;\n        }\n        setFilteredItems(filtered);\n    };\n    const deleteMediaItem = async (id)=>{\n        if (!confirm(t('messages.confirmDelete'))) return;\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = `token_${user.id || 'unknown'}_${user.role || 'unknown'}`;\n            console.log('Sending delete request with token:', tokenWithRole);\n            const response = await fetch(`/api/media?id=${id}`, {\n                method: 'DELETE',\n                headers: {\n                    'Authorization': `Bearer ${tokenWithRole}`\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(mediaItems.filter((item)=>item.id !== id));\n                showSuccessToast('mediaDeleted');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error deleting media item:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const exportToExcel = async ()=>{\n        setIsExporting(true);\n        try {\n            console.log('🚀 بدء تصدير قاعدة البيانات...');\n            // إرسال الفلاتر الحالية مع طلب التصدير\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (codeSearchTerm) params.append('codeSearch', codeSearchTerm);\n            if (selectedType !== 'ALL') params.append('type', selectedType);\n            if (selectedStatus !== 'ALL') params.append('status', selectedStatus);\n            const apiUrl = `/api/export-unified${params.toString() ? '?' + params.toString() : ''}`;\n            console.log('📊 تصدير مع الفلاتر:', apiUrl);\n            const response = await fetch(apiUrl);\n            if (!response.ok) {\n                throw new Error(t('messages.exportError'));\n            }\n            // الحصول على الملف كـ blob\n            const blob = await response.blob();\n            // إنشاء رابط التحميل\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = downloadUrl;\n            // تحديد اسم الملف\n            const fileName = `Media_Database_${new Date().toISOString().split('T')[0]}.xlsx`;\n            link.download = fileName;\n            // تحميل الملف\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            // تنظيف الذاكرة\n            window.URL.revokeObjectURL(downloadUrl);\n            console.log('✅ تم تصدير قاعدة البيانات بنجاح');\n            showSuccessToast('exportSuccess');\n        } catch (error) {\n            console.error('❌ خطأ في التصدير:', error);\n            showErrorToast('exportFailed');\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const getTypeLabel = (type)=>{\n        return t(`mediaTypes.${type}`) || type;\n    };\n    const getStatusLabel = (status)=>{\n        return t(`mediaStatus.${status}`) || status;\n    };\n    const getChannelLabel = (channel)=>{\n        return t(`channels.${channel}`) || channel;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"⏳ \",\n                    t('common.loading')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"❌ \",\n                    t('common.error'),\n                    \": \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'MEDIA_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: t('media.list'),\n            subtitle: t('media.title'),\n            icon: \"\\uD83C\\uDFAC\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"media-header-card\",\n                    style: {\n                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                        padding: 'clamp(15px, 3vw, 20px)',\n                        borderRadius: '12px',\n                        marginBottom: 'clamp(20px, 4vw, 25px)',\n                        color: 'white',\n                        textAlign: 'center',\n                        boxShadow: '0 6px 20px rgba(102, 126, 234, 0.3)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: '0 0 10px 0',\n                                fontSize: 'clamp(1.1rem, 3vw, 1.3rem)'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCCA \",\n                                t('media.list')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: '0 0 8px 0',\n                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                opacity: 0.9\n                            },\n                            children: t('media.mediaOverview')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: '0',\n                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)',\n                                opacity: 0.8\n                            },\n                            children: [\n                                \"✨ \",\n                                t('media.searchFilterExport')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"action-buttons\",\n                    style: {\n                        display: 'flex',\n                        gap: 'clamp(10px, 2vw, 15px)',\n                        justifyContent: 'center',\n                        marginBottom: 'clamp(20px, 4vw, 25px)',\n                        flexWrap: 'wrap'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: exportToExcel,\n                        disabled: isExporting,\n                        style: {\n                            background: isExporting ? 'linear-gradient(45deg, #6c757d, #5a6268)' : 'linear-gradient(45deg, #17a2b8, #138496)',\n                            color: 'white',\n                            padding: 'clamp(10px, 2vw, 12px) clamp(20px, 4vw, 25px)',\n                            borderRadius: '25px',\n                            border: 'none',\n                            fontWeight: 'bold',\n                            cursor: isExporting ? 'not-allowed' : 'pointer',\n                            boxShadow: '0 4px 15px rgba(23,162,184,0.3)',\n                            fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                            minHeight: '44px',\n                            transition: 'all 0.2s ease'\n                        },\n                        children: isExporting ? '⏳ ' + t('media.exporting') : '📊 ' + t('media.exportExcel')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"search-filter-section\",\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: 'clamp(20px, 4vw, 25px)',\n                        marginBottom: 'clamp(20px, 4vw, 25px)',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: '#f3f4f6',\n                                marginBottom: 'clamp(15px, 3vw, 20px)',\n                                fontSize: 'clamp(1.1rem, 3vw, 1.3rem)'\n                            },\n                            children: [\n                                \"\\uD83D\\uDD0D \",\n                                t('media.searchAndFilter')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"search-grid\",\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(min(250px, 100%), 1fr))',\n                                gap: 'clamp(12px, 2.5vw, 15px)'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)'\n                                            },\n                                            children: [\n                                                t('media.searchByName'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('media.searchPlaceholder'),\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: 'clamp(8px, 2vw, 10px)',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937',\n                                                minHeight: '40px',\n                                                boxSizing: 'border-box'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"search-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)'\n                                            },\n                                            children: [\n                                                t('media.searchByCode'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('media.codePlaceholder'),\n                                            value: codeSearchTerm,\n                                            onChange: (e)=>setCodeSearchTerm(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: 'clamp(8px, 2vw, 10px)',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                                direction: 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937',\n                                                minHeight: '40px',\n                                                boxSizing: 'border-box'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 377,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"filter-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)'\n                                            },\n                                            children: [\n                                                t('media.mediaType'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedType,\n                                            onChange: (e)=>setSelectedType(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: 'clamp(8px, 2vw, 10px)',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937',\n                                                minHeight: '40px',\n                                                boxSizing: 'border-box'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ALL\",\n                                                    children: t('mediaTypes.ALL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"FILM\",\n                                                    children: t('mediaTypes.FILM')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 423,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SERIES\",\n                                                    children: t('mediaTypes.SERIES')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"PROGRAM\",\n                                                    children: t('mediaTypes.PROGRAM')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SONG\",\n                                                    children: t('mediaTypes.SONG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"FILLER\",\n                                                    children: t('mediaTypes.FILLER')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"STING\",\n                                                    children: t('mediaTypes.STING')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"PROMO\",\n                                                    children: t('mediaTypes.PROMO')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NEXT\",\n                                                    children: t('mediaTypes.NEXT')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NOW\",\n                                                    children: t('mediaTypes.NOW')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"سنعود\",\n                                                    children: t('mediaTypes.سنعود')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"عدنا\",\n                                                    children: t('mediaTypes.عدنا')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MINI\",\n                                                    children: t('mediaTypes.MINI')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CROSS\",\n                                                    children: t('mediaTypes.CROSS')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"filter-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)'\n                                            },\n                                            children: [\n                                                t('media.mediaStatus'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedStatus,\n                                            onChange: (e)=>setSelectedStatus(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: 'clamp(8px, 2vw, 10px)',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937',\n                                                minHeight: '40px',\n                                                boxSizing: 'border-box'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ALL\",\n                                                    children: t('mediaStatus.ALL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"VALID\",\n                                                    children: t('mediaStatus.VALID')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"REJECTED_CENSORSHIP\",\n                                                    children: t('mediaStatus.REJECTED_CENSORSHIP')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"REJECTED_TECHNICAL\",\n                                                    children: t('mediaStatus.REJECTED_TECHNICAL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"EXPIRED\",\n                                                    children: t('mediaStatus.EXPIRED')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"HOLD\",\n                                                    children: t('mediaStatus.HOLD')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 440,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"filter-field\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: 'clamp(0.8rem, 2vw, 0.9rem)'\n                                            },\n                                            children: [\n                                                t('media.sortBy'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: 'clamp(8px, 2vw, 10px)',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: 'clamp(0.9rem, 2.5vw, 1rem)',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937',\n                                                minHeight: '40px',\n                                                boxSizing: 'border-box'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"newest\",\n                                                    children: t('media.newest')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"oldest\",\n                                                    children: t('media.oldest')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: t('media.byName')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"type\",\n                                                    children: t('media.byType')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '15px',\n                                padding: '10px',\n                                background: '#1f2937',\n                                borderRadius: '8px',\n                                textAlign: 'center',\n                                color: '#d1d5db',\n                                border: '1px solid #6b7280'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCCA \",\n                                t('media.searchStats', {\n                                    filtered: filteredItems.length,\n                                    total: mediaItems.length\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 335,\n                    columnNumber: 9\n                }, this),\n                filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: '50px',\n                        textAlign: 'center',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: '#d1d5db',\n                                fontSize: '1.5rem'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCED \",\n                                t('media.noMediaFound')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: '#a0aec0',\n                                marginTop: '10px'\n                            },\n                            children: t('media.startAdding')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 524,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 514,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gap: '20px'\n                    },\n                    children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#4a5568',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: '1fr auto',\n                                    gap: '20px',\n                                    alignItems: 'start'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    marginBottom: '15px',\n                                                    fontSize: '1.4rem'\n                                                },\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                    gap: '15px',\n                                                    marginBottom: '15px',\n                                                    color: '#d1d5db'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('common.type'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getTypeLabel(item.type)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('media.channel'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 548,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getChannelLabel(item.channel)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('common.status'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getStatusLabel(item.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('media.segmentCount'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 554,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            item.segments.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    marginBottom: '10px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t('media.description'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \" \",\n                                                    item.description\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.segments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '15px',\n                                                    color: '#d1d5db'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t('media.segments'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'grid',\n                                                            gap: '8px',\n                                                            marginTop: '8px'\n                                                        },\n                                                        children: item.segments.map((segment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: '#1f2937',\n                                                                    padding: '8px 12px',\n                                                                    borderRadius: '8px',\n                                                                    fontSize: '0.9rem',\n                                                                    color: '#d1d5db',\n                                                                    border: '1px solid #6b7280'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            \"#\",\n                                                                            segment.segmentNumber\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                        lineNumber: 577,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    \" -\",\n                                                                    segment.code && segment.code.trim() !== '' ? ` ${segment.code} - ` : ` [${t('media.noCode')}] - `,\n                                                                    segment.timeIn,\n                                                                    \" → \",\n                                                                    segment.timeOut,\n                                                                    \" (\",\n                                                                    segment.duration,\n                                                                    \")\"\n                                                                ]\n                                                            }, `${item.id}_segment_${index}`, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 565,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 538,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            flexDirection: 'column',\n                                            gap: '10px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    // توجيه لصفحة التعديل مع معرف المادة\n                                                    window.location.href = `/edit-media?id=${item.id}`;\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem',\n                                                    marginBottom: '5px'\n                                                },\n                                                children: [\n                                                    \"✏️ \",\n                                                    t('media.edit')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>deleteMediaItem(item.id),\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDDD1️ \",\n                                                    t('media.delete')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 588,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 17\n                            }, this)\n                        }, item.id, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 529,\n                    columnNumber: 11\n                }, this),\n                showScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: scrollToTop,\n                    style: {\n                        position: 'fixed',\n                        bottom: '30px',\n                        right: '30px',\n                        width: '60px',\n                        height: '60px',\n                        borderRadius: '50%',\n                        background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                        color: 'white',\n                        border: 'none',\n                        cursor: 'pointer',\n                        fontSize: '24px',\n                        boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',\n                        zIndex: 1000,\n                        transition: 'all 0.3s ease',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                    },\n                    onMouseEnter: (e)=>{\n                        e.currentTarget.style.transform = 'scale(1.1)';\n                        e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 123, 255, 0.4)';\n                    },\n                    onMouseLeave: (e)=>{\n                        e.currentTarget.style.transform = 'scale(1)';\n                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 123, 255, 0.3)';\n                    },\n                    title: t('media.scrollToTop'),\n                    children: \"⬆️\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 631,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/media-list/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \n\n\nfunction AuthGuard({ children, requiredPermissions = [], requiredRole, fallbackComponent }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    // المدير له صلاحيات كاملة\n    if (user.role === 'ADMIN') {\n      console.log('✅ المستخدم هو مدير النظام - تم منح جميع الصلاحيات');\n      return true;\n    }\n\n    // التحقق من الدور المطلوب\n    if (role && user.role !== role) {\n      console.log(`❌ المستخدم ليس لديه الدور المطلوب: ${role}`);\n      return false;\n    }\n\n    // التحقق من الصلاحيات المطلوبة\n    if (permissions.length > 0) {\n      const userPermissions = getUserPermissions(user.role);\n      console.log('🔍 التحقق من الصلاحيات:', {\n        required: permissions,\n        userHas: userPermissions\n      });\n      \n      const hasAllPermissions = permissions.every(permission => \n        userPermissions.includes(permission) || userPermissions.includes('ALL')\n      );\n      \n      if (!hasAllPermissions) {\n        console.log('❌ المستخدم ليس لديه جميع الصلاحيات المطلوبة');\n      } else {\n        console.log('✅ المستخدم لديه جميع الصلاحيات المطلوبة');\n      }\n      \n      return hasAllPermissions;\n    }\n\n    return true;\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user?.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user?.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    if (!user) return false;\n    if (user.role === 'ADMIN') return true;\n\n    const userPermissions = getUserPermissions(user.role);\n    return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: user?.role === 'ADMIN',\n        isMediaManager: user?.role === 'MEDIA_MANAGER',\n        isScheduler: user?.role === 'SCHEDULER',\n        isViewer: user?.role === 'VIEWER',\n        isFullViewer: user?.role === 'FULL_VIEWER',\n        isDataEntry: user?.role === 'DATA_ENTRY',\n        isMapScheduler: user?.role === 'MAP_SCHEDULER',\n        isContentManager: user?.role === 'CONTENT_MANAGER'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BdXRoR3VhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ0E7QUFtQnJDLFNBQVNHLFVBQVUsRUFDeEJDLFFBQVEsRUFDUkMsc0JBQXNCLEVBQUUsRUFDeEJDLFlBQVksRUFDWkMsaUJBQWlCLEVBQ0Y7SUFDZixNQUFNQyxTQUFTTiwwREFBU0E7SUFDeEIsTUFBTSxDQUFDTyxNQUFNQyxRQUFRLEdBQUdULCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ1UsV0FBV0MsYUFBYSxHQUFHWCwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNZLFdBQVdDLGFBQWEsR0FBR2IsK0NBQVFBLENBQUM7SUFFM0NELGdEQUFTQTsrQkFBQztZQUNSZTtRQUNGOzhCQUFHLEVBQUU7SUFFTCxNQUFNQSxZQUFZO1FBQ2hCLElBQUk7WUFDRixpREFBaUQ7WUFDakQsTUFBTUMsV0FBV0MsYUFBYUMsT0FBTyxDQUFDO1lBQ3RDLE1BQU1DLFFBQVFGLGFBQWFDLE9BQU8sQ0FBQztZQUVuQyxJQUFJLENBQUNGLFlBQVksQ0FBQ0csT0FBTztnQkFDdkJYLE9BQU9ZLElBQUksQ0FBQztnQkFDWjtZQUNGO1lBRUEsTUFBTUMsYUFBYUMsS0FBS0MsS0FBSyxDQUFDUDtZQUM5Qk4sUUFBUVc7WUFFUixzQkFBc0I7WUFDdEIsTUFBTUcsU0FBU0MsaUJBQWlCSixZQUFZaEIscUJBQXFCQztZQUNqRVEsYUFBYVU7WUFFYixJQUFJLENBQUNBLFVBQVVqQixzQkFBc0JtQixXQUFXO2dCQUM5Q2xCLE9BQU9ZLElBQUksQ0FBQztZQUNkO1FBRUYsRUFBRSxPQUFPTyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxxQkFBcUJBO1lBQ25DbkIsT0FBT1ksSUFBSSxDQUFDO1FBQ2QsU0FBVTtZQUNSUixhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU1hLG1CQUFtQixDQUFDaEIsTUFBWW9CLGFBQXVCQztRQUMzREYsUUFBUUcsR0FBRyxDQUFDO1FBQ1osT0FBTyxNQUFNLCtEQUErRDtJQUU1RTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFtQ0EsR0FDRjtJQUVBLE1BQU1DLHFCQUFxQixDQUFDRjtRQUMxQixNQUFNRyxrQkFBK0M7WUFDbkQsU0FBUztnQkFBQzthQUFNO1lBQ2hCLG1CQUFtQjtnQkFBQztnQkFBZ0I7Z0JBQWM7Z0JBQWdCO2dCQUFnQjtnQkFBbUI7Z0JBQWlCO2dCQUFtQjthQUFrQjtZQUMzSixpQkFBaUI7Z0JBQUM7Z0JBQWdCO2dCQUFjO2dCQUFnQjtnQkFBZ0I7YUFBZ0I7WUFDaEcsYUFBYTtnQkFBQztnQkFBbUI7Z0JBQWlCO2dCQUFtQjtnQkFBbUI7YUFBYTtZQUNyRyxlQUFlO2dCQUFDO2dCQUFjO2dCQUFpQjtnQkFBWTtnQkFBa0I7Z0JBQWU7YUFBaUI7WUFDN0csY0FBYztnQkFBQztnQkFBZ0I7Z0JBQWM7Z0JBQWdCO2FBQWU7WUFDNUUsaUJBQWlCO2dCQUFDO2dCQUFjO2dCQUFZO2dCQUFjO2dCQUFjO2dCQUFtQjtnQkFBaUI7Z0JBQW1CO2dCQUFtQjthQUFhO1lBQy9KLFVBQVU7Z0JBQUM7Z0JBQWM7YUFBZ0I7UUFDM0M7UUFFQSxPQUFPQSxlQUFlLENBQUNILEtBQUssSUFBSSxFQUFFO0lBQ3BDO0lBRUEsSUFBSW5CLFdBQVc7UUFDYixxQkFDRSw4REFBQ3VCO1lBQUlDLE9BQU87Z0JBQ1ZDLFdBQVc7Z0JBQ1hDLFlBQVk7Z0JBQ1pDLFNBQVM7Z0JBQ1RDLFlBQVk7Z0JBQ1pDLGdCQUFnQjtnQkFDaEJDLFlBQVk7WUFDZDtzQkFDRSw0RUFBQ1A7Z0JBQUlDLE9BQU87b0JBQ1ZFLFlBQVk7b0JBQ1pLLGNBQWM7b0JBQ2RDLFNBQVM7b0JBQ1RDLFdBQVc7b0JBQ1hDLFdBQVc7Z0JBQ2I7O2tDQUNFLDhEQUFDWDt3QkFBSUMsT0FBTzs0QkFDVlcsT0FBTzs0QkFDUEMsUUFBUTs0QkFDUkMsUUFBUTs0QkFDUkMsV0FBVzs0QkFDWFAsY0FBYzs0QkFDZFEsUUFBUTt3QkFDVjs7Ozs7O2tDQUNBLDhEQUFDQzt3QkFBR2hCLE9BQU87NEJBQUVpQixPQUFPOzRCQUFRRixRQUFRO3dCQUFFO2tDQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztJQUlqRDtJQUVBLElBQUksQ0FBQ3JDLFdBQVc7UUFDZCxJQUFJTixtQkFBbUI7WUFDckIscUJBQU87MEJBQUdBOztRQUNaO1FBRUEscUJBQ0UsOERBQUMyQjtZQUFJQyxPQUFPO2dCQUNWQyxXQUFXO2dCQUNYQyxZQUFZO2dCQUNaQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxnQkFBZ0I7Z0JBQ2hCQyxZQUFZO2dCQUNaWSxXQUFXO1lBQ2I7c0JBQ0UsNEVBQUNuQjtnQkFBSUMsT0FBTztvQkFDVkUsWUFBWTtvQkFDWkssY0FBYztvQkFDZEMsU0FBUztvQkFDVEMsV0FBVztvQkFDWEMsV0FBVztvQkFDWFMsVUFBVTtnQkFDWjs7a0NBQ0UsOERBQUNwQjt3QkFBSUMsT0FBTzs0QkFDVm9CLFVBQVU7NEJBQ1ZDLGNBQWM7d0JBQ2hCO2tDQUFHOzs7Ozs7a0NBR0gsOERBQUNMO3dCQUFHaEIsT0FBTzs0QkFDVGlCLE9BQU87NEJBQ1BJLGNBQWM7NEJBQ2RELFVBQVU7d0JBQ1o7a0NBQUc7Ozs7OztrQ0FHSCw4REFBQ0U7d0JBQUV0QixPQUFPOzRCQUNSaUIsT0FBTzs0QkFDUEksY0FBYzs0QkFDZEQsVUFBVTt3QkFDWjtrQ0FBRzs7Ozs7O2tDQUdILDhEQUFDckI7d0JBQUlDLE9BQU87NEJBQ1ZFLFlBQVk7NEJBQ1pNLFNBQVM7NEJBQ1RELGNBQWM7NEJBQ2RjLGNBQWM7NEJBQ2RaLFdBQVc7d0JBQ2I7OzBDQUNFLDhEQUFDYzswQ0FBTzs7Ozs7OzBDQUEwQiw4REFBQ0M7Ozs7OzRCQUFLOzRCQUNoQ2xELE1BQU1tRDswQ0FBSyw4REFBQ0Q7Ozs7OzRCQUFLOzRCQUNqQmxELE1BQU1xQjswQ0FBSyw4REFBQzZCOzs7Ozs0QkFBSzs0QkFDSnRELG9CQUFvQndELElBQUksQ0FBQyxTQUFTOzs7Ozs7O2tDQUV6RCw4REFBQ0M7d0JBQ0NDLFNBQVMsSUFBTXZELE9BQU9ZLElBQUksQ0FBQzt3QkFDM0JlLE9BQU87NEJBQ0xFLFlBQVk7NEJBQ1plLE9BQU87NEJBQ1BKLFFBQVE7NEJBQ1JOLGNBQWM7NEJBQ2RDLFNBQVM7NEJBQ1RZLFVBQVU7NEJBQ1ZTLFFBQVE7NEJBQ1J2QixZQUFZO3dCQUNkO2tDQUNEOzs7Ozs7Ozs7Ozs7Ozs7OztJQU1UO0lBRUEscUJBQU87a0JBQUdyQzs7QUFDWjtBQUVBLHVDQUF1QztBQUNoQyxTQUFTNkQ7SUFDZCxNQUFNLENBQUN4RCxNQUFNQyxRQUFRLEdBQUdULCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ1UsV0FBV0MsYUFBYSxHQUFHWCwrQ0FBUUEsQ0FBQztJQUUzQ0QsZ0RBQVNBOzZCQUFDO1lBQ1IsTUFBTWdCLFdBQVdDLGFBQWFDLE9BQU8sQ0FBQztZQUN0QyxJQUFJRixVQUFVO2dCQUNaTixRQUFRWSxLQUFLQyxLQUFLLENBQUNQO1lBQ3JCO1lBQ0FKLGFBQWE7UUFDZjs0QkFBRyxFQUFFO0lBRUwsTUFBTXNELFNBQVM7UUFDYmpELGFBQWFrRCxVQUFVLENBQUM7UUFDeEJsRCxhQUFha0QsVUFBVSxDQUFDO1FBQ3hCQyxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRztJQUN6QjtJQUVBLE1BQU1DLGdCQUFnQixDQUFDQztRQUNyQjVDLFFBQVFHLEdBQUcsQ0FBQztRQUNaLE9BQU8sTUFBTSwrREFBK0Q7SUFFNUU7Ozs7OztJQU1BLEdBQ0Y7SUFFQSxNQUFNQyxxQkFBcUIsQ0FBQ0Y7UUFDMUIsTUFBTUcsa0JBQStDO1lBQ25ELFNBQVM7Z0JBQUM7YUFBTTtZQUNoQixtQkFBbUI7Z0JBQUM7Z0JBQWdCO2dCQUFjO2dCQUFnQjtnQkFBZ0I7Z0JBQW1CO2dCQUFpQjtnQkFBbUI7YUFBa0I7WUFDM0osaUJBQWlCO2dCQUFDO2dCQUFnQjtnQkFBYztnQkFBZ0I7Z0JBQWdCO2FBQWdCO1lBQ2hHLGFBQWE7Z0JBQUM7Z0JBQW1CO2dCQUFpQjtnQkFBbUI7Z0JBQW1CO2FBQWE7WUFDckcsZUFBZTtnQkFBQztnQkFBYztnQkFBaUI7Z0JBQVk7Z0JBQWtCO2dCQUFlO2FBQWlCO1lBQzdHLGNBQWM7Z0JBQUM7Z0JBQWdCO2dCQUFjO2dCQUFnQjthQUFlO1lBQzVFLGlCQUFpQjtnQkFBQztnQkFBYztnQkFBWTtnQkFBYztnQkFBYztnQkFBbUI7Z0JBQWlCO2dCQUFtQjtnQkFBbUI7YUFBYTtZQUMvSixVQUFVO2dCQUFDO2dCQUFjO2FBQWdCO1FBQzNDO1FBRUEsT0FBT0EsZUFBZSxDQUFDSCxLQUFLLElBQUksRUFBRTtJQUNwQztJQUVBLE9BQU87UUFDTHJCO1FBQ0FFO1FBQ0F1RDtRQUNBSztRQUNBRSxTQUFTaEUsTUFBTXFCLFNBQVM7UUFDeEI0QyxnQkFBZ0JqRSxNQUFNcUIsU0FBUztRQUMvQjZDLGFBQWFsRSxNQUFNcUIsU0FBUztRQUM1QjhDLFVBQVVuRSxNQUFNcUIsU0FBUztRQUN6QitDLGNBQWNwRSxNQUFNcUIsU0FBUztRQUM3QmdELGFBQWFyRSxNQUFNcUIsU0FBUztRQUM1QmlELGdCQUFnQnRFLE1BQU1xQixTQUFTO1FBQy9Ca0Qsa0JBQWtCdkUsTUFBTXFCLFNBQVM7SUFDbkM7QUFDRiIsInNvdXJjZXMiOlsiRDpcXHByb2plY3Qgc3BvcnRcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcbWVkaWEtZGFzaGJvYXJkXFxzcmNcXGNvbXBvbmVudHNcXEF1dGhHdWFyZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcblxuaW50ZXJmYWNlIFVzZXIge1xuICBpZDogc3RyaW5nO1xuICB1c2VybmFtZTogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIHJvbGU6IHN0cmluZztcbiAgaXNBY3RpdmU6IGJvb2xlYW47XG4gIHBlcm1pc3Npb25zPzogc3RyaW5nW107XG59XG5cbmludGVyZmFjZSBBdXRoR3VhcmRQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIHJlcXVpcmVkUGVybWlzc2lvbnM/OiBzdHJpbmdbXTtcbiAgcmVxdWlyZWRSb2xlPzogc3RyaW5nO1xuICBmYWxsYmFja0NvbXBvbmVudD86IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEF1dGhHdWFyZCh7IFxuICBjaGlsZHJlbiwgXG4gIHJlcXVpcmVkUGVybWlzc2lvbnMgPSBbXSwgXG4gIHJlcXVpcmVkUm9sZSxcbiAgZmFsbGJhY2tDb21wb25lbnQgXG59OiBBdXRoR3VhcmRQcm9wcykge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtoYXNBY2Nlc3MsIHNldEhhc0FjY2Vzc10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjaGVja0F1dGgoKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGNoZWNrQXV0aCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2YjYrNmI2K8g2KjZitin2YbYp9iqINin2YTZhdiz2KrYrtiv2YUg2YHZiiBsb2NhbFN0b3JhZ2VcbiAgICAgIGNvbnN0IHVzZXJEYXRhID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3VzZXInKTtcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG5cbiAgICAgIGlmICghdXNlckRhdGEgfHwgIXRva2VuKSB7XG4gICAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBwYXJzZWRVc2VyID0gSlNPTi5wYXJzZSh1c2VyRGF0YSk7XG4gICAgICBzZXRVc2VyKHBhcnNlZFVzZXIpO1xuXG4gICAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYp9mE2LXZhNin2K3Zitin2KpcbiAgICAgIGNvbnN0IGFjY2VzcyA9IGNoZWNrUGVybWlzc2lvbnMocGFyc2VkVXNlciwgcmVxdWlyZWRQZXJtaXNzaW9ucywgcmVxdWlyZWRSb2xlKTtcbiAgICAgIHNldEhhc0FjY2VzcyhhY2Nlc3MpO1xuXG4gICAgICBpZiAoIWFjY2VzcyAmJiBmYWxsYmFja0NvbXBvbmVudCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJvdXRlci5wdXNoKCcvdW5hdXRob3JpemVkJyk7XG4gICAgICB9XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignQXV0aCBjaGVjayBlcnJvcjonLCBlcnJvcik7XG4gICAgICByb3V0ZXIucHVzaCgnL2xvZ2luJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNoZWNrUGVybWlzc2lvbnMgPSAodXNlcjogVXNlciwgcGVybWlzc2lvbnM6IHN0cmluZ1tdLCByb2xlPzogc3RyaW5nKTogYm9vbGVhbiA9PiB7XG4gICAgY29uc29sZS5sb2coJ+KaoO+4jyDYqtis2KfZiNiyINin2YTYqtit2YLZgiDZhdmGINin2YTYtdmE2KfYrdmK2KfYqiDZhdik2YLYqtin2Ysg2YTZhNiq2LfZiNmK2LEnKTtcbiAgICByZXR1cm4gdHJ1ZTsgLy8g2YXYpNmC2KrYp9mLOiDYp9mE2LPZhdin2K0g2KjYrNmF2YrYuSDYp9mE2LnZhdmE2YrYp9iqINmE2KzZhdmK2Lkg2KfZhNmF2LPYqtiu2K/ZhdmK2YYgKNmE2YTYqti32YjZitixINmB2YLYtylcbiAgICBcbiAgICAvKlxuICAgIC8vINin2YTZhdiv2YrYsSDZhNmHINi12YTYp9it2YrYp9iqINmD2KfZhdmE2KlcbiAgICBpZiAodXNlci5yb2xlID09PSAnQURNSU4nKSB7XG4gICAgICBjb25zb2xlLmxvZygn4pyFINin2YTZhdiz2KrYrtiv2YUg2YfZiCDZhdiv2YrYsSDYp9mE2YbYuNin2YUgLSDYqtmFINmF2YbYrSDYrNmF2YrYuSDYp9mE2LXZhNin2K3Zitin2KonKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cblxuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINin2YTYr9mI2LEg2KfZhNmF2LfZhNmI2KhcbiAgICBpZiAocm9sZSAmJiB1c2VyLnJvbGUgIT09IHJvbGUpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDinYwg2KfZhNmF2LPYqtiu2K/ZhSDZhNmK2LMg2YTYr9mK2Ycg2KfZhNiv2YjYsSDYp9mE2YXYt9mE2YjYqDogJHtyb2xlfWApO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cblxuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINin2YTYtdmE2KfYrdmK2KfYqiDYp9mE2YXYt9mE2YjYqNipXG4gICAgaWYgKHBlcm1pc3Npb25zLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IHVzZXJQZXJtaXNzaW9ucyA9IGdldFVzZXJQZXJtaXNzaW9ucyh1c2VyLnJvbGUpO1xuICAgICAgY29uc29sZS5sb2coJ/CflI0g2KfZhNiq2K3ZgtmCINmF2YYg2KfZhNi12YTYp9it2YrYp9iqOicsIHtcbiAgICAgICAgcmVxdWlyZWQ6IHBlcm1pc3Npb25zLFxuICAgICAgICB1c2VySGFzOiB1c2VyUGVybWlzc2lvbnNcbiAgICAgIH0pO1xuICAgICAgXG4gICAgICBjb25zdCBoYXNBbGxQZXJtaXNzaW9ucyA9IHBlcm1pc3Npb25zLmV2ZXJ5KHBlcm1pc3Npb24gPT4gXG4gICAgICAgIHVzZXJQZXJtaXNzaW9ucy5pbmNsdWRlcyhwZXJtaXNzaW9uKSB8fCB1c2VyUGVybWlzc2lvbnMuaW5jbHVkZXMoJ0FMTCcpXG4gICAgICApO1xuICAgICAgXG4gICAgICBpZiAoIWhhc0FsbFBlcm1pc3Npb25zKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinYwg2KfZhNmF2LPYqtiu2K/ZhSDZhNmK2LMg2YTYr9mK2Ycg2KzZhdmK2Lkg2KfZhNi12YTYp9it2YrYp9iqINin2YTZhdi32YTZiNio2KknKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg2KfZhNmF2LPYqtiu2K/ZhSDZhNiv2YrZhyDYrNmF2YrYuSDYp9mE2LXZhNin2K3Zitin2Kog2KfZhNmF2LfZhNmI2KjYqScpO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gaGFzQWxsUGVybWlzc2lvbnM7XG4gICAgfVxuXG4gICAgcmV0dXJuIHRydWU7XG4gICAgKi9cbiAgfTtcblxuICBjb25zdCBnZXRVc2VyUGVybWlzc2lvbnMgPSAocm9sZTogc3RyaW5nKTogc3RyaW5nW10gPT4ge1xuICAgIGNvbnN0IHJvbGVQZXJtaXNzaW9uczogeyBba2V5OiBzdHJpbmddOiBzdHJpbmdbXSB9ID0ge1xuICAgICAgJ0FETUlOJzogWydBTEwnXSxcbiAgICAgICdDT05URU5UX01BTkFHRVInOiBbJ01FRElBX0NSRUFURScsICdNRURJQV9SRUFEJywgJ01FRElBX1VQREFURScsICdNRURJQV9ERUxFVEUnLCAnU0NIRURVTEVfQ1JFQVRFJywgJ1NDSEVEVUxFX1JFQUQnLCAnU0NIRURVTEVfVVBEQVRFJywgJ1NDSEVEVUxFX0RFTEVURSddLFxuICAgICAgJ01FRElBX01BTkFHRVInOiBbJ01FRElBX0NSRUFURScsICdNRURJQV9SRUFEJywgJ01FRElBX1VQREFURScsICdNRURJQV9ERUxFVEUnLCAnU0NIRURVTEVfUkVBRCddLFxuICAgICAgJ1NDSEVEVUxFUic6IFsnU0NIRURVTEVfQ1JFQVRFJywgJ1NDSEVEVUxFX1JFQUQnLCAnU0NIRURVTEVfVVBEQVRFJywgJ1NDSEVEVUxFX0RFTEVURScsICdNRURJQV9SRUFEJ10sXG4gICAgICAnRlVMTF9WSUVXRVInOiBbJ01FRElBX1JFQUQnLCAnU0NIRURVTEVfUkVBRCcsICdNQVBfUkVBRCcsICdCUk9BRENBU1RfUkVBRCcsICdSRVBPUlRfUkVBRCcsICdEQVNIQk9BUkRfUkVBRCddLFxuICAgICAgJ0RBVEFfRU5UUlknOiBbJ01FRElBX0NSRUFURScsICdNRURJQV9SRUFEJywgJ01FRElBX1VQREFURScsICdNRURJQV9ERUxFVEUnXSxcbiAgICAgICdNQVBfU0NIRURVTEVSJzogWydNQVBfQ1JFQVRFJywgJ01BUF9SRUFEJywgJ01BUF9VUERBVEUnLCAnTUFQX0RFTEVURScsICdTQ0hFRFVMRV9DUkVBVEUnLCAnU0NIRURVTEVfUkVBRCcsICdTQ0hFRFVMRV9VUERBVEUnLCAnU0NIRURVTEVfREVMRVRFJywgJ01FRElBX1JFQUQnXSxcbiAgICAgICdWSUVXRVInOiBbJ01FRElBX1JFQUQnLCAnU0NIRURVTEVfUkVBRCddXG4gICAgfTtcblxuICAgIHJldHVybiByb2xlUGVybWlzc2lvbnNbcm9sZV0gfHwgW107XG4gIH07XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgIG1pbkhlaWdodDogJzEwMHZoJyxcbiAgICAgICAgYmFja2dyb3VuZDogJ2xpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSknLFxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgIGZvbnRGYW1pbHk6ICdDYWlybywgQXJpYWwsIHNhbnMtc2VyaWYnXG4gICAgICB9fT5cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIGJhY2tncm91bmQ6ICd3aGl0ZScsXG4gICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMjBweCcsXG4gICAgICAgICAgcGFkZGluZzogJzQwcHgnLFxuICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgYm94U2hhZG93OiAnMCAyMHB4IDQwcHggcmdiYSgwLDAsMCwwLjEpJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICB3aWR0aDogJzUwcHgnLFxuICAgICAgICAgICAgaGVpZ2h0OiAnNTBweCcsXG4gICAgICAgICAgICBib3JkZXI6ICc0cHggc29saWQgI2YzZjNmMycsXG4gICAgICAgICAgICBib3JkZXJUb3A6ICc0cHggc29saWQgIzY2N2VlYScsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc1MCUnLFxuICAgICAgICAgICAgbWFyZ2luOiAnMCBhdXRvIDIwcHgnXG4gICAgICAgICAgfX0gLz5cbiAgICAgICAgICA8aDIgc3R5bGU9e3sgY29sb3I6ICcjMzMzJywgbWFyZ2luOiAwIH19PuKPsyDYrNin2LHZiiDYp9mE2KrYrdmC2YIg2YXZhiDYp9mE2LXZhNin2K3Zitin2KouLi48L2gyPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICBpZiAoIWhhc0FjY2Vzcykge1xuICAgIGlmIChmYWxsYmFja0NvbXBvbmVudCkge1xuICAgICAgcmV0dXJuIDw+e2ZhbGxiYWNrQ29tcG9uZW50fTwvPjtcbiAgICB9XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsXG4gICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpJyxcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgICAgICBmb250RmFtaWx5OiAnQ2Fpcm8sIEFyaWFsLCBzYW5zLXNlcmlmJyxcbiAgICAgICAgZGlyZWN0aW9uOiAncnRsJ1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAnd2hpdGUnLFxuICAgICAgICAgIGJvcmRlclJhZGl1czogJzIwcHgnLFxuICAgICAgICAgIHBhZGRpbmc6ICc0MHB4JyxcbiAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgICAgICAgIGJveFNoYWRvdzogJzAgMjBweCA0MHB4IHJnYmEoMCwwLDAsMC4xKScsXG4gICAgICAgICAgbWF4V2lkdGg6ICc1MDBweCdcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgZm9udFNpemU6ICc0cmVtJyxcbiAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzIwcHgnXG4gICAgICAgICAgfX0+XG4gICAgICAgICAgICDwn5qrXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGgyIHN0eWxlPXt7IFxuICAgICAgICAgICAgY29sb3I6ICcjZGMzNTQ1JywgXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcxNXB4JyxcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMS41cmVtJ1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAg2LrZitixINmF2LXYsditINmE2YMg2KjYp9mE2YjYtdmI2YRcbiAgICAgICAgICA8L2gyPlxuICAgICAgICAgIDxwIHN0eWxlPXt7IFxuICAgICAgICAgICAgY29sb3I6ICcjNmM3NTdkJywgXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcyNXB4JyxcbiAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbSdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgINmE2YrYsyDZhNiv2YrZgyDYp9mE2LXZhNin2K3Zitin2Kog2KfZhNmF2LfZhNmI2KjYqSDZhNmE2YjYtdmI2YQg2KXZhNmJINmH2LDZhyDYp9mE2LXZgdit2KlcbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgICAgYmFja2dyb3VuZDogJyNmOGY5ZmEnLFxuICAgICAgICAgICAgcGFkZGluZzogJzE1cHgnLFxuICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTBweCcsXG4gICAgICAgICAgICBtYXJnaW5Cb3R0b206ICcyNXB4JyxcbiAgICAgICAgICAgIHRleHRBbGlnbjogJ3JpZ2h0J1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAgPHN0cm9uZz7Zhdi52YTZiNmF2KfYqiDYp9mE2YXYs9iq2K7Yr9mFOjwvc3Ryb25nPjxiciAvPlxuICAgICAgICAgICAg2KfZhNin2LPZhToge3VzZXI/Lm5hbWV9PGJyIC8+XG4gICAgICAgICAgICDYp9mE2K/ZiNixOiB7dXNlcj8ucm9sZX08YnIgLz5cbiAgICAgICAgICAgINin2YTYtdmE2KfYrdmK2KfYqiDYp9mE2YXYt9mE2YjYqNipOiB7cmVxdWlyZWRQZXJtaXNzaW9ucy5qb2luKCcsICcpIHx8ICfYutmK2LEg2YXYrdiv2K8nfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvJyl9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjNjY3ZWVhLCAjNzY0YmEyKScsXG4gICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBib3JkZXI6ICdub25lJyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTBweCcsXG4gICAgICAgICAgICAgIHBhZGRpbmc6ICcxMnB4IDI1cHgnLFxuICAgICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgZm9udEZhbWlseTogJ0NhaXJvLCBBcmlhbCwgc2Fucy1zZXJpZidcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAg8J+PoCDYp9mE2LnZiNiv2Kkg2YTZhNix2KbZitiz2YrYqVxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gPD57Y2hpbGRyZW59PC8+O1xufVxuXG4vLyBIb29rINmE2KfYs9iq2K7Yr9in2YUg2KjZitin2YbYp9iqINin2YTZhdiz2KrYrtiv2YUg2KfZhNit2KfZhNmKXG5leHBvcnQgZnVuY3Rpb24gdXNlQXV0aCgpIHtcbiAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB1c2VyRGF0YSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJyk7XG4gICAgaWYgKHVzZXJEYXRhKSB7XG4gICAgICBzZXRVc2VyKEpTT04ucGFyc2UodXNlckRhdGEpKTtcbiAgICB9XG4gICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgfSwgW10pO1xuXG4gIGNvbnN0IGxvZ291dCA9ICgpID0+IHtcbiAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgndXNlcicpO1xuICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd0b2tlbicpO1xuICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbic7XG4gIH07XG5cbiAgY29uc3QgaGFzUGVybWlzc2lvbiA9IChwZXJtaXNzaW9uOiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgICBjb25zb2xlLmxvZygn4pqg77iPINiq2KzYp9mI2LIg2KfZhNiq2K3ZgtmCINmF2YYg2KfZhNi12YTYp9it2YrYp9iqINmF2KTZgtiq2KfZiyDZhNmE2KrYt9mI2YrYsScpO1xuICAgIHJldHVybiB0cnVlOyAvLyDZhdik2YLYqtin2Ys6INin2YTYs9mF2KfYrSDYqNis2YXZiti5INin2YTYudmF2YTZitin2Kog2YTYrNmF2YrYuSDYp9mE2YXYs9iq2K7Yr9mF2YrZhiAo2YTZhNiq2LfZiNmK2LEg2YHZgti3KVxuICAgIFxuICAgIC8qXG4gICAgaWYgKCF1c2VyKSByZXR1cm4gZmFsc2U7XG4gICAgaWYgKHVzZXIucm9sZSA9PT0gJ0FETUlOJykgcmV0dXJuIHRydWU7XG5cbiAgICBjb25zdCB1c2VyUGVybWlzc2lvbnMgPSBnZXRVc2VyUGVybWlzc2lvbnModXNlci5yb2xlKTtcbiAgICByZXR1cm4gdXNlclBlcm1pc3Npb25zLmluY2x1ZGVzKHBlcm1pc3Npb24pIHx8IHVzZXJQZXJtaXNzaW9ucy5pbmNsdWRlcygnQUxMJyk7XG4gICAgKi9cbiAgfTtcblxuICBjb25zdCBnZXRVc2VyUGVybWlzc2lvbnMgPSAocm9sZTogc3RyaW5nKTogc3RyaW5nW10gPT4ge1xuICAgIGNvbnN0IHJvbGVQZXJtaXNzaW9uczogeyBba2V5OiBzdHJpbmddOiBzdHJpbmdbXSB9ID0ge1xuICAgICAgJ0FETUlOJzogWydBTEwnXSxcbiAgICAgICdDT05URU5UX01BTkFHRVInOiBbJ01FRElBX0NSRUFURScsICdNRURJQV9SRUFEJywgJ01FRElBX1VQREFURScsICdNRURJQV9ERUxFVEUnLCAnU0NIRURVTEVfQ1JFQVRFJywgJ1NDSEVEVUxFX1JFQUQnLCAnU0NIRURVTEVfVVBEQVRFJywgJ1NDSEVEVUxFX0RFTEVURSddLFxuICAgICAgJ01FRElBX01BTkFHRVInOiBbJ01FRElBX0NSRUFURScsICdNRURJQV9SRUFEJywgJ01FRElBX1VQREFURScsICdNRURJQV9ERUxFVEUnLCAnU0NIRURVTEVfUkVBRCddLFxuICAgICAgJ1NDSEVEVUxFUic6IFsnU0NIRURVTEVfQ1JFQVRFJywgJ1NDSEVEVUxFX1JFQUQnLCAnU0NIRURVTEVfVVBEQVRFJywgJ1NDSEVEVUxFX0RFTEVURScsICdNRURJQV9SRUFEJ10sXG4gICAgICAnRlVMTF9WSUVXRVInOiBbJ01FRElBX1JFQUQnLCAnU0NIRURVTEVfUkVBRCcsICdNQVBfUkVBRCcsICdCUk9BRENBU1RfUkVBRCcsICdSRVBPUlRfUkVBRCcsICdEQVNIQk9BUkRfUkVBRCddLFxuICAgICAgJ0RBVEFfRU5UUlknOiBbJ01FRElBX0NSRUFURScsICdNRURJQV9SRUFEJywgJ01FRElBX1VQREFURScsICdNRURJQV9ERUxFVEUnXSxcbiAgICAgICdNQVBfU0NIRURVTEVSJzogWydNQVBfQ1JFQVRFJywgJ01BUF9SRUFEJywgJ01BUF9VUERBVEUnLCAnTUFQX0RFTEVURScsICdTQ0hFRFVMRV9DUkVBVEUnLCAnU0NIRURVTEVfUkVBRCcsICdTQ0hFRFVMRV9VUERBVEUnLCAnU0NIRURVTEVfREVMRVRFJywgJ01FRElBX1JFQUQnXSxcbiAgICAgICdWSUVXRVInOiBbJ01FRElBX1JFQUQnLCAnU0NIRURVTEVfUkVBRCddXG4gICAgfTtcblxuICAgIHJldHVybiByb2xlUGVybWlzc2lvbnNbcm9sZV0gfHwgW107XG4gIH07XG5cbiAgcmV0dXJuIHtcbiAgICB1c2VyLFxuICAgIGlzTG9hZGluZyxcbiAgICBsb2dvdXQsXG4gICAgaGFzUGVybWlzc2lvbixcbiAgICBpc0FkbWluOiB1c2VyPy5yb2xlID09PSAnQURNSU4nLFxuICAgIGlzTWVkaWFNYW5hZ2VyOiB1c2VyPy5yb2xlID09PSAnTUVESUFfTUFOQUdFUicsXG4gICAgaXNTY2hlZHVsZXI6IHVzZXI/LnJvbGUgPT09ICdTQ0hFRFVMRVInLFxuICAgIGlzVmlld2VyOiB1c2VyPy5yb2xlID09PSAnVklFV0VSJyxcbiAgICBpc0Z1bGxWaWV3ZXI6IHVzZXI/LnJvbGUgPT09ICdGVUxMX1ZJRVdFUicsXG4gICAgaXNEYXRhRW50cnk6IHVzZXI/LnJvbGUgPT09ICdEQVRBX0VOVFJZJyxcbiAgICBpc01hcFNjaGVkdWxlcjogdXNlcj8ucm9sZSA9PT0gJ01BUF9TQ0hFRFVMRVInLFxuICAgIGlzQ29udGVudE1hbmFnZXI6IHVzZXI/LnJvbGUgPT09ICdDT05URU5UX01BTkFHRVInXG4gIH07XG59Il0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlUm91dGVyIiwiQXV0aEd1YXJkIiwiY2hpbGRyZW4iLCJyZXF1aXJlZFBlcm1pc3Npb25zIiwicmVxdWlyZWRSb2xlIiwiZmFsbGJhY2tDb21wb25lbnQiLCJyb3V0ZXIiLCJ1c2VyIiwic2V0VXNlciIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImhhc0FjY2VzcyIsInNldEhhc0FjY2VzcyIsImNoZWNrQXV0aCIsInVzZXJEYXRhIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInRva2VuIiwicHVzaCIsInBhcnNlZFVzZXIiLCJKU09OIiwicGFyc2UiLCJhY2Nlc3MiLCJjaGVja1Blcm1pc3Npb25zIiwidW5kZWZpbmVkIiwiZXJyb3IiLCJjb25zb2xlIiwicGVybWlzc2lvbnMiLCJyb2xlIiwibG9nIiwiZ2V0VXNlclBlcm1pc3Npb25zIiwicm9sZVBlcm1pc3Npb25zIiwiZGl2Iiwic3R5bGUiLCJtaW5IZWlnaHQiLCJiYWNrZ3JvdW5kIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImZvbnRGYW1pbHkiLCJib3JkZXJSYWRpdXMiLCJwYWRkaW5nIiwidGV4dEFsaWduIiwiYm94U2hhZG93Iiwid2lkdGgiLCJoZWlnaHQiLCJib3JkZXIiLCJib3JkZXJUb3AiLCJtYXJnaW4iLCJoMiIsImNvbG9yIiwiZGlyZWN0aW9uIiwibWF4V2lkdGgiLCJmb250U2l6ZSIsIm1hcmdpbkJvdHRvbSIsInAiLCJzdHJvbmciLCJiciIsIm5hbWUiLCJqb2luIiwiYnV0dG9uIiwib25DbGljayIsImN1cnNvciIsInVzZUF1dGgiLCJsb2dvdXQiLCJyZW1vdmVJdGVtIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiaGFzUGVybWlzc2lvbiIsInBlcm1pc3Npb24iLCJpc0FkbWluIiwiaXNNZWRpYU1hbmFnZXIiLCJpc1NjaGVkdWxlciIsImlzVmlld2VyIiwiaXNGdWxsVmlld2VyIiwiaXNEYXRhRW50cnkiLCJpc01hcFNjaGVkdWxlciIsImlzQ29udGVudE1hbmFnZXIiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(ssr)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction DashboardLayout({ children, title = '', subtitle = '', icon = '📊', requiredPermissions, requiredRole, fullWidth = false }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const timer = setInterval({\n                \"DashboardLayout.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"DashboardLayout.useEffect.timer\"], 1000);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>clearInterval(timer)\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    const navigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: false,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            active: false,\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.unifiedSystem'),\n            icon: '📤',\n            active: false,\n            path: '/unified-system',\n            adminOnly: true,\n            superAdminOnly: true\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard'\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics'\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.superAdminOnly && (user?.role !== 'ADMIN' || user?.username !== 'admin')) return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: requiredPermissions,\n        requiredRole: requiredRole,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"dashboard-layout\",\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: isRTL ? 'rtl' : 'ltr'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const newLang = currentLang === 'ar' ? 'en' : 'ar';\n                                        i18n.changeLanguage(newLang);\n                                    },\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '5px'\n                                    },\n                                    title: currentLang === 'ar' ? 'Switch to English' : 'التبديل للعربية',\n                                    children: [\n                                        \"\\uD83C\\uDF10\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: currentLang === 'ar' ? 'EN' : 'عر'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    title: t('auth.logout'),\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        ...isRTL ? {\n                            marginRight: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-right 0.3s ease'\n                        } : {\n                            marginLeft: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-left 0.3s ease'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: t('common.active')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: currentTime.toLocaleTimeString(isRTL ? 'ar-EG' : 'en-US')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"dashboard-card\",\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '20px',\n                                padding: '25px',\n                                border: '1px solid #4a5568',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                                maxWidth: fullWidth ? 'none' : '1000px',\n                                margin: '0 auto',\n                                width: fullWidth ? '100%' : 'auto'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Logo({ size = 'medium', className, style }) {\n    const sizes = {\n        small: {\n            fontSize: '1rem',\n            gap: '3px',\n            xSize: '1.2rem'\n        },\n        medium: {\n            fontSize: '1.2rem',\n            gap: '5px',\n            xSize: '1.5rem'\n        },\n        large: {\n            fontSize: '2rem',\n            gap: '8px',\n            xSize: '2.5rem'\n        }\n    };\n    const currentSize = sizes[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        style: {\n            display: 'flex',\n            alignItems: 'center',\n            fontSize: currentSize.fontSize,\n            fontWeight: '900',\n            fontFamily: 'Arial, sans-serif',\n            gap: currentSize.gap,\n            direction: 'ltr',\n            ...style\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '800',\n                    letterSpacing: '1px'\n                },\n                children: \"Prime\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: '#6c757d',\n                    fontSize: '0.8em',\n                    fontWeight: '300'\n                },\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '900',\n                    fontSize: currentSize.xSize\n                },\n                children: \"X\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(ssr)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Sidebar({ isOpen, onToggle }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const menuItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.importSchedule'),\n            icon: '📤',\n            path: '/daily-schedule/import',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            path: '/statistics',\n            permission: null\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"sidebar-container\",\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    ...isRTL ? {\n                        right: isOpen ? 0 : '-280px',\n                        borderLeft: '1px solid #2d3748'\n                    } : {\n                        left: isOpen ? 0 : '-280px',\n                        borderRight: '1px solid #2d3748'\n                    },\n                    width: 'min(280px, 90vw)',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    transition: `${isRTL ? 'right' : 'left'} 0.3s ease`,\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif',\n                    boxShadow: isOpen ? '0 0 20px rgba(0, 0, 0, 0.5)' : 'none'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar-header\",\n                        style: {\n                            padding: 'clamp(15px, 3vw, 20px)',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between',\n                            minHeight: '70px'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px',\n                                    flex: 1\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        flex: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: \"small\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                margin: 0,\n                                                fontSize: 'clamp(0.7rem, 2vw, 0.8rem)',\n                                                lineHeight: 1.3\n                                            },\n                                            children: t('dashboard.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: 'clamp(1rem, 3vw, 1.2rem)',\n                                        cursor: 'pointer',\n                                        padding: '8px',\n                                        borderRadius: '4px',\n                                        transition: 'all 0.2s ease'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        e.target.style.background = 'rgba(102, 126, 234, 0.1)';\n                                        e.target.style.color = '#667eea';\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar-menu\",\n                        style: {\n                            flex: 1,\n                            padding: 'clamp(15px, 3vw, 20px) 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"sidebar-menu-item\",\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#2d3748' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    borderTop: 'none',\n                                    borderBottom: 'none',\n                                    ...isRTL ? {\n                                        borderLeft: 'none',\n                                        borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    } : {\n                                        borderRight: 'none',\n                                        borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    },\n                                    padding: isRTL ? 'clamp(10px, 2.5vw, 12px) clamp(15px, 4vw, 20px) clamp(10px, 2.5vw, 12px) clamp(6px, 2vw, 8px)' : 'clamp(10px, 2.5vw, 12px) clamp(6px, 2vw, 8px) clamp(10px, 2.5vw, 12px) clamp(15px, 4vw, 20px)',\n                                    textAlign: isRTL ? 'right' : 'left',\n                                    cursor: 'pointer',\n                                    fontSize: 'clamp(0.8rem, 2.5vw, 0.9rem)',\n                                    fontWeight: 'bold',\n                                    transition: 'all 0.2s ease',\n                                    direction: isRTL ? 'rtl' : 'ltr',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: 'clamp(8px, 2vw, 12px)',\n                                    minHeight: '44px'\n                                },\n                                onMouseEnter: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'rgba(102, 126, 234, 0.1)';\n                                        e.target.style.color = '#667eea';\n                                    }\n                                },\n                                onMouseLeave: (e)=>{\n                                    if (!isActive) {\n                                        e.target.style.background = 'transparent';\n                                        e.target.style.color = '#a0aec0';\n                                    }\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            fontSize: 'clamp(1rem, 3vw, 1.2rem)'\n                                        },\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        style: {\n                                            flex: 1\n                                        },\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"sidebar-footer\",\n                        style: {\n                            padding: 'clamp(15px, 3vw, 20px)',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('user');\n                                localStorage.removeItem('token');\n                                router.push('/login');\n                            },\n                            style: {\n                                width: '100%',\n                                background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: 'clamp(10px, 2.5vw, 12px)',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: 'clamp(6px, 2vw, 8px)',\n                                fontSize: 'clamp(0.8rem, 2.5vw, 0.9rem)',\n                                fontWeight: 'bold',\n                                marginBottom: 'clamp(10px, 2.5vw, 15px)',\n                                transition: 'all 0.2s ease',\n                                minHeight: '44px'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.target.style.transform = 'translateY(-2px)';\n                                e.target.style.boxShadow = '0 4px 12px rgba(245, 101, 101, 0.4)';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.target.style.transform = 'translateY(0)';\n                                e.target.style.boxShadow = 'none';\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    style: {\n                                        fontSize: 'clamp(1rem, 3vw, 1.2rem)'\n                                    },\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: t('navigation.logout')\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \n\nfunction Toast({ message, type, duration = 3000, onClose }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن\n                }\n            }[\"Toast.useEffect.timer\"], duration);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        duration,\n        onClose\n    ]);\n    const getToastStyles = ()=>{\n        const baseStyles = {\n            position: 'relative',\n            padding: '15px 20px',\n            borderRadius: '10px',\n            color: 'white',\n            fontWeight: 'bold',\n            fontSize: '1rem',\n            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n            transform: isVisible ? 'translateX(0)' : 'translateX(100%)',\n            transition: 'transform 0.3s ease, opacity 0.3s ease',\n            opacity: isVisible ? 1 : 0,\n            minWidth: '300px',\n            maxWidth: '500px',\n            direction: 'rtl',\n            fontFamily: 'Cairo, Arial, sans-serif'\n        };\n        const typeStyles = {\n            success: {\n                background: 'linear-gradient(45deg, #28a745, #20c997)'\n            },\n            error: {\n                background: 'linear-gradient(45deg, #dc3545, #c82333)'\n            },\n            warning: {\n                background: 'linear-gradient(45deg, #ffc107, #e0a800)'\n            },\n            info: {\n                background: 'linear-gradient(45deg, #007bff, #0056b3)'\n            }\n        };\n        return {\n            ...baseStyles,\n            ...typeStyles[type]\n        };\n    };\n    const getIcon = ()=>{\n        const icons = {\n            success: '✅',\n            error: '❌',\n            warning: '⚠️',\n            info: 'ℹ️'\n        };\n        return icons[type];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: getToastStyles(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '10px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        fontSize: '1.2rem'\n                    },\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setIsVisible(false);\n                        setTimeout(onClose, 300);\n                    },\n                    style: {\n                        background: 'rgba(255,255,255,0.2)',\n                        border: 'none',\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '25px',\n                        height: '25px',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        marginLeft: 'auto'\n                    },\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n// Hook لاستخدام Toast\nfunction useToast() {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (message, type = 'info')=>{\n        // التحقق من عدم وجود رسالة مشابهة\n        const existingToast = toasts.find((toast)=>toast.message === message && toast.type === type);\n        if (existingToast) {\n            return; // لا تضيف رسالة مكررة\n        }\n        // إنشاء ID فريد باستخدام timestamp + random number\n        const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        // حد أقصى 5 رسائل في نفس الوقت\n        setToasts((prev)=>prev.slice(-4)); // احتفظ بآخر 4 + الجديدة = 5\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const ToastContainer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: 'fixed',\n                top: '20px',\n                right: '20px',\n                zIndex: 1000,\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '10px'\n            },\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                    message: toast.message,\n                    type: toast.type,\n                    onClose: ()=>removeToast(toast.id)\n                }, toast.id, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 129,\n            columnNumber: 5\n        }, this);\n    return {\n        showToast,\n        ToastContainer\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAppTranslation.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAppTranslation.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppTranslation: () => (/* binding */ useAppTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _utils_translations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/translations */ \"(ssr)/./src/utils/translations.ts\");\n\n\n// Hook مخصص للترجمة يضمن الاتساق\nconst useAppTranslation = ()=>{\n    const { i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const currentLang = i18n.language || 'ar';\n    // دالة الترجمة الأساسية\n    const t = (key)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getTranslation)(key, currentLang);\n    };\n    // دالة ترجمة أنواع المواد\n    const tMediaType = (type)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getMediaTypeLabel)(type, currentLang);\n    };\n    // دالة ترجمة الأدوار\n    const tRole = (role)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getRoleLabel)(role, currentLang);\n    };\n    // دالة ترجمة أوصاف الأدوار\n    const tRoleDesc = (role)=>{\n        return (0,_utils_translations__WEBPACK_IMPORTED_MODULE_1__.getRoleDescription)(role, currentLang);\n    };\n    return {\n        t,\n        tMediaType,\n        tRole,\n        tRoleDesc,\n        currentLang,\n        isRTL: currentLang === 'ar'\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAppTranslation.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useTranslatedToast.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useTranslatedToast.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslatedToast: () => (/* binding */ useTranslatedToast)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Toast */ \"(ssr)/./src/components/Toast.tsx\");\n\n\nconst useTranslatedToast = ()=>{\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    const showTranslatedToast = (messageKey, type = 'info')=>{\n        const message = t(`messages.${type}.${messageKey}`);\n        showToast(message, type);\n    };\n    const showSuccessToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'success');\n    };\n    const showErrorToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'error');\n    };\n    const showInfoToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'info');\n    };\n    return {\n        showTranslatedToast,\n        showSuccessToast,\n        showErrorToast,\n        showInfoToast,\n        ToastContainer\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useTranslatedToast.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n.ts":
/*!*************************!*\
  !*** ./src/lib/i18n.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _public_locales_ar_common_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/locales/ar/common.json */ \"(ssr)/./public/locales/ar/common.json\");\n/* harmony import */ var _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../public/locales/en/common.json */ \"(ssr)/./public/locales/en/common.json\");\n\n\n// Import translation files\n\n\nconst resources = {\n    ar: {\n        common: _public_locales_ar_common_json__WEBPACK_IMPORTED_MODULE_2__\n    },\n    en: {\n        common: _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__\n    }\n};\n// Get saved language from localStorage or default to Arabic\nconst getInitialLanguage = ()=>{\n    if (false) {}\n    return 'ar';\n};\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    lng: getInitialLanguage(),\n    fallbackLng: 'ar',\n    debug: false,\n    interpolation: {\n        escapeValue: false\n    },\n    react: {\n        useSuspense: false\n    },\n    // Configure namespaces\n    defaultNS: 'common',\n    ns: [\n        'common'\n    ]\n});\n// Listen for language changes and update document direction\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].on('languageChanged', (lng)=>{\n    if (false) {}\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/translations.ts":
/*!***********************************!*\
  !*** ./src/utils/translations.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getMediaTypeLabel: () => (/* binding */ getMediaTypeLabel),\n/* harmony export */   getRoleDescription: () => (/* binding */ getRoleDescription),\n/* harmony export */   getRoleLabel: () => (/* binding */ getRoleLabel),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation),\n/* harmony export */   translations: () => (/* binding */ translations)\n/* harmony export */ });\n// مركز الترجمة الموحد - يضمن الترجمة الصحيحة في كلا اللغتين\nconst translations = {\n    ar: {\n        // Navigation\n        navigation: {\n            dashboard: \"لوحة التحكم\",\n            mediaList: \"المواد الإعلامية\",\n            addMedia: \"إضافة مادة\",\n            weeklySchedule: \"الخريطة البرامجية\",\n            dailySchedule: \"الجدول الإذاعي اليومي\",\n            importSchedule: \"استيراد جدول\",\n            statistics: \"الإحصائيات\",\n            adminDashboard: \"إدارة المستخدمين\",\n            reports: \"تقارير البث\",\n            logout: \"تسجيل الخروج\"\n        },\n        // Common terms\n        common: {\n            name: \"الاسم\",\n            type: \"النوع\",\n            status: \"الحالة\",\n            actions: \"الإجراءات\",\n            time: \"الوقت\",\n            duration: \"المدة\",\n            content: \"المحتوى\",\n            code: \"الكود\",\n            segments: \"السيجمنتات\"\n        },\n        // Media types - أسماء ثابتة حسب المطلوب\n        mediaTypes: {\n            ALL: \"جميع الأنواع\",\n            PROGRAM: \"برنامج\",\n            SERIES: \"مسلسل\",\n            FILM: \"فيلم\",\n            SONG: \"أغنية\",\n            PROMO: \"إعلان ترويجي\",\n            STING: \"فاصل\",\n            FILLER: \"مادة مالئة\",\n            NEXT: \"التالي\",\n            NOW: \"الآن\",\n            MINI: \"Mini\",\n            CROSS: \"Cross\",\n            \"سنعود\": \"سنعود\",\n            \"عدنا\": \"عدنا\" // يبقى بالعربية\n        },\n        // User roles\n        roles: {\n            ADMIN: \"مدير النظام\",\n            CONTENT_MANAGER: \"مدير المحتوى\",\n            MEDIA_MANAGER: \"مدير قاعدة البيانات\",\n            SCHEDULER: \"مجدول البرامج\",\n            FULL_VIEWER: \"مستخدم عرض كامل\",\n            DATA_ENTRY: \"إدخال البيانات\",\n            MAP_SCHEDULER: \"مدير الخريطة والجداول\",\n            VIEWER: \"مستخدم عرض\"\n        },\n        // Role descriptions\n        roleDescriptions: {\n            ADMIN: \"صلاحيات كاملة لإدارة النظام والمستخدمين\",\n            CONTENT_MANAGER: \"إدارة المحتوى والمواد الإعلامية\",\n            MEDIA_MANAGER: \"إدارة قاعدة بيانات المواد\",\n            SCHEDULER: \"إنشاء وتعديل الجداول البرامجية\",\n            FULL_VIEWER: \"عرض جميع البيانات والتقارير\",\n            DATA_ENTRY: \"إدخال وتعديل البيانات الأساسية\",\n            MAP_SCHEDULER: \"إدارة الخريطة البرامجية والجداول\",\n            VIEWER: \"عرض البيانات الأساسية فقط\"\n        }\n    },\n    en: {\n        // Navigation\n        navigation: {\n            dashboard: \"Dashboard\",\n            mediaList: \"Media List\",\n            addMedia: \"Add Media\",\n            weeklySchedule: \"Weekly Schedule\",\n            dailySchedule: \"Daily Schedule\",\n            importSchedule: \"Import Schedule\",\n            statistics: \"Statistics\",\n            adminDashboard: \"User Management\",\n            reports: \"Broadcast Reports\",\n            logout: \"Logout\"\n        },\n        // Common terms\n        common: {\n            name: \"Name\",\n            type: \"Type\",\n            status: \"Status\",\n            actions: \"Actions\",\n            time: \"Time\",\n            duration: \"Duration\",\n            content: \"Content\",\n            code: \"Code\",\n            segments: \"Segments\"\n        },\n        // Media types - أسماء ثابتة حسب المطلوب\n        mediaTypes: {\n            ALL: \"All Types\",\n            PROGRAM: \"Program\",\n            SERIES: \"Series\",\n            FILM: \"Film\",\n            SONG: \"Song\",\n            PROMO: \"Promo\",\n            STING: \"Sting\",\n            FILLER: \"Filler\",\n            NEXT: \"Next\",\n            NOW: \"Now\",\n            MINI: \"Mini\",\n            CROSS: \"Cross\",\n            \"سنعود\": \"We'll Be Back\",\n            \"عدنا\": \"We're Back\" // ترجمة للإنجليزية\n        },\n        // User roles\n        roles: {\n            ADMIN: \"System Administrator\",\n            CONTENT_MANAGER: \"Content Manager\",\n            MEDIA_MANAGER: \"Database Manager\",\n            SCHEDULER: \"Program Scheduler\",\n            FULL_VIEWER: \"Full View User\",\n            DATA_ENTRY: \"Data Entry\",\n            MAP_SCHEDULER: \"Map & Schedule Manager\",\n            VIEWER: \"Viewer\"\n        },\n        // Role descriptions\n        roleDescriptions: {\n            ADMIN: \"Full system administration and user management permissions\",\n            CONTENT_MANAGER: \"Manage content and media materials\",\n            MEDIA_MANAGER: \"Manage media database\",\n            SCHEDULER: \"Create and edit program schedules\",\n            FULL_VIEWER: \"View all data and reports\",\n            DATA_ENTRY: \"Enter and edit basic data\",\n            MAP_SCHEDULER: \"Manage program map and schedules\",\n            VIEWER: \"View basic data only\"\n        }\n    }\n};\n// دالة الترجمة المركزية\nconst getTranslation = (key, language = 'ar')=>{\n    const keys = key.split('.');\n    let value = translations[language];\n    for (const k of keys){\n        if (value && typeof value === 'object' && k in value) {\n            value = value[k];\n        } else {\n            // إذا لم توجد الترجمة، ارجع المفتاح نفسه\n            console.warn(`Translation missing for key: ${key} in language: ${language}`);\n            return key;\n        }\n    }\n    return typeof value === 'string' ? value : key;\n};\n// دالة مساعدة للحصول على ترجمة نوع المادة\nconst getMediaTypeLabel = (type, language = 'ar')=>{\n    return getTranslation(`mediaTypes.${type}`, language);\n};\n// دالة مساعدة للحصول على ترجمة الدور\nconst getRoleLabel = (role, language = 'ar')=>{\n    return getTranslation(`roles.${role}`, language);\n};\n// دالة مساعدة للحصول على وصف الدور\nconst getRoleDescription = (role, language = 'ar')=>{\n    return getTranslation(`roleDescriptions.${role}`, language);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/translations.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmedia-list%2Fpage&page=%2Fmedia-list%2Fpage&appPaths=%2Fmedia-list%2Fpage&pagePath=private-next-app-dir%2Fmedia-list%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();