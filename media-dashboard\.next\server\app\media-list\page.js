/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/media-list/page";
exports.ids = ["app/media-list/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmedia-list%2Fpage&page=%2Fmedia-list%2Fpage&appPaths=%2Fmedia-list%2Fpage&pagePath=private-next-app-dir%2Fmedia-list%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmedia-list%2Fpage&page=%2Fmedia-list%2Fpage&appPaths=%2Fmedia-list%2Fpage&pagePath=private-next-app-dir%2Fmedia-list%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/media-list/page.tsx */ \"(rsc)/./src/app/media-list/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'media-list',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/media-list/page\",\n        pathname: \"/media-list\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZtZWRpYS1saXN0JTJGcGFnZSZwYWdlPSUyRm1lZGlhLWxpc3QlMkZwYWdlJmFwcFBhdGhzPSUyRm1lZGlhLWxpc3QlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGbWVkaWEtbGlzdCUyRnBhZ2UudHN4JmFwcERpcj1EJTNBJTVDcHJvamVjdCUyMHNwb3J0JTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDbWVkaWEtZGFzaGJvYXJkJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1EJTNBJTVDcHJvamVjdCUyMHNwb3J0JTVDbWVkaWEtZGFzaGJvYXJkLWNsZWFuJTVDbWVkaWEtZGFzaGJvYXJkJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQkFBc0Isb0pBQW9IO0FBQzFJLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLDBOQUFnRjtBQUN0RyxzQkFBc0IsZ09BQW1GO0FBQ3pHLG9CQUFvQixzS0FBOEg7QUFHaEo7QUFHQTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlDQUFpQztBQUNqQztBQUNBO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0Msc2ZBQTRRO0FBQ2hUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxzZkFBNFE7QUFDaFQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFHckI7QUFDRiw2QkFBNkIsbUJBQW1CO0FBQ2hEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFHRTtBQUNGO0FBQ08sd0JBQXdCLHVHQUFrQjtBQUNqRDtBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBtb2R1bGUwID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxwcm9qZWN0IHNwb3J0XFxcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcXFxtZWRpYS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgcGFnZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXG1lZGlhLWxpc3RcXFxccGFnZS50c3hcIik7XG5pbXBvcnQgeyBBcHBQYWdlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNzcidcbn07XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc2VydmVyLXV0aWxpdHknXG59O1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ21lZGlhLWxpc3QnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbJ19fUEFHRV9fJywge30sIHtcbiAgICAgICAgICBwYWdlOiBbcGFnZTQsIFwiRDpcXFxccHJvamVjdCBzcG9ydFxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbWVkaWEtbGlzdFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBcbiAgICAgICAgfV1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxwcm9qZWN0IHNwb3J0XFxcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcXFxtZWRpYS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgJ2xheW91dCc6IFttb2R1bGUwLCBcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFEOlxcXFxwcm9qZWN0IHNwb3J0XFxcXG1lZGlhLWRhc2hib2FyZC1jbGVhblxcXFxtZWRpYS1kYXNoYm9hcmRcXFxcc3JjXFxcXGFwcFxcXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfX1wiKSkuZGVmYXVsdChwcm9wcykpXSxcbiAgICBhcHBsZTogW10sXG4gICAgb3BlbkdyYXBoOiBbXSxcbiAgICB0d2l0dGVyOiBbXSxcbiAgICBtYW5pZmVzdDogdW5kZWZpbmVkXG4gIH1cbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0uY2hpbGRyZW47XG5jb25zdCBwYWdlcyA9IFtcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXG1lZGlhLWxpc3RcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL21lZGlhLWxpc3QvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvbWVkaWEtbGlzdFwiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmedia-list%2Fpage&page=%2Fmedia-list%2Fpage&appPaths=%2Fmedia-list%2Fpage&pagePath=private-next-app-dir%2Fmedia-list%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/media-list/page.tsx */ \"(rsc)/./src/app/media-list/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtZWRpYS1saXN0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUE4SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccHJvamVjdCBzcG9ydFxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbWVkaWEtbGlzdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\project sport\\media-dashboard-clean\\media-dashboard\\src\\app\\media-list\\page.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBb0giLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXHByb2plY3Qgc3BvcnRcXFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxcXG1lZGlhLWRhc2hib2FyZFxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/media-list/page.tsx */ \"(ssr)/./src/app/media-list/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNwcm9qZWN0JTIwc3BvcnQlNUMlNUNtZWRpYS1kYXNoYm9hcmQtY2xlYW4lNUMlNUNtZWRpYS1kYXNoYm9hcmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNtZWRpYS1saXN0JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHNLQUE4SCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxccHJvamVjdCBzcG9ydFxcXFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXFxcbWVkaWEtZGFzaGJvYXJkXFxcXHNyY1xcXFxhcHBcXFxcbWVkaWEtbGlzdFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cproject%20sport%5C%5Cmedia-dashboard-clean%5C%5Cmedia-dashboard%5C%5Csrc%5C%5Capp%5C%5Cmedia-list%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./public/locales/ar/common.json":
/*!***************************************!*\
  !*** ./public/locales/ar/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"navigation":{"mediaList":"قائمة المواد"},"common":{"welcome":"مرحباً","loading":"جاري التحميل...","loadingSchedule":"جاري تحميل الجدول الأسبوعي...","loadingData":"جاري تحميل البيانات...","save":"حفظ","cancel":"إلغاء","delete":"حذف","edit":"تعديل","add":"إضافة","search":"بحث","filter":"فلترة","export":"تصدير","import":"استيراد","yes":"نعم","no":"لا","ok":"موافق","close":"إغلاق","back":"رجوع","next":"التالي","previous":"السابق","submit":"إرسال","reset":"إعادة تعيين","clear":"مسح","select":"اختيار","selectDate":"اختر التاريخ","selectTime":"اختر الوقت","actions":"إجراءات","status":"الحالة","type":"النوع","name":"الاسم","description":"الوصف","duration":"المدة","startTime":"وقت البداية","endTime":"وقت النهاية","date":"التاريخ","time":"الوقت","content":"المحتوى","switchLanguage":"تبديل اللغة"},"auth":{"login":"تسجيل الدخول","logout":"تسجيل الخروج","username":"اسم المستخدم","password":"كلمة المرور","loginButton":"دخول","loginError":"خطأ في تسجيل الدخول","invalidCredentials":"بيانات الدخول غير صحيحة","sessionExpired":"انتهت صلاحية الجلسة","pleaseLogin":"يرجى تسجيل الدخول"},"dashboard":{"title":"لوحة التحكم","subtitle":"نظام إدارة المواد الإعلامية","overview":"نظرة عامة","recentActivity":"النشاط الحديث","statistics":"الإحصائيات"},"media":{"list":"قائمة المواد الإعلامية","title":"المواد الإعلامية","mediaOverview":"عرض شامل لجميع المواد الإعلامية في قاعدة البيانات","searchFilterExport":"البحث والفلترة والتصدير متاحة","exporting":"جاري التصدير...","exportExcel":"تصدير إلى Excel","searchAndFilter":"البحث والفلترة","searchByName":"البحث بالاسم","searchPlaceholder":"ابحث بالاسم أو الوصف...","searchByCode":"البحث بالكود","codePlaceholder":"ابحث بكود السيجمنت أو معرف المادة...","mediaType":"نوع المادة","mediaStatus":"حالة المادة","sortBy":"ترتيب حسب","newest":"الأحدث","oldest":"الأقدم","byName":"الاسم","byType":"النوع","searchStats":"عرض {{filtered}} من {{total}} مادة إعلامية","noMediaFound":"لا توجد مواد إعلامية","startAdding":"ابدأ بإضافة المواد الإعلامية الخاصة بك","channel":"القناة","segmentCount":"عدد السيجمنتات","description":"الوصف","segments":"السيجمنتات","noCode":"بدون كود","edit":"تعديل","delete":"حذف","scrollToTop":"العودة للأعلى","addNew":"إضافة جديد"},"mediaTypes":{"ALL":"جميع الأنواع","FILM":"Film","SERIES":"Series","PROGRAM":"Program","SONG":"Song","FILLER":"Filler","STING":"Sting","PROMO":"Promo","NEXT":"Next","NOW":"Now","سنعود":"سنعود","عدنا":"عدنا","MINI":"Mini","CROSS":"Cross"},"mediaStatus":{"ALL":"جميع الحالات","VALID":"صالح","REJECTED_CENSORSHIP":"مرفوض - رقابة","REJECTED_TECHNICAL":"مرفوض - تقني","EXPIRED":"منتهي الصلاحية","HOLD":"معلق"},"channels":{"DOCUMENTARY":"الوثائقية","NEWS":"الأخبار","OTHER":"أخرى"},"addMedia":{"title":"إضافة مادة إعلامية جديدة","subtitle":"أدخل تفاصيل المادة الإعلامية","basicInfo":"المعلومات الأساسية","hardDiskNumber":"رقم الهارد ديسك","selectType":"اختر النوع","selectChannel":"اختر القناة","selectStatus":"اختر الحالة","channel":"القناة","source":"المصدر","startDate":"تاريخ البداية","endDate":"تاريخ النهاية","episodeNumber":"رقم الحلقة","seasonNumber":"رقم الموسم","partNumber":"رقم الجزء","description":"الوصف","notes":"ملاحظات","additionalNotes":"ملاحظات إضافية","showInSchedule":"إظهار في الجدول","txDescription":"تحديد ما إذا كانت هذه المادة ستظهر في الجداول الإذاعية","segments":"السيجمنتات","addSegment":"إضافة سيجمنت","segment":"سيجمنت","segmentCode":"كود السيجمنت","segmentCodeExample":"مثال: DDC000055-P1-3","durationAutoCalculated":"المدة (محسوبة تلقائياً)","saveAndAddNew":"حفظ وإضافة جديد","clearFields":"مسح الحقول","segmentCodeRequired":"كود السيجمنت (مطلوب لجميع السيجمنتات)","timeOutRequired":"Time Out للسيجمنت","timeIn":"وقت البداية","timeOut":"وقت النهاية","validationError":"خطأ في التحقق","success":"تم إضافة المادة بنجاح","error":"خطأ في إضافة المادة"},"messages":{"saveSuccess":"تم الحفظ بنجاح","saveError":"خطأ في الحفظ","deleteSuccess":"تم الحذف بنجاح","deleteError":"خطأ في الحذف","updateSuccess":"تم التحديث بنجاح","updateError":"خطأ في التحديث","createSuccess":"تم الإنشاء بنجاح","createError":"خطأ في الإنشاء","importSuccess":"تم الاستيراد بنجاح","importError":"خطأ في الاستيراد","exportSuccess":"تم التصدير بنجاح","exportError":"خطأ في التصدير","confirmDelete":"هل أنت متأكد من الحذف؟","confirmSave":"هل تريد حفظ التغييرات؟","unsavedChanges":"يوجد تغييرات غير محفوظة","operationCompleted":"تمت العملية بنجاح","operationFailed":"فشلت العملية","pleaseWait":"يرجى الانتظار...","processing":"جاري المعالجة...","connecting":"جاري الاتصال...","disconnected":"انقطع الاتصال","reconnecting":"جاري إعادة الاتصال...","noInternetConnection":"لا يوجد اتصال بالإنترنت","serverError":"خطأ في الخادم","networkError":"خطأ في الشبكة","timeoutError":"انتهت مهلة الاتصال","unknownError":"خطأ غير معروف","success":{"mediaAdded":"تم إضافة المادة بنجاح","mediaUpdated":"تم تحديث المادة بنجاح","mediaDeleted":"تم حذف المادة بنجاح","exportSuccess":"تم التصدير بنجاح","importSuccess":"تم الاستيراد بنجاح","scheduleUpdated":"تم تحديث الجدول بنجاح","userCreated":"تم إنشاء المستخدم بنجاح","userUpdated":"تم تحديث المستخدم بنجاح","userDeleted":"تم حذف المستخدم بنجاح","changesSaved":"تم حفظ التغييرات بنجاح"},"error":{"serverConnection":"خطأ في الاتصال بالخادم","mediaNotFound":"المادة غير موجودة","invalidData":"بيانات غير صحيحة","permissionDenied":"ليس لديك صلاحية","exportFailed":"فشل في التصدير","importFailed":"فشل في الاستيراد","unknownError":"حدث خطأ غير معروف","timeFormatError":"خطأ في تنسيق الوقت","calculationError":"خطأ في الحساب"},"info":{"loading":"جاري التحميل...","saving":"جاري الحفظ...","processing":"جاري المعالجة...","exporting":"جاري التصدير...","importing":"جاري الاستيراد..."}},"schedule":{"weekly":"الخريطة الأسبوعية","daily":"الجدول اليومي","searchMedia":"البحث في المواد الإعلامية","resultsCount":"عرض {{count}} من {{total}} مادة","noMedia":"لا توجد مواد إعلامية","changeFilter":"غير المرشح للعثور على مواد أخرى","addNewMedia":"أضف مواد إعلامية جديدة"},"stats":{"totalMedia":"إجمالي المواد","activeUsers":"المستخدمون النشطون"},"admin":{"title":"إدارة المستخدمين","subtitle":"إدارة مستخدمي النظام والصلاحيات","users":"المستخدمون","permissions":"الصلاحيات","addUser":"إضافة مستخدم","editUser":"تعديل مستخدم","deleteUser":"حذف مستخدم","userRole":"دور المستخدم","userStatus":"حالة المستخدم","lastLogin":"آخر تسجيل دخول","createdAt":"تاريخ الإنشاء","updatedAt":"تاريخ التحديث","activeUsers":"المستخدمون النشطون","inactiveUsers":"المستخدمون غير النشطين","totalUsers":"إجمالي المستخدمين","loadingData":"جاري تحميل البيانات...","userManagement":"إدارة المستخدمين","addNewUser":"إضافة مستخدم جديد","username":"اسم المستخدم","password":"كلمة المرور","fullName":"الاسم الكامل","email":"البريد الإلكتروني","phone":"الهاتف","role":"الدور","status":{"valid":"صالح","rejectedCensorship":"مرفوض - رقابة","rejectedTechnical":"مرفوض - تقني","waiting":"في الانتظار"},"actions":"الإجراءات","active":"نشط","inactive":"غير نشط","createUser":"إنشاء مستخدم","cancel":"إلغاء","saveChanges":"حفظ التغييرات","edit":"تعديل","delete":"حذف","noUsers":"لا يوجد مستخدمون","addUsersMessage":"أضف مستخدمين جدد باستخدام زر \\"إضافة مستخدم جديد\\"","rolesExplanation":"شرح الأدوار والصلاحيات","allPermissions":"جميع الصلاحيات","mediaManagement":"إدارة المواد الإعلامية","scheduleManagement":"إدارة الجداول","viewMedia":"عرض المواد الإعلامية","viewSchedules":"عرض الجداول","viewMap":"عرض الخريطة","viewBroadcast":"عرض البث","mapManagement":"إدارة الخريطة","permissionsLabel":"الصلاحيات:","noLoginYet":"لم يسجل دخول بعد","editingUser":"تعديل المستخدم","passwordNote":"(اتركه فارغاً للاحتفاظ بالحالي)","confirmDelete":"هل أنت متأكد من حذف هذا المستخدم؟","roles":{"admin":"مدير النظام","contentManager":"مدير المحتوى","mediaManager":"مدير المواد الإعلامية","scheduler":"مجدول البرامج","fullViewer":"مستخدم رؤية كاملة","dataEntry":"مدخل بيانات","mapScheduler":"مسؤول الخريطة والجدول","viewer":"مستخدم عرض","adminDesc":"وصول كامل للنظام وإدارة المستخدمين","contentManagerDesc":"إدارة المحتوى الإعلامي والجداول","mediaManagerDesc":"إدارة مكتبة المواد الإعلامية والمحتوى","schedulerDesc":"إنشاء وإدارة جداول البث","fullViewerDesc":"عرض جميع بيانات النظام والتقارير","dataEntryDesc":"إضافة وتعديل المحتوى الإعلامي","mapSchedulerDesc":"إدارة خرائط البرامج والجداول الإذاعية اليومية مع عرض قاعدة البيانات بدون تعديل","viewerDesc":"عرض المحتوى فقط بدون إمكانيات التعديل أو الإضافة"},"invalidCredentials":"بيانات الدخول غير صحيحة","welcomeBack":"مرحباً بعودتك","pleaseLogin":"يرجى تسجيل الدخول للمتابعة","userRoles":"أدوار المستخدمين","adminDesc":"صلاحيات كاملة","contentManagerDesc":"إدارة المواد الإعلامية","schedulerDesc":"إدارة الجداول","viewerDesc":"عرض فقط","sessionExpired":"انتهت صلاحية الجلسة","accessDenied":"تم رفض الوصول","insufficientPermissions":"صلاحيات غير كافية","segments":"سيجمنت"}}');

/***/ }),

/***/ "(ssr)/./public/locales/en/common.json":
/*!***************************************!*\
  !*** ./public/locales/en/common.json ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"navigation":{"dashboard":"Dashboard","mediaList":"Media List","addMedia":"Add Media","weeklySchedule":"Weekly Schedule","dailySchedule":"Daily Schedule","reports":"Reports","unifiedSystem":"Import/Export","adminDashboard":"Users","statistics":"Statistics"},"common":{"welcome":"Welcome","loading":"Loading...","loadingSchedule":"Loading weekly schedule...","loadingData":"Loading data...","save":"Save","cancel":"Cancel","delete":"Delete","edit":"Edit","add":"Add","search":"Search","filter":"Filter","export":"Export","import":"Import","yes":"Yes","no":"No","ok":"OK","close":"Close","back":"Back","next":"Next","previous":"Previous","submit":"Submit","reset":"Reset","clear":"Clear","select":"Select","selectDate":"Select Date","selectTime":"Select Time","actions":"Actions","status":"Status","type":"Type","name":"Name","description":"Description","duration":"Duration","startTime":"Start Time","endTime":"End Time","date":"Date","time":"Time","content":"Content","code":"Code","episode":"Episode","season":"Season","part":"Part","segments":"Segments","available":"Available","unavailable":"Unavailable","active":"Active","inactive":"Inactive","valid":"Valid","invalid":"Invalid","expired":"Expired","pending":"Pending","approved":"Approved","rejected":"Rejected","total":"Total","count":"Count","items":"Items","user":"User","noData":"No Data Available","noResults":"No Results Found","error":"Error","success":"Success","warning":"Warning","info":"Information"},"mediaTypes":{"ALL":"All Types","PROGRAM":"Program","SERIES":"Series","FILM":"Film","SONG":"Song","PROMO":"Promo","STING":"Sting","FILLER":"Filler","NEXT":"Next","NOW":"Now","MINI":"Mini","CROSS":"Cross","سنعود":"We\'ll Be Back","عدنا":"We\'re Back"},"roles":{"ADMIN":"System Administrator","CONTENT_MANAGER":"Content Manager","MEDIA_MANAGER":"Database Manager","SCHEDULER":"Program Scheduler","FULL_VIEWER":"Full View User","DATA_ENTRY":"Data Entry","MAP_SCHEDULER":"Map & Schedule Manager","VIEWER":"Viewer","EDITOR":"Editor","OPERATOR":"Operator"},"roleDescriptions":{"ADMIN":"Full system administration and user management permissions","CONTENT_MANAGER":"Manage content and media materials","MEDIA_MANAGER":"Manage media database","SCHEDULER":"Create and edit program schedules","FULL_VIEWER":"View all data and reports","DATA_ENTRY":"Enter and edit basic data","MAP_SCHEDULER":"Manage program map and schedules","VIEWER":"View basic data only"},"mediaStatus":{"ALL":"All Status","VALID":"Valid","REJECTED_CENSORSHIP":"Rejected - Censorship","REJECTED_TECHNICAL":"Rejected - Technical","EXPIRED":"Expired","HOLD":"On Hold"},"channels":{"DOCUMENTARY":"Documentary","NEWS":"News","OTHER":"Other"},"dashboard":{"title":"Dashboard","subtitle":"Media Management System","totalMedia":"Total Media","activeSchedules":"Active Schedules","todayBroadcast":"Today\'s Broadcast","systemUsers":"System Users","recentActivity":"Recent Activity","quickActions":"Quick Actions","statistics":"Statistics","overview":"Overview"},"media":{"title":"Media Management","addNew":"Add New Media","list":"Media List","details":"Media Details","segments":"Segments","addSegment":"Add Segment","segmentCode":"Segment Code","timeIn":"Time In","timeOut":"Time Out","hardDrive":"Hard Drive","server":"Server","notes":"Notes","startDate":"Start Date","endDate":"End Date","showInTX":"Show in TX","episodeNumber":"Episode Number","seasonNumber":"Season Number","partNumber":"Part Number","totalSegments":"Total Segments","validMedia":"Valid Media","expiredMedia":"Expired Media","pendingMedia":"Pending Media","searchByName":"Search by name or description","searchByCode":"Search by media or segment code","searchPlaceholder":"Search for media...","codePlaceholder":"Search by code...","mediaType":"Media Type","mediaStatus":"Status","sortBy":"Sort By","newest":"Newest First","oldest":"Oldest First","byName":"Name (A-Z)","byType":"Type","searchStats":"Showing {{filtered}} of {{total}} media items","noMediaFound":"No media items found","startAdding":"Start by adding a new media item","exportExcel":"Export Excel","exporting":"Exporting...","searchAndFilter":"Search & Filter","mediaOverview":"View and manage media content","searchFilterExport":"You can search, filter and export data","channel":"Channel","segmentCount":"Segment Count","description":"Description","noCode":"[No Code]","edit":"Edit","delete":"Delete","scrollToTop":"Back to top"},"schedule":{"title":"Schedule Management","weekly":"Weekly Program Schedule","daily":"Daily Broadcast Schedule","import":"Import Schedule","export":"Export Schedule","broadcast":"Broadcast","rerun":"Rerun","prime":"Prime","filler":"Filler","empty":"Empty","addRow":"Add Row","deleteRow":"Delete Row","moveUp":"Move Up","moveDown":"Move Down","showMap":"Show Map","hideMap":"Hide Map","saveChanges":"Save Changes","discardChanges":"Discard Changes","importFromTime":"Import from this time","broadcastTime":"Broadcast Time","programMap":"Program Map","scheduleItems":"Schedule Items","availableMedia":"Available Media","scheduledMedia":"Today\'s Scheduled Programs","weeklySchedule":"Weekly Program Schedule","weeklySubtitle":"Weekly program scheduling","importTitle":"Import Broadcast Schedule","importSubtitle":"Import schedule from specific time","importInstructions":"Select Date → Set Time → Import → Edit Media → Export Excel","importSchedule":"Import Schedule","importInstructionsLong":"Select date and time then click \\"Import from this time\\" to display the schedule","loadingSchedule":"Loading weekly schedule...","selectingDate":"Selecting date...","mediaList":"Media List","addTempMedia":"Add Temporary Media","mediaName":"Media name...","duration":"Duration (e.g.: 01:30:00)","notes":"Notes (optional)...","add":"Add","updateReruns":"Update Reruns","allTypes":"All Types","searchMedia":"Search media...","resultsCount":"{{count}} of {{total}} media","noMedia":"No media found","changeFilter":"Try changing filter or search","addNewMedia":"Add new media from user page","deleteTempMedia":"Delete temporary media","liveProgram":"Live","pendingDelivery":"Pending Delivery","temporary":"Temporary","broadcastSchedule":"Broadcast Schedule","exportSchedule":"Export Schedule","selectedWeek":"Selected Week","previousWeek":"← Previous Week","nextWeek":"Next Week →","time":"Time","rerunIndicator":"Rerun - can be deleted for editing","hideSchedule":"Hide Schedule","showSchedule":"Show Schedule","unknown":"Unknown","season":"Season","episode":"Episode","part":"Part","confirmDelete":"Are you sure you want to delete {{type}}: \\"{{name}}\\"?","deleteWarningOriginal":"Warning: Deleting original media will delete all its reruns","deleteWarningRerun":"Warning: Deleting rerun will not affect original media","deleteWarningTemp":"Temporary media will be deleted from schedule","originalMaterial":"Original Material","rerunMaterial":"Rerun","tempMaterial":"Temporary Media","timeConflict":"Time conflict detected! Choose another time.","enterMediaName":"Please enter media name","confirmDeleteTemp":"Do you want to delete this temporary media?","deleteMediaTitle":"🗑️ Delete Media:","deleteOriginalInfo":"Original Media: Permanent deletion with all reruns","deleteRerunInfo":"Reruns: Delete leaving field empty for editing","deleteConfirmInfo":"Confirmation will appear before deletion","confirmDeleteSegment":"Are you sure you want to delete this segment?","usageInstructions":"📋 Usage Instructions:","addMediaTitle":"🎯 Adding Media:","addMediaInstruction1":"Drag media from the right panel to the schedule","addMediaInstruction2":"🔄 Drag media within the schedule to copy to other time slots","addMediaInstruction3":"🎬 Use type filter to filter by media type","addMediaInstruction4":"🔍 Use search to find media quickly","primeTimeTitle":"🌟 Original Media (Prime Time):","primeTimeSchedule1":"Sunday-Wednesday: 18:00-00:00","primeTimeSchedule2":"Thursday-Saturday: 18:00-02:00","primeTimeColor":"🟡 Golden color in schedule","rerunsTitle":"♻️ Automatic Reruns (Two Parts):","rerunsSchedule1":"Sunday-Wednesday:","rerunsSchedule2":"Thursday-Saturday:","rerunsPart1Sun":"Part 1: Same column 00:00-07:59","rerunsPart2Sun":"Part 2: Next column 08:00-17:59","rerunsPart1Thu":"Part 1: Same column 02:00-07:59","rerunsPart2Thu":"Part 2: Next column 08:00-17:59","rerunsColor":"🔘 Gray color - can be deleted for editing","dateManagementTitle":"📅 Date Management:","dateManagementInfo":"Use calendar and buttons to navigate between weeks • Each week is saved separately","importantNoteTitle":"💡 Important Note:","importantNoteInfo":"When adding few media items (1-3 items) in prime time, they will appear with time gaps in reruns to avoid excessive repetition. Add more media for greater variety.","weeklyScheduleTitle":"Weekly Schedule","noWeeklyData":"No data available for weekly schedule","types":{"program":"Program","series":"Series","film":"Film","song":"Song","sting":"Sting","fillIn":"Fill In","filler":"Filler","promo":"Promo","next":"Next","now":"Now","snawod":"We\'ll Be Back","odna":"We\'re Back","mini":"Mini","cross":"Cross"},"startTime":"Start Time"},"days":{"sunday":"Sunday","monday":"Monday","tuesday":"Tuesday","wednesday":"Wednesday","thursday":"Thursday","friday":"Friday","saturday":"Saturday"},"months":{"january":"January","february":"February","march":"March","april":"April","may":"May","june":"June","july":"July","august":"August","september":"September","october":"October","november":"November","december":"December"},"auth":{"login":"Login","logout":"Logout","username":"Username","password":"Password","loginButton":"Sign In","loginError":"Login Error","accessDenied":"Access denied","createUserError":"Error creating user","deleteUserSuccess":"User deleted successfully!","deleteUserError":"Error deleting user","fillRequiredFields":"Please fill all required fields","updateUserSuccess":"User updated successfully!","updateUserError":"Error updating user","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","permissions":"Permissions","allPermissions":"All Permissions","manageMedia":"Manage Media","manageSchedules":"Manage Schedules","viewMedia":"View Media","viewSchedules":"View Schedules","viewMap":"View Map","viewBroadcast":"View Broadcast","manageMap":"Manage Map","roles":{"admin":"System Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Scheduler","fullViewer":"Full View User","dataEntry":"Data Entry","mapScheduler":"Map & Schedule Manager","viewer":"Viewer","adminDesc":"Full permissions for all system parts + user management","contentManagerDesc":"Full media and schedule management (without user management)","mediaManagerDesc":"Media management only (add, edit, delete)","schedulerDesc":"Broadcast schedules and program map management only","fullViewerDesc":"View entire application without editing or adding capabilities","dataEntryDesc":"Data entry and editing only without viewing other parts of the application","mapSchedulerDesc":"Map and daily broadcast schedule management with database viewing without editing","viewerDesc":"View content only without editing or adding capabilities"},"invalidCredentials":"Invalid credentials","welcomeBack":"Welcome back","pleaseLogin":"Please login to continue","userRoles":"User Roles","adminDesc":"Full permissions","contentManagerDesc":"Media management","schedulerDesc":"Schedule management","viewerDesc":"View only","sessionExpired":"Session expired","insufficientPermissions":"Insufficient permissions","status":{"valid":"Valid","rejectedCensorship":"Rejected - Censorship","rejectedTechnical":"Rejected - Technical","waiting":"Waiting"},"segments":"segments"},"admin":{"title":"User Management","subtitle":"Add and edit users","users":"Users","permissions":"Permissions","addUser":"Add User","editUser":"Edit User","deleteUser":"Delete User","userRole":"User Role","userStatus":"User Status","lastLogin":"Last Login","createdAt":"Created At","updatedAt":"Updated At","activeUsers":"Active Users","inactiveUsers":"Inactive Users","totalUsers":"Total Users","loadingData":"Loading data...","userManagement":"User Management","addNewUser":"Add New User","username":"Username","password":"Password","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","status":"Status","actions":"Actions","active":"Active","inactive":"Inactive","createUser":"Create User","cancel":"Cancel","saveChanges":"Save Changes","edit":"Edit","delete":"Delete","noUsers":"No users found","addUsersMessage":"Add new users using the \\"Add New User\\" button","rolesExplanation":"Roles and Permissions Explanation","allPermissions":"All Permissions","mediaManagement":"Media Management","scheduleManagement":"Schedule Management","viewMedia":"View Media","viewSchedules":"View Schedules","viewMap":"View Map","viewBroadcast":"View Broadcast","mapManagement":"Map Management","permissionsLabel":"Permissions:","noLoginYet":"No login yet","editingUser":"Edit User","passwordNote":"(leave empty to keep current)","confirmDelete":"Are you sure you want to delete this user?","userCreated":"User created successfully!","userUpdated":"User updated successfully!","userDeleted":"User deleted successfully!","fillRequired":"Please fill all required fields","createError":"Error creating user","updateError":"Error updating user","deleteError":"Error deleting user","fetchError":"Error fetching user data","serverError":"Server connection error","roles":{"admin":"System Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Program Scheduler","fullViewer":"Full Viewer","dataEntry":"Data Entry","mapScheduler":"Map & Schedule Manager","viewer":"Viewer","adminDesc":"Full system administration and user management permissions","contentManagerDesc":"Manage content and media materials","mediaManagerDesc":"Manage media database","schedulerDesc":"Create and edit program schedules","fullViewerDesc":"View all data and reports","dataEntryDesc":"Enter and edit basic data","mapSchedulerDesc":"Manage program map and schedules","viewerDesc":"View basic data only"}},"permissions":{"MEDIA_READ":"Read Media","MEDIA_CREATE":"Create Media","MEDIA_UPDATE":"Update Media","MEDIA_DELETE":"Delete Media","SCHEDULE_READ":"Read Schedules","SCHEDULE_CREATE":"Create Schedules","SCHEDULE_UPDATE":"Update Schedules","SCHEDULE_DELETE":"Delete Schedules","USER_MANAGEMENT":"User Management","SYSTEM_ADMIN":"System Administration"},"stats":{"totalMedia":"Total Media","validMedia":"Valid Media","maintenanceMedia":"Under Maintenance","activeUsers":"Active Users","efficiency":"Overall Efficiency","operationalCost":"Operational Cost","processingTime":"Average Processing Time","activeOperations":"Active Operations","growthRate":"Growth Rate","healthRate":"Health Rate","issueRate":"Issue Rate","activityRate":"Activity Rate","improvement":"Improvement","dailyAverage":"Daily Average","loadingDummyData":"Loading dummy data"},"messages":{"success":{"mediaAdded":"Media added successfully","mediaUpdated":"Media updated successfully","mediaDeleted":"Media deleted successfully","exportSuccess":"Export completed successfully","importSuccess":"Import completed successfully","scheduleUpdated":"Schedule updated successfully","userCreated":"User created successfully","userUpdated":"User updated successfully","userDeleted":"User deleted successfully","changesSaved":"Changes saved successfully"},"error":{"serverConnection":"Server connection error","mediaNotFound":"Media not found","invalidData":"Invalid data","permissionDenied":"Permission denied","exportFailed":"Export failed","importFailed":"Import failed","unknownError":"Unknown error occurred","timeFormatError":"Time format error","calculationError":"Calculation error"},"info":{"loading":"Loading...","saving":"Saving...","processing":"Processing...","exporting":"Exporting...","importing":"Importing..."},"admin":{"title":"User Management","subtitle":"Manage system users and permissions","loadingData":"Loading data...","userManagement":"User Management","addNewUser":"Add New User","username":"Username","password":"Password","fullName":"Full Name","email":"Email","phone":"Phone","role":"Role","createUser":"Create User","cancel":"Cancel","lastLogin":"Last Login","actions":"Actions","noLoginYet":"No login yet","confirmDelete":"Are you sure you want to delete this user?","noUsers":"No users found","addUsersMessage":"Start by adding new users to the system","rolesExplanation":"Roles and Permissions","permissions":"Permissions","allPermissions":"All Permissions","manageMedia":"Manage Media","manageSchedules":"Manage Schedules","viewSchedules":"View Schedules","viewMedia":"View Media","viewMap":"View Map","viewBroadcast":"View Broadcast","manageMap":"Manage Map","editingUser":"Editing User","passwordNote":"(leave empty to keep current)","status":"Status","active":"Active","inactive":"Inactive","saveChanges":"Save Changes","roles":{"admin":"Administrator","contentManager":"Content Manager","mediaManager":"Media Manager","scheduler":"Scheduler","fullViewer":"Full Viewer","dataEntry":"Data Entry","mapScheduler":"Map Scheduler","viewer":"Viewer","adminDesc":"Full system access and user management","contentManagerDesc":"Manage media content and schedules","mediaManagerDesc":"Manage media library and content","schedulerDesc":"Create and manage broadcast schedules","fullViewerDesc":"View all system data and reports","dataEntryDesc":"Add and edit media content","mapSchedulerDesc":"Manage program maps and schedules","viewerDesc":"Basic viewing permissions"}}},"home":{"title":"Prime-X","subtitle":"Media Management System","loading":"Loading...","autoRedirect":"Or wait for automatic redirect to dashboard","quickNavigation":"Quick Navigation","dashboard":"Dashboard","dailySchedule":"Daily Broadcast Schedule","weeklySchedule":"Program Schedule","mediaList":"Media List","addMedia":"Add Media","adminPanel":"User Management"},"reports":{"title":"Broadcast Reports","subtitle":"Search and analyze broadcast content","searchFilters":"Search Filters","mediaType":"Media Type","allTypes":"All Types","mediaName":"Media Name","mediaCode":"Media Code","source":"Source","both":"Both","weekly":"Weekly Schedule","daily":"Daily Schedule","dateFrom":"From Date","dateTo":"To Date","search":"Search","exportExcel":"Export Excel","showStatistics":"Show Statistics","hideStatistics":"Hide Statistics","searchResults":"Search Results","noResults":"No results found","searchMessage":"Use the filters above to search for broadcast content","resultsCount":"{{count}} results","statistics":"Media Statistics","totalItems":"Total Items","totalDuration":"Total Duration","count":"Count","duration":"Duration","percentage":"Percentage","loadingMedia":"Loading media...","searchError":"Search error","exportError":"Export error","exportSuccess":"Export successful","date":"Date","time":"Time","type":"Type","name":"Name","code":"Code","rerun":"Rerun","temporary":"Temporary","original":"Original","programsFilmsSeries":"Programs, Films & Series","promosFillersSting":"Promos, Fillers & Stings","detailedStatistics":"Detailed Statistics"},"unified":{"title":"Unified Import/Export System","subtitle":"Manage import and export of media data","importExport":"Import/Export","selectFile":"Please select a file","uploadFile":"Upload File","exportData":"Export Data","fileSelected":"File Selected","processing":"Processing...","success":"Operation Successful","error":"An Error Occurred","noFileSelected":"No File Selected","invalidFileType":"Invalid File Type","mediaLibrary":"Media Library","totalMedia":"Total Media","searchMedia":"Search media...","filterByType":"Filter by Type","loadingMedia":"Loading media...","importSuccess":"Import successful","itemsImported":"items imported","importError":"Import error occurred","exportSuccess":"File exported successfully","exportError":"Export error","noMedia":"No media available","result":"Result","searchDescription":"Search the program schedule and daily broadcast schedule easily","noResultsMessage":"No results to display. Please set search criteria and click \\"Search & Statistics\\".","searchAndStats":"Search & Statistics","dailyScheduleHint":"Choose \\"Daily Schedule\\" to search for promos, fillers, and stings","broadcastDayHint":"Broadcast day starts at 08:00 AM","broadcastDayEndHint":"Broadcast day ends at 07:59 AM next day","date":"Date","time":"Time","type":"Type","name":"Name","code":"Code","rerun":"Rerun","temporary":"Temporary"},"statistics":{"title":"System Statistics","subtitle":"Detailed reports and statistics","loadingStats":"Loading statistics...","totalMedia":"Total Media","allRegisteredMedia":"All registered media","totalSegments":"Total Segments","allSegments":"All segments","differentTypes":"Different Types","mediaTypes":"Media types","averageSegments":"Average Segments","perMedia":"Per media","distributionByType":"Distribution by Type","distributionByStatus":"Distribution by Status","recentlyAdded":"Recently Added","status":{"valid":"Valid for broadcast","rejectedCensorship":"Rejected (Censorship) - Content review needed","rejectedTechnical":"Rejected (Technical) - Quality issues","waiting":"Awaiting review"}},"addMedia":{"title":"Add New Media","subtitle":"After saving, you\'ll stay on this page to add another media","basicInfo":"Basic Information","hardDiskNumber":"Hard Disk Number","selectType":"Select Type","channel":"Channel","selectChannel":"Select Channel","selectStatus":"Select Status","source":"Source","startDate":"Start Date","endDate":"End Date","segments":"Segments","addSegment":"Add Segment","segment":"Segment","segmentCode":"Segment Code","saveAndAddNew":"Save and Add New","clearFields":"Clear Fields","description":"Media Description","notes":"Notes","additionalNotes":"Additional notes","showInSchedule":"Show in Schedule and Broadcast Lists","txDescription":"When enabled, this media will appear in the sidebar of schedule and broadcast tables","episodeNumber":"Episode Number","seasonNumber":"Season Number","partNumber":"Part Number","durationAutoCalculated":"Duration (Auto-calculated)"}}');

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5b8c52149260\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjViOGM1MjE0OTI2MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lib_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../lib/i18n */ \"(ssr)/./src/lib/i18n.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n // Initialize i18n\nfunction RootLayout({ children }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"RootLayout.useEffect\": ()=>{\n            // Set initial language and direction from localStorage or default to Arabic\n            const savedLang = localStorage.getItem('language') || 'ar';\n            document.documentElement.lang = savedLang;\n            document.documentElement.dir = savedLang === 'ar' ? 'rtl' : 'ltr';\n            // Add language class to body for CSS targeting\n            document.body.className = `lang-${savedLang}`;\n            // Set font family based on language\n            if (savedLang === 'ar') {\n                document.body.style.fontFamily = 'Cairo, Arial, sans-serif';\n            } else {\n                document.body.style.fontFamily = 'Inter, Arial, sans-serif';\n            }\n        }\n    }[\"RootLayout.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"نظام إدارة المحتوى الإعلامي | Media Management System\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"نظام متكامل لإدارة المحتوى الإعلامي والخريطة البرامجية | Integrated Media Content Management System\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"black-translucent\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        href: \"https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                style: {\n                    margin: 0,\n                    padding: 0,\n                    fontFamily: 'Cairo, Inter, Arial, sans-serif'\n                },\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/media-list/page.tsx":
/*!*************************************!*\
  !*** ./src/app/media-list/page.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MediaListPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(ssr)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(ssr)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(ssr)/./src/hooks/useTranslatedToast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction MediaListPage() {\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_5__.useTranslatedToast)();\n    const [mediaItems, setMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filteredItems, setFilteredItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [codeSearchTerm, setCodeSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [selectedStatus, setSelectedStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ALL');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('newest');\n    const [isExporting, setIsExporting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showScrollToTop, setShowScrollToTop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            fetchMediaItems();\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    // مراقبة التمرير لإظهار/إخفاء زر العودة لأعلى\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            const handleScroll = {\n                \"MediaListPage.useEffect.handleScroll\": ()=>{\n                    setShowScrollToTop(window.scrollY > 300);\n                }\n            }[\"MediaListPage.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"MediaListPage.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"MediaListPage.useEffect\"];\n        }\n    }[\"MediaListPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MediaListPage.useEffect\": ()=>{\n            filterAndSortItems();\n        }\n    }[\"MediaListPage.useEffect\"], [\n        mediaItems,\n        searchTerm,\n        codeSearchTerm,\n        selectedType,\n        selectedStatus,\n        sortBy\n    ]);\n    // دالة العودة لأعلى الصفحة\n    const scrollToTop = ()=>{\n        window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n        });\n    };\n    const fetchMediaItems = async ()=>{\n        try {\n            const response = await fetch('/api/media');\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(result.data);\n            } else {\n                setError(result.error);\n            }\n        } catch (error) {\n            console.error('Error fetching media items:', error);\n            setError(t('messages.networkError'));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filterAndSortItems = ()=>{\n        let filtered = [\n            ...mediaItems\n        ];\n        // البحث بالاسم\n        if (searchTerm) {\n            filtered = filtered.filter((item)=>item.name.toLowerCase().includes(searchTerm.toLowerCase()) || item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // البحث بالكود (في السيجمانت)\n        if (codeSearchTerm) {\n            filtered = filtered.filter((item)=>{\n                // البحث في معرف المادة\n                if (item.id.toLowerCase().includes(codeSearchTerm.toLowerCase())) {\n                    return true;\n                }\n                // البحث في أكواد السيجمانت\n                if (item.segments && item.segments.length > 0) {\n                    return item.segments.some((segment)=>segment.code && segment.code.toLowerCase().includes(codeSearchTerm.toLowerCase()));\n                }\n                return false;\n            });\n        }\n        // فلترة بالنوع\n        if (selectedType !== 'ALL') {\n            filtered = filtered.filter((item)=>item.type === selectedType);\n        }\n        // فلترة بالحالة\n        if (selectedStatus !== 'ALL') {\n            filtered = filtered.filter((item)=>item.status === selectedStatus);\n        }\n        // الترتيب\n        switch(sortBy){\n            case 'newest':\n                filtered.sort((a, b)=>new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n                break;\n            case 'oldest':\n                filtered.sort((a, b)=>new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());\n                break;\n            case 'name':\n                filtered.sort((a, b)=>a.name.localeCompare(b.name, 'ar'));\n                break;\n            case 'type':\n                filtered.sort((a, b)=>a.type.localeCompare(b.type));\n                break;\n        }\n        setFilteredItems(filtered);\n    };\n    const deleteMediaItem = async (id)=>{\n        if (!confirm(t('messages.confirmDelete'))) return;\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = `token_${user.id || 'unknown'}_${user.role || 'unknown'}`;\n            console.log('Sending delete request with token:', tokenWithRole);\n            const response = await fetch(`/api/media?id=${id}`, {\n                method: 'DELETE',\n                headers: {\n                    'Authorization': `Bearer ${tokenWithRole}`\n                }\n            });\n            const result = await response.json();\n            if (result.success) {\n                setMediaItems(mediaItems.filter((item)=>item.id !== id));\n                showSuccessToast('mediaDeleted');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error deleting media item:', error);\n            showErrorToast('unknownError');\n        }\n    };\n    const exportToExcel = async ()=>{\n        setIsExporting(true);\n        try {\n            console.log('🚀 بدء تصدير قاعدة البيانات...');\n            // إرسال الفلاتر الحالية مع طلب التصدير\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (codeSearchTerm) params.append('codeSearch', codeSearchTerm);\n            if (selectedType !== 'ALL') params.append('type', selectedType);\n            if (selectedStatus !== 'ALL') params.append('status', selectedStatus);\n            const apiUrl = `/api/export-unified${params.toString() ? '?' + params.toString() : ''}`;\n            console.log('📊 تصدير مع الفلاتر:', apiUrl);\n            const response = await fetch(apiUrl);\n            if (!response.ok) {\n                throw new Error(t('messages.exportError'));\n            }\n            // الحصول على الملف كـ blob\n            const blob = await response.blob();\n            // إنشاء رابط التحميل\n            const downloadUrl = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = downloadUrl;\n            // تحديد اسم الملف\n            const fileName = `Media_Database_${new Date().toISOString().split('T')[0]}.xlsx`;\n            link.download = fileName;\n            // تحميل الملف\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            // تنظيف الذاكرة\n            window.URL.revokeObjectURL(downloadUrl);\n            console.log('✅ تم تصدير قاعدة البيانات بنجاح');\n            showSuccessToast('exportSuccess');\n        } catch (error) {\n            console.error('❌ خطأ في التصدير:', error);\n            showErrorToast('exportFailed');\n        } finally{\n            setIsExporting(false);\n        }\n    };\n    const getTypeLabel = (type)=>{\n        return t(`mediaTypes.${type}`) || type;\n    };\n    const getStatusLabel = (status)=>{\n        return t(`mediaStatus.${status}`) || status;\n    };\n    const getChannelLabel = (channel)=>{\n        return t(`channels.${channel}`) || channel;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"⏳ \",\n                    t('common.loading')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 262,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 255,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    color: 'white',\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"❌ \",\n                    t('common.error'),\n                    \": \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                lineNumber: 276,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 269,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'MEDIA_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: t('media.list'),\n            subtitle: t('media.title'),\n            icon: \"\\uD83C\\uDFAC\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                        padding: '20px',\n                        borderRadius: '12px',\n                        marginBottom: '25px',\n                        color: 'white',\n                        textAlign: 'center',\n                        boxShadow: '0 6px 20px rgba(102, 126, 234, 0.3)'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                margin: '0 0 10px 0',\n                                fontSize: '1.3rem'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCCA \",\n                                t('media.list')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: '0 0 8px 0',\n                                fontSize: '1rem',\n                                opacity: 0.9\n                            },\n                            children: t('media.mediaOverview')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 295,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                margin: '0',\n                                fontSize: '0.9rem',\n                                opacity: 0.8\n                            },\n                            children: [\n                                \"✨ \",\n                                t('media.searchFilterExport')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'flex',\n                        gap: '15px',\n                        justifyContent: 'center',\n                        marginBottom: '25px',\n                        flexWrap: 'wrap'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: exportToExcel,\n                        disabled: isExporting,\n                        style: {\n                            background: isExporting ? 'linear-gradient(45deg, #6c757d, #5a6268)' : 'linear-gradient(45deg, #17a2b8, #138496)',\n                            color: 'white',\n                            padding: '12px 25px',\n                            borderRadius: '25px',\n                            border: 'none',\n                            fontWeight: 'bold',\n                            cursor: isExporting ? 'not-allowed' : 'pointer',\n                            boxShadow: '0 4px 15px rgba(23,162,184,0.3)',\n                            fontSize: '1rem'\n                        },\n                        children: isExporting ? '⏳ ' + t('media.exporting') : '📊 ' + t('media.exportExcel')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                        lineNumber: 305,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: '25px',\n                        marginBottom: '25px',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: '#f3f4f6',\n                                marginBottom: '20px',\n                                fontSize: '1.3rem'\n                            },\n                            children: [\n                                \"\\uD83D\\uDD0D \",\n                                t('media.searchAndFilter')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.searchByName'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('media.searchPlaceholder'),\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: 'rtl',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.searchByCode'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: t('media.codePlaceholder'),\n                                            value: codeSearchTerm,\n                                            onChange: (e)=>setCodeSearchTerm(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: 'rtl',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.mediaType'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedType,\n                                            onChange: (e)=>setSelectedType(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ALL\",\n                                                    children: t('mediaTypes.ALL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 408,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"FILM\",\n                                                    children: t('mediaTypes.FILM')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SERIES\",\n                                                    children: t('mediaTypes.SERIES')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 410,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"PROGRAM\",\n                                                    children: t('mediaTypes.PROGRAM')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"SONG\",\n                                                    children: t('mediaTypes.SONG')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"FILLER\",\n                                                    children: t('mediaTypes.FILLER')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"STING\",\n                                                    children: t('mediaTypes.STING')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"PROMO\",\n                                                    children: t('mediaTypes.PROMO')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NEXT\",\n                                                    children: t('mediaTypes.NEXT')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"NOW\",\n                                                    children: t('mediaTypes.NOW')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"سنعود\",\n                                                    children: t('mediaTypes.سنعود')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"عدنا\",\n                                                    children: t('mediaTypes.عدنا')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"MINI\",\n                                                    children: t('mediaTypes.MINI')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"CROSS\",\n                                                    children: t('mediaTypes.CROSS')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.mediaStatus'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: selectedStatus,\n                                            onChange: (e)=>setSelectedStatus(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"ALL\",\n                                                    children: t('mediaStatus.ALL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 444,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"VALID\",\n                                                    children: t('mediaStatus.VALID')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"REJECTED_CENSORSHIP\",\n                                                    children: t('mediaStatus.REJECTED_CENSORSHIP')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"REJECTED_TECHNICAL\",\n                                                    children: t('mediaStatus.REJECTED_TECHNICAL')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"EXPIRED\",\n                                                    children: t('mediaStatus.EXPIRED')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"HOLD\",\n                                                    children: t('mediaStatus.HOLD')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            style: {\n                                                display: 'block',\n                                                marginBottom: '5px',\n                                                color: '#f3f4f6',\n                                                fontSize: '0.9rem'\n                                            },\n                                            children: [\n                                                t('media.sortBy'),\n                                                \":\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            style: {\n                                                width: '100%',\n                                                padding: '10px',\n                                                border: '1px solid #6b7280',\n                                                borderRadius: '8px',\n                                                fontSize: '1rem',\n                                                direction: isRTL ? 'rtl' : 'ltr',\n                                                color: 'white',\n                                                background: '#1f2937'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"newest\",\n                                                    children: t('media.newest')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"oldest\",\n                                                    children: t('media.oldest')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"name\",\n                                                    children: t('media.byName')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"type\",\n                                                    children: t('media.byType')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                            lineNumber: 458,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                marginTop: '15px',\n                                padding: '10px',\n                                background: '#1f2937',\n                                borderRadius: '8px',\n                                textAlign: 'center',\n                                color: '#d1d5db',\n                                border: '1px solid #6b7280'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCCA \",\n                                t('media.searchStats', {\n                                    filtered: filteredItems.length,\n                                    total: mediaItems.length\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 9\n                }, this),\n                filteredItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#4a5568',\n                        borderRadius: '15px',\n                        padding: '50px',\n                        textAlign: 'center',\n                        border: '1px solid #6b7280'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            style: {\n                                color: '#d1d5db',\n                                fontSize: '1.5rem'\n                            },\n                            children: [\n                                \"\\uD83D\\uDCED \",\n                                t('media.noMediaFound')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: '#a0aec0',\n                                marginTop: '10px'\n                            },\n                            children: t('media.startAdding')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 496,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gap: '20px'\n                    },\n                    children: filteredItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                background: '#4a5568',\n                                borderRadius: '15px',\n                                padding: '25px',\n                                border: '1px solid #6b7280'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gridTemplateColumns: '1fr auto',\n                                    gap: '20px',\n                                    alignItems: 'start'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    marginBottom: '15px',\n                                                    fontSize: '1.4rem'\n                                                },\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    display: 'grid',\n                                                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                    gap: '15px',\n                                                    marginBottom: '15px',\n                                                    color: '#d1d5db'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('common.type'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getTypeLabel(item.type)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('media.channel'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getChannelLabel(item.channel)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('common.status'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            getStatusLabel(item.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                children: [\n                                                                    t('media.segmentCount'),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \" \",\n                                                            item.segments.length\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 21\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    color: '#a0aec0',\n                                                    marginBottom: '10px'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t('media.description'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \" \",\n                                                    item.description\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.segments.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    marginTop: '15px',\n                                                    color: '#d1d5db'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: [\n                                                            t('media.segments'),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        style: {\n                                                            display: 'grid',\n                                                            gap: '8px',\n                                                            marginTop: '8px'\n                                                        },\n                                                        children: item.segments.map((segment, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                style: {\n                                                                    background: '#1f2937',\n                                                                    padding: '8px 12px',\n                                                                    borderRadius: '8px',\n                                                                    fontSize: '0.9rem',\n                                                                    color: '#d1d5db',\n                                                                    border: '1px solid #6b7280'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            \"#\",\n                                                                            segment.segmentNumber\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    \" -\",\n                                                                    segment.code && segment.code.trim() !== '' ? ` ${segment.code} - ` : ` [${t('media.noCode')}] - `,\n                                                                    segment.timeIn,\n                                                                    \" → \",\n                                                                    segment.timeOut,\n                                                                    \" (\",\n                                                                    segment.duration,\n                                                                    \")\"\n                                                                ]\n                                                            }, `${item.id}_segment_${index}`, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                                lineNumber: 551,\n                                                                columnNumber: 29\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            flexDirection: 'column',\n                                            gap: '10px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    // توجيه لصفحة التعديل مع معرف المادة\n                                                    window.location.href = `/edit-media?id=${item.id}`;\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem',\n                                                    marginBottom: '5px'\n                                                },\n                                                children: [\n                                                    \"✏️ \",\n                                                    t('media.edit')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>deleteMediaItem(item.id),\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #dc3545, #c82333)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '8px 16px',\n                                                    cursor: 'pointer',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDDD1️ \",\n                                                    t('media.delete')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 17\n                            }, this)\n                        }, item.id, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 511,\n                    columnNumber: 11\n                }, this),\n                showScrollToTop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: scrollToTop,\n                    style: {\n                        position: 'fixed',\n                        bottom: '30px',\n                        right: '30px',\n                        width: '60px',\n                        height: '60px',\n                        borderRadius: '50%',\n                        background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                        color: 'white',\n                        border: 'none',\n                        cursor: 'pointer',\n                        fontSize: '24px',\n                        boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',\n                        zIndex: 1000,\n                        transition: 'all 0.3s ease',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                    },\n                    onMouseEnter: (e)=>{\n                        e.currentTarget.style.transform = 'scale(1.1)';\n                        e.currentTarget.style.boxShadow = '0 6px 16px rgba(0, 123, 255, 0.4)';\n                    },\n                    onMouseLeave: (e)=>{\n                        e.currentTarget.style.transform = 'scale(1)';\n                        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 123, 255, 0.3)';\n                    },\n                    title: t('media.scrollToTop'),\n                    children: \"⬆️\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 613,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n                    lineNumber: 647,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\media-list\\\\page.tsx\",\n        lineNumber: 282,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL21lZGlhLWxpc3QvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNPO0FBQ1E7QUFDRztBQUNFO0FBNkJqRCxTQUFTTTtJQUN0QixNQUFNLEVBQUVDLENBQUMsRUFBRUMsVUFBVSxFQUFFQyxLQUFLLEVBQUUsR0FBR0wsMkVBQWlCQTtJQUNsRCxNQUFNLEVBQUVNLGdCQUFnQixFQUFFQyxjQUFjLEVBQUVDLGNBQWMsRUFBRSxHQUFHUCw2RUFBa0JBO0lBRS9FLE1BQU0sQ0FBQ1EsWUFBWUMsY0FBYyxHQUFHZCwrQ0FBUUEsQ0FBYyxFQUFFO0lBQzVELE1BQU0sQ0FBQ2UsZUFBZUMsaUJBQWlCLEdBQUdoQiwrQ0FBUUEsQ0FBYyxFQUFFO0lBQ2xFLE1BQU0sQ0FBQ2lCLFNBQVNDLFdBQVcsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ21CLE9BQU9DLFNBQVMsR0FBR3BCLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNxQixZQUFZQyxjQUFjLEdBQUd0QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUN1QixnQkFBZ0JDLGtCQUFrQixHQUFHeEIsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDeUIsY0FBY0MsZ0JBQWdCLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUMyQixnQkFBZ0JDLGtCQUFrQixHQUFHNUIsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDNkIsUUFBUUMsVUFBVSxHQUFHOUIsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDK0IsYUFBYUMsZUFBZSxHQUFHaEMsK0NBQVFBLENBQUM7SUFFL0MsTUFBTSxDQUFDaUMsaUJBQWlCQyxtQkFBbUIsR0FBR2xDLCtDQUFRQSxDQUFDO0lBRXZEQyxnREFBU0E7bUNBQUM7WUFDUmtDO1FBQ0Y7a0NBQUcsRUFBRTtJQUVMLDhDQUE4QztJQUM5Q2xDLGdEQUFTQTttQ0FBQztZQUNSLE1BQU1tQzt3REFBZTtvQkFDbkJGLG1CQUFtQkcsT0FBT0MsT0FBTyxHQUFHO2dCQUN0Qzs7WUFFQUQsT0FBT0UsZ0JBQWdCLENBQUMsVUFBVUg7WUFDbEM7MkNBQU8sSUFBTUMsT0FBT0csbUJBQW1CLENBQUMsVUFBVUo7O1FBQ3BEO2tDQUFHLEVBQUU7SUFFTG5DLGdEQUFTQTttQ0FBQztZQUNSd0M7UUFDRjtrQ0FBRztRQUFDNUI7UUFBWVE7UUFBWUU7UUFBZ0JFO1FBQWNFO1FBQWdCRTtLQUFPO0lBRWpGLDJCQUEyQjtJQUMzQixNQUFNYSxjQUFjO1FBQ2xCTCxPQUFPTSxRQUFRLENBQUM7WUFDZEMsS0FBSztZQUNMQyxVQUFVO1FBQ1o7SUFDRjtJQUVBLE1BQU1WLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTVcsV0FBVyxNQUFNQyxNQUFNO1lBQzdCLE1BQU1DLFNBQVMsTUFBTUYsU0FBU0csSUFBSTtZQUVsQyxJQUFJRCxPQUFPRSxPQUFPLEVBQUU7Z0JBQ2xCcEMsY0FBY2tDLE9BQU9HLElBQUk7WUFDM0IsT0FBTztnQkFDTC9CLFNBQVM0QixPQUFPN0IsS0FBSztZQUN2QjtRQUNGLEVBQUUsT0FBT0EsT0FBTztZQUNkaUMsUUFBUWpDLEtBQUssQ0FBQywrQkFBK0JBO1lBQzdDQyxTQUFTYixFQUFFO1FBQ2IsU0FBVTtZQUNSVyxXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU11QixxQkFBcUI7UUFDekIsSUFBSVksV0FBVztlQUFJeEM7U0FBVztRQUU5QixlQUFlO1FBQ2YsSUFBSVEsWUFBWTtZQUNkZ0MsV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQSxPQUN6QkEsS0FBS0MsSUFBSSxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ3JDLFdBQVdvQyxXQUFXLE9BQ3RERixLQUFLSSxXQUFXLElBQUlKLEtBQUtJLFdBQVcsQ0FBQ0YsV0FBVyxHQUFHQyxRQUFRLENBQUNyQyxXQUFXb0MsV0FBVztRQUV2RjtRQUVBLDhCQUE4QjtRQUM5QixJQUFJbEMsZ0JBQWdCO1lBQ2xCOEIsV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQTtnQkFDekIsdUJBQXVCO2dCQUN2QixJQUFJQSxLQUFLSyxFQUFFLENBQUNILFdBQVcsR0FBR0MsUUFBUSxDQUFDbkMsZUFBZWtDLFdBQVcsS0FBSztvQkFDaEUsT0FBTztnQkFDVDtnQkFFQSwyQkFBMkI7Z0JBQzNCLElBQUlGLEtBQUtNLFFBQVEsSUFBSU4sS0FBS00sUUFBUSxDQUFDQyxNQUFNLEdBQUcsR0FBRztvQkFDN0MsT0FBT1AsS0FBS00sUUFBUSxDQUFDRSxJQUFJLENBQUNDLENBQUFBLFVBQ3hCQSxRQUFRQyxJQUFJLElBQUlELFFBQVFDLElBQUksQ0FBQ1IsV0FBVyxHQUFHQyxRQUFRLENBQUNuQyxlQUFla0MsV0FBVztnQkFFbEY7Z0JBRUEsT0FBTztZQUNUO1FBQ0Y7UUFFQSxlQUFlO1FBQ2YsSUFBSWhDLGlCQUFpQixPQUFPO1lBQzFCNEIsV0FBV0EsU0FBU0MsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLVyxJQUFJLEtBQUt6QztRQUNuRDtRQUVBLGdCQUFnQjtRQUNoQixJQUFJRSxtQkFBbUIsT0FBTztZQUM1QjBCLFdBQVdBLFNBQVNDLE1BQU0sQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS1ksTUFBTSxLQUFLeEM7UUFDckQ7UUFFQSxVQUFVO1FBQ1YsT0FBUUU7WUFDTixLQUFLO2dCQUNId0IsU0FBU2UsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU0sSUFBSUMsS0FBS0QsRUFBRUUsU0FBUyxFQUFFQyxPQUFPLEtBQUssSUFBSUYsS0FBS0YsRUFBRUcsU0FBUyxFQUFFQyxPQUFPO2dCQUN2RjtZQUNGLEtBQUs7Z0JBQ0hwQixTQUFTZSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTSxJQUFJQyxLQUFLRixFQUFFRyxTQUFTLEVBQUVDLE9BQU8sS0FBSyxJQUFJRixLQUFLRCxFQUFFRSxTQUFTLEVBQUVDLE9BQU87Z0JBQ3ZGO1lBQ0YsS0FBSztnQkFDSHBCLFNBQVNlLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNRCxFQUFFYixJQUFJLENBQUNrQixhQUFhLENBQUNKLEVBQUVkLElBQUksRUFBRTtnQkFDckQ7WUFDRixLQUFLO2dCQUNISCxTQUFTZSxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsRUFBRUgsSUFBSSxDQUFDUSxhQUFhLENBQUNKLEVBQUVKLElBQUk7Z0JBQ25EO1FBQ0o7UUFFQWxELGlCQUFpQnFDO0lBQ25CO0lBRUEsTUFBTXNCLGtCQUFrQixPQUFPZjtRQUM3QixJQUFJLENBQUNnQixRQUFRckUsRUFBRSw0QkFBNEI7UUFFM0MsSUFBSTtZQUNGLG1DQUFtQztZQUNuQyxNQUFNc0UsT0FBT0MsS0FBS0MsS0FBSyxDQUFDQyxhQUFhQyxPQUFPLENBQUMsV0FBVztZQUN4RCxNQUFNQyxnQkFBZ0IsQ0FBQyxNQUFNLEVBQUVMLEtBQUtqQixFQUFFLElBQUksVUFBVSxDQUFDLEVBQUVpQixLQUFLTSxJQUFJLElBQUksV0FBVztZQUUvRS9CLFFBQVFnQyxHQUFHLENBQUMsc0NBQXNDRjtZQUVsRCxNQUFNcEMsV0FBVyxNQUFNQyxNQUFNLENBQUMsY0FBYyxFQUFFYSxJQUFJLEVBQUU7Z0JBQ2xEeUIsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVKLGVBQWU7Z0JBQzVDO1lBQ0Y7WUFFQSxNQUFNbEMsU0FBUyxNQUFNRixTQUFTRyxJQUFJO1lBRWxDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEJwQyxjQUFjRCxXQUFXeUMsTUFBTSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLSyxFQUFFLEtBQUtBO2dCQUNwRGxELGlCQUFpQjtZQUNuQixPQUFPO2dCQUNMQyxlQUFlO1lBQ2pCO1FBQ0YsRUFBRSxPQUFPUSxPQUFPO1lBQ2RpQyxRQUFRakMsS0FBSyxDQUFDLDhCQUE4QkE7WUFDNUNSLGVBQWU7UUFDakI7SUFDRjtJQUVBLE1BQU00RSxnQkFBZ0I7UUFDcEJ2RCxlQUFlO1FBQ2YsSUFBSTtZQUNGb0IsUUFBUWdDLEdBQUcsQ0FBQztZQUVaLHVDQUF1QztZQUN2QyxNQUFNSSxTQUFTLElBQUlDO1lBQ25CLElBQUlwRSxZQUFZbUUsT0FBT0UsTUFBTSxDQUFDLFVBQVVyRTtZQUN4QyxJQUFJRSxnQkFBZ0JpRSxPQUFPRSxNQUFNLENBQUMsY0FBY25FO1lBQ2hELElBQUlFLGlCQUFpQixPQUFPK0QsT0FBT0UsTUFBTSxDQUFDLFFBQVFqRTtZQUNsRCxJQUFJRSxtQkFBbUIsT0FBTzZELE9BQU9FLE1BQU0sQ0FBQyxVQUFVL0Q7WUFFdEQsTUFBTWdFLFNBQVMsQ0FBQyxtQkFBbUIsRUFBRUgsT0FBT0ksUUFBUSxLQUFLLE1BQU1KLE9BQU9JLFFBQVEsS0FBSyxJQUFJO1lBQ3ZGeEMsUUFBUWdDLEdBQUcsQ0FBQyx3QkFBd0JPO1lBRXBDLE1BQU03QyxXQUFXLE1BQU1DLE1BQU00QztZQUU3QixJQUFJLENBQUM3QyxTQUFTK0MsRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU12RixFQUFFO1lBQ3BCO1lBRUEsMkJBQTJCO1lBQzNCLE1BQU13RixPQUFPLE1BQU1qRCxTQUFTaUQsSUFBSTtZQUVoQyxxQkFBcUI7WUFDckIsTUFBTUMsY0FBYzNELE9BQU80RCxHQUFHLENBQUNDLGVBQWUsQ0FBQ0g7WUFDL0MsTUFBTUksT0FBT0MsU0FBU0MsYUFBYSxDQUFDO1lBQ3BDRixLQUFLRyxJQUFJLEdBQUdOO1lBRVosa0JBQWtCO1lBQ2xCLE1BQU1PLFdBQVcsQ0FBQyxlQUFlLEVBQUUsSUFBSWhDLE9BQU9pQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQztZQUNoRk4sS0FBS08sUUFBUSxHQUFHSDtZQUVoQixjQUFjO1lBQ2RILFNBQVNPLElBQUksQ0FBQ0MsV0FBVyxDQUFDVDtZQUMxQkEsS0FBS1UsS0FBSztZQUNWVCxTQUFTTyxJQUFJLENBQUNHLFdBQVcsQ0FBQ1g7WUFFMUIsZ0JBQWdCO1lBQ2hCOUQsT0FBTzRELEdBQUcsQ0FBQ2MsZUFBZSxDQUFDZjtZQUUzQjVDLFFBQVFnQyxHQUFHLENBQUM7WUFDWjFFLGlCQUFpQjtRQUVuQixFQUFFLE9BQU9TLE9BQU87WUFDZGlDLFFBQVFqQyxLQUFLLENBQUMscUJBQXFCQTtZQUNuQ1IsZUFBZTtRQUNqQixTQUFVO1lBQ1JxQixlQUFlO1FBQ2pCO0lBQ0Y7SUFJQSxNQUFNZ0YsZUFBZSxDQUFDOUM7UUFDcEIsT0FBTzNELEVBQUUsQ0FBQyxXQUFXLEVBQUUyRCxNQUFNLEtBQUtBO0lBQ3BDO0lBRUEsTUFBTStDLGlCQUFpQixDQUFDOUM7UUFDdEIsT0FBTzVELEVBQUUsQ0FBQyxZQUFZLEVBQUU0RCxRQUFRLEtBQUtBO0lBQ3ZDO0lBRUEsTUFBTStDLGtCQUFrQixDQUFDQztRQUN2QixPQUFPNUcsRUFBRSxDQUFDLFNBQVMsRUFBRTRHLFNBQVMsS0FBS0E7SUFDckM7SUFFQSxJQUFJbEcsU0FBUztRQUNYLHFCQUNFLDhEQUFDbUc7WUFBSUMsT0FBTztnQkFDVkMsV0FBVztnQkFDWEMsWUFBWTtnQkFDWkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsZ0JBQWdCO1lBQ2xCO3NCQUNFLDRFQUFDTjtnQkFBSUMsT0FBTztvQkFBRU0sT0FBTztvQkFBU0MsVUFBVTtnQkFBUzs7b0JBQUc7b0JBQUdySCxFQUFFOzs7Ozs7Ozs7Ozs7SUFHL0Q7SUFFQSxJQUFJWSxPQUFPO1FBQ1QscUJBQ0UsOERBQUNpRztZQUFJQyxPQUFPO2dCQUNWQyxXQUFXO2dCQUNYQyxZQUFZO2dCQUNaQyxTQUFTO2dCQUNUQyxZQUFZO2dCQUNaQyxnQkFBZ0I7WUFDbEI7c0JBQ0UsNEVBQUNOO2dCQUFJQyxPQUFPO29CQUFFTSxPQUFPO29CQUFTQyxVQUFVO2dCQUFTOztvQkFBRztvQkFBR3JILEVBQUU7b0JBQWdCO29CQUFHWTs7Ozs7Ozs7Ozs7O0lBR2xGO0lBRUEscUJBQ0UsOERBQUNqQiw0REFBU0E7UUFBQzJILHFCQUFxQjtZQUFDO1NBQWE7a0JBQzVDLDRFQUFDMUgsbUVBQWVBO1lBQUMySCxPQUFPdkgsRUFBRTtZQUFld0gsVUFBVXhILEVBQUU7WUFBZ0J5SCxNQUFLOzs4QkFFeEUsOERBQUNaO29CQUFJQyxPQUFPO3dCQUNWRSxZQUFZO3dCQUNaVSxTQUFTO3dCQUNUQyxjQUFjO3dCQUNkQyxjQUFjO3dCQUNkUixPQUFPO3dCQUNQUyxXQUFXO3dCQUNYQyxXQUFXO29CQUNiOztzQ0FDRSw4REFBQ0M7NEJBQUdqQixPQUFPO2dDQUFFa0IsUUFBUTtnQ0FBY1gsVUFBVTs0QkFBUzs7Z0NBQUc7Z0NBQUlySCxFQUFFOzs7Ozs7O3NDQUMvRCw4REFBQ2lJOzRCQUFFbkIsT0FBTztnQ0FBRWtCLFFBQVE7Z0NBQWFYLFVBQVU7Z0NBQVFhLFNBQVM7NEJBQUk7c0NBQzdEbEksRUFBRTs7Ozs7O3NDQUVMLDhEQUFDaUk7NEJBQUVuQixPQUFPO2dDQUFFa0IsUUFBUTtnQ0FBS1gsVUFBVTtnQ0FBVWEsU0FBUzs0QkFBSTs7Z0NBQUc7Z0NBQ3hEbEksRUFBRTs7Ozs7Ozs7Ozs7Ozs4QkFLVCw4REFBQzZHO29CQUFJQyxPQUFPO3dCQUFFRyxTQUFTO3dCQUFRa0IsS0FBSzt3QkFBUWhCLGdCQUFnQjt3QkFBVVMsY0FBYzt3QkFBUVEsVUFBVTtvQkFBTzs4QkFDM0csNEVBQUNDO3dCQUNDQyxTQUFTdEQ7d0JBQ1R1RCxVQUFVL0c7d0JBQ1ZzRixPQUFPOzRCQUNMRSxZQUFZeEYsY0FDUiw2Q0FDQTs0QkFDSjRGLE9BQU87NEJBQ1BNLFNBQVM7NEJBQ1RDLGNBQWM7NEJBQ2RhLFFBQVE7NEJBQ1JDLFlBQVk7NEJBQ1pDLFFBQVFsSCxjQUFjLGdCQUFnQjs0QkFDdENzRyxXQUFXOzRCQUNYVCxVQUFVO3dCQUNaO2tDQUVDN0YsY0FBYyxPQUFPeEIsRUFBRSxxQkFBcUIsUUFBUUEsRUFBRTs7Ozs7Ozs7Ozs7OEJBUzNELDhEQUFDNkc7b0JBQUlDLE9BQU87d0JBQ1ZFLFlBQVk7d0JBQ1pXLGNBQWM7d0JBQ2RELFNBQVM7d0JBQ1RFLGNBQWM7d0JBQ2RZLFFBQVE7b0JBQ1Y7O3NDQUNFLDhEQUFDRzs0QkFBRzdCLE9BQU87Z0NBQUVNLE9BQU87Z0NBQVdRLGNBQWM7Z0NBQVFQLFVBQVU7NEJBQVM7O2dDQUFHO2dDQUNyRXJILEVBQUU7Ozs7Ozs7c0NBR1IsOERBQUM2Rzs0QkFBSUMsT0FBTztnQ0FBRUcsU0FBUztnQ0FBUTJCLHFCQUFxQjtnQ0FBd0NULEtBQUs7NEJBQU87OzhDQUV0Ryw4REFBQ3RCOztzREFDQyw4REFBQ2dDOzRDQUFNL0IsT0FBTztnREFBRUcsU0FBUztnREFBU1csY0FBYztnREFBT1IsT0FBTztnREFBV0MsVUFBVTs0Q0FBUzs7Z0RBQ3pGckgsRUFBRTtnREFBc0I7Ozs7Ozs7c0RBRTNCLDhEQUFDOEk7NENBQ0NuRixNQUFLOzRDQUNMb0YsYUFBYS9JLEVBQUU7NENBQ2ZnSixPQUFPbEk7NENBQ1BtSSxVQUFVLENBQUNDLElBQU1uSSxjQUFjbUksRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUM3Q2xDLE9BQU87Z0RBQ0xzQyxPQUFPO2dEQUNQMUIsU0FBUztnREFDVGMsUUFBUTtnREFDUmIsY0FBYztnREFDZE4sVUFBVTtnREFDVmdDLFdBQVc7Z0RBQ1hqQyxPQUFPO2dEQUNQSixZQUFZOzRDQUNkOzs7Ozs7Ozs7Ozs7OENBS0osOERBQUNIOztzREFDQyw4REFBQ2dDOzRDQUFNL0IsT0FBTztnREFBRUcsU0FBUztnREFBU1csY0FBYztnREFBT1IsT0FBTztnREFBV0MsVUFBVTs0Q0FBUzs7Z0RBQ3pGckgsRUFBRTtnREFBc0I7Ozs7Ozs7c0RBRTNCLDhEQUFDOEk7NENBQ0NuRixNQUFLOzRDQUNMb0YsYUFBYS9JLEVBQUU7NENBQ2ZnSixPQUFPaEk7NENBQ1BpSSxVQUFVLENBQUNDLElBQU1qSSxrQkFBa0JpSSxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQ2pEbEMsT0FBTztnREFDTHNDLE9BQU87Z0RBQ1AxQixTQUFTO2dEQUNUYyxRQUFRO2dEQUNSYixjQUFjO2dEQUNkTixVQUFVO2dEQUNWZ0MsV0FBVztnREFDWGpDLE9BQU87Z0RBQ1BKLFlBQVk7NENBQ2Q7Ozs7Ozs7Ozs7Ozs4Q0FLSiw4REFBQ0g7O3NEQUNDLDhEQUFDZ0M7NENBQU0vQixPQUFPO2dEQUFFRyxTQUFTO2dEQUFTVyxjQUFjO2dEQUFPUixPQUFPO2dEQUFXQyxVQUFVOzRDQUFTOztnREFDekZySCxFQUFFO2dEQUFtQjs7Ozs7OztzREFFeEIsOERBQUNzSjs0Q0FDQ04sT0FBTzlIOzRDQUNQK0gsVUFBVSxDQUFDQyxJQUFNL0gsZ0JBQWdCK0gsRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUMvQ2xDLE9BQU87Z0RBQ0xzQyxPQUFPO2dEQUNQMUIsU0FBUztnREFDVGMsUUFBUTtnREFDUmIsY0FBYztnREFDZE4sVUFBVTtnREFDVmdDLFdBQVduSixRQUFRLFFBQVE7Z0RBQzNCa0gsT0FBTztnREFDUEosWUFBWTs0Q0FDZDs7OERBRUEsOERBQUN1QztvREFBT1AsT0FBTTs4REFBT2hKLEVBQUU7Ozs7Ozs4REFDdkIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBUWhKLEVBQUU7Ozs7Ozs4REFDeEIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBVWhKLEVBQUU7Ozs7Ozs4REFDMUIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBV2hKLEVBQUU7Ozs7Ozs4REFDM0IsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBUWhKLEVBQUU7Ozs7Ozs4REFDeEIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBVWhKLEVBQUU7Ozs7Ozs4REFDMUIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBU2hKLEVBQUU7Ozs7Ozs4REFDekIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBU2hKLEVBQUU7Ozs7Ozs4REFDekIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBUWhKLEVBQUU7Ozs7Ozs4REFDeEIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBT2hKLEVBQUU7Ozs7Ozs4REFDdkIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBU2hKLEVBQUU7Ozs7Ozs4REFDekIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBUWhKLEVBQUU7Ozs7Ozs4REFDeEIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBUWhKLEVBQUU7Ozs7Ozs4REFDeEIsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBU2hKLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLN0IsOERBQUM2Rzs7c0RBQ0MsOERBQUNnQzs0Q0FBTS9CLE9BQU87Z0RBQUVHLFNBQVM7Z0RBQVNXLGNBQWM7Z0RBQU9SLE9BQU87Z0RBQVdDLFVBQVU7NENBQVM7O2dEQUN6RnJILEVBQUU7Z0RBQXFCOzs7Ozs7O3NEQUUxQiw4REFBQ3NKOzRDQUNDTixPQUFPNUg7NENBQ1A2SCxVQUFVLENBQUNDLElBQU03SCxrQkFBa0I2SCxFQUFFQyxNQUFNLENBQUNILEtBQUs7NENBQ2pEbEMsT0FBTztnREFDTHNDLE9BQU87Z0RBQ1AxQixTQUFTO2dEQUNUYyxRQUFRO2dEQUNSYixjQUFjO2dEQUNkTixVQUFVO2dEQUNWZ0MsV0FBV25KLFFBQVEsUUFBUTtnREFDM0JrSCxPQUFPO2dEQUNQSixZQUFZOzRDQUNkOzs4REFFQSw4REFBQ3VDO29EQUFPUCxPQUFNOzhEQUFPaEosRUFBRTs7Ozs7OzhEQUN2Qiw4REFBQ3VKO29EQUFPUCxPQUFNOzhEQUFTaEosRUFBRTs7Ozs7OzhEQUN6Qiw4REFBQ3VKO29EQUFPUCxPQUFNOzhEQUF1QmhKLEVBQUU7Ozs7Ozs4REFDdkMsOERBQUN1SjtvREFBT1AsT0FBTTs4REFBc0JoSixFQUFFOzs7Ozs7OERBQ3RDLDhEQUFDdUo7b0RBQU9QLE9BQU07OERBQVdoSixFQUFFOzs7Ozs7OERBQzNCLDhEQUFDdUo7b0RBQU9QLE9BQU07OERBQVFoSixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzVCLDhEQUFDNkc7O3NEQUNDLDhEQUFDZ0M7NENBQU0vQixPQUFPO2dEQUFFRyxTQUFTO2dEQUFTVyxjQUFjO2dEQUFPUixPQUFPO2dEQUFXQyxVQUFVOzRDQUFTOztnREFDekZySCxFQUFFO2dEQUFnQjs7Ozs7OztzREFFckIsOERBQUNzSjs0Q0FDQ04sT0FBTzFIOzRDQUNQMkgsVUFBVSxDQUFDQyxJQUFNM0gsVUFBVTJILEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSzs0Q0FDekNsQyxPQUFPO2dEQUNMc0MsT0FBTztnREFDUDFCLFNBQVM7Z0RBQ1RjLFFBQVE7Z0RBQ1JiLGNBQWM7Z0RBQ2ROLFVBQVU7Z0RBQ1ZnQyxXQUFXbkosUUFBUSxRQUFRO2dEQUMzQmtILE9BQU87Z0RBQ1BKLFlBQVk7NENBQ2Q7OzhEQUVBLDhEQUFDdUM7b0RBQU9QLE9BQU07OERBQVVoSixFQUFFOzs7Ozs7OERBQzFCLDhEQUFDdUo7b0RBQU9QLE9BQU07OERBQVVoSixFQUFFOzs7Ozs7OERBQzFCLDhEQUFDdUo7b0RBQU9QLE9BQU07OERBQVFoSixFQUFFOzs7Ozs7OERBQ3hCLDhEQUFDdUo7b0RBQU9QLE9BQU07OERBQVFoSixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTlCLDhEQUFDNkc7NEJBQUlDLE9BQU87Z0NBQ1YwQyxXQUFXO2dDQUNYOUIsU0FBUztnQ0FDVFYsWUFBWTtnQ0FDWlcsY0FBYztnQ0FDZEUsV0FBVztnQ0FDWFQsT0FBTztnQ0FDUG9CLFFBQVE7NEJBQ1Y7O2dDQUFHO2dDQUNHeEksRUFBRSxxQkFBcUI7b0NBQUU4QyxVQUFVdEMsY0FBYytDLE1BQU07b0NBQUVrRyxPQUFPbkosV0FBV2lELE1BQU07Z0NBQUM7Ozs7Ozs7Ozs7Ozs7Z0JBS3pGL0MsY0FBYytDLE1BQU0sS0FBSyxrQkFDeEIsOERBQUNzRDtvQkFBSUMsT0FBTzt3QkFDVkUsWUFBWTt3QkFDWlcsY0FBYzt3QkFDZEQsU0FBUzt3QkFDVEcsV0FBVzt3QkFDWFcsUUFBUTtvQkFDVjs7c0NBQ0UsOERBQUNHOzRCQUFHN0IsT0FBTztnQ0FBRU0sT0FBTztnQ0FBV0MsVUFBVTs0QkFBUzs7Z0NBQUc7Z0NBQy9DckgsRUFBRTs7Ozs7OztzQ0FFUiw4REFBQ2lJOzRCQUFFbkIsT0FBTztnQ0FBRU0sT0FBTztnQ0FBV29DLFdBQVc7NEJBQU87c0NBQzdDeEosRUFBRTs7Ozs7Ozs7Ozs7eUNBSVAsOERBQUM2RztvQkFBSUMsT0FBTzt3QkFBRUcsU0FBUzt3QkFBUWtCLEtBQUs7b0JBQU87OEJBQ3hDM0gsY0FBY2tKLEdBQUcsQ0FBQyxDQUFDMUcscUJBQ2xCLDhEQUFDNkQ7NEJBQWtCQyxPQUFPO2dDQUN4QkUsWUFBWTtnQ0FDWlcsY0FBYztnQ0FDZEQsU0FBUztnQ0FDVGMsUUFBUTs0QkFDVjtzQ0FDRSw0RUFBQzNCO2dDQUFJQyxPQUFPO29DQUFFRyxTQUFTO29DQUFRMkIscUJBQXFCO29DQUFZVCxLQUFLO29DQUFRakIsWUFBWTtnQ0FBUTs7a0RBQy9GLDhEQUFDTDs7MERBQ0MsOERBQUNrQjtnREFBR2pCLE9BQU87b0RBQUVNLE9BQU87b0RBQVdRLGNBQWM7b0RBQVFQLFVBQVU7Z0RBQVM7MERBQ3JFckUsS0FBS0MsSUFBSTs7Ozs7OzBEQUdaLDhEQUFDNEQ7Z0RBQUlDLE9BQU87b0RBQUVHLFNBQVM7b0RBQVEyQixxQkFBcUI7b0RBQXdDVCxLQUFLO29EQUFRUCxjQUFjO29EQUFRUixPQUFPO2dEQUFVOztrRUFDOUksOERBQUNQOzswRUFDQyw4REFBQzhDOztvRUFBUTNKLEVBQUU7b0VBQWU7Ozs7Ozs7NERBQVU7NERBQUV5RyxhQUFhekQsS0FBS1csSUFBSTs7Ozs7OztrRUFFOUQsOERBQUNrRDs7MEVBQ0MsOERBQUM4Qzs7b0VBQVEzSixFQUFFO29FQUFpQjs7Ozs7Ozs0REFBVTs0REFBRTJHLGdCQUFnQjNELEtBQUs0RCxPQUFPOzs7Ozs7O2tFQUV0RSw4REFBQ0M7OzBFQUNDLDhEQUFDOEM7O29FQUFRM0osRUFBRTtvRUFBaUI7Ozs7Ozs7NERBQVU7NERBQUUwRyxlQUFlMUQsS0FBS1ksTUFBTTs7Ozs7OztrRUFFcEUsOERBQUNpRDs7MEVBQ0MsOERBQUM4Qzs7b0VBQVEzSixFQUFFO29FQUFzQjs7Ozs7Ozs0REFBVTs0REFBRWdELEtBQUtNLFFBQVEsQ0FBQ0MsTUFBTTs7Ozs7Ozs7Ozs7Ozs0Q0FJcEVQLEtBQUtJLFdBQVcsa0JBQ2YsOERBQUM2RTtnREFBRW5CLE9BQU87b0RBQUVNLE9BQU87b0RBQVdRLGNBQWM7Z0RBQU87O2tFQUNqRCw4REFBQytCOzs0REFBUTNKLEVBQUU7NERBQXFCOzs7Ozs7O29EQUFVO29EQUFFZ0QsS0FBS0ksV0FBVzs7Ozs7Ozs0Q0FJL0RKLEtBQUtNLFFBQVEsQ0FBQ0MsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQ3NEO2dEQUFJQyxPQUFPO29EQUFFMEMsV0FBVztvREFBUXBDLE9BQU87Z0RBQVU7O2tFQUNoRCw4REFBQ3VDOzs0REFBUTNKLEVBQUU7NERBQWtCOzs7Ozs7O2tFQUM3Qiw4REFBQzZHO3dEQUFJQyxPQUFPOzREQUFFRyxTQUFTOzREQUFRa0IsS0FBSzs0REFBT3FCLFdBQVc7d0RBQU07a0VBQ3pEeEcsS0FBS00sUUFBUSxDQUFDb0csR0FBRyxDQUFDLENBQUNqRyxTQUFTbUcsc0JBQzNCLDhEQUFDL0M7Z0VBQXdDQyxPQUFPO29FQUM5Q0UsWUFBWTtvRUFDWlUsU0FBUztvRUFDVEMsY0FBYztvRUFDZE4sVUFBVTtvRUFDVkQsT0FBTztvRUFDUG9CLFFBQVE7Z0VBQ1Y7O2tGQUNFLDhEQUFDbUI7OzRFQUFPOzRFQUFFbEcsUUFBUW9HLGFBQWE7Ozs7Ozs7b0VBQVU7b0VBQ3hDcEcsUUFBUUMsSUFBSSxJQUFJRCxRQUFRQyxJQUFJLENBQUNvRyxJQUFJLE9BQU8sS0FBSyxDQUFDLENBQUMsRUFBRXJHLFFBQVFDLElBQUksQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLEVBQUUsRUFBRTFELEVBQUUsZ0JBQWdCLElBQUksQ0FBQztvRUFDakd5RCxRQUFRc0csTUFBTTtvRUFBQztvRUFBSXRHLFFBQVF1RyxPQUFPO29FQUFDO29FQUFHdkcsUUFBUXdHLFFBQVE7b0VBQUM7OytEQVZoRCxHQUFHakgsS0FBS0ssRUFBRSxDQUFDLFNBQVMsRUFBRXVHLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBbUJqRCw4REFBQy9DO3dDQUFJQyxPQUFPOzRDQUFFRyxTQUFTOzRDQUFRaUQsZUFBZTs0Q0FBVS9CLEtBQUs7d0NBQU87OzBEQUNsRSw4REFBQ0U7Z0RBQ0NDLFNBQVM7b0RBQ1AscUNBQXFDO29EQUNyQ3hHLE9BQU9xSSxRQUFRLENBQUNwRSxJQUFJLEdBQUcsQ0FBQyxlQUFlLEVBQUUvQyxLQUFLSyxFQUFFLEVBQUU7Z0RBQ3BEO2dEQUNBeUQsT0FBTztvREFDTEUsWUFBWTtvREFDWkksT0FBTztvREFDUG9CLFFBQVE7b0RBQ1JiLGNBQWM7b0RBQ2RELFNBQVM7b0RBQ1RnQixRQUFRO29EQUNSckIsVUFBVTtvREFDVk8sY0FBYztnREFDaEI7O29EQUNEO29EQUNLNUgsRUFBRTs7Ozs7OzswREFHUiw4REFBQ3FJO2dEQUNDQyxTQUFTLElBQU1sRSxnQkFBZ0JwQixLQUFLSyxFQUFFO2dEQUN0Q3lELE9BQU87b0RBQ0xFLFlBQVk7b0RBQ1pJLE9BQU87b0RBQ1BvQixRQUFRO29EQUNSYixjQUFjO29EQUNkRCxTQUFTO29EQUNUZ0IsUUFBUTtvREFDUnJCLFVBQVU7Z0RBQ1o7O29EQUNEO29EQUNNckgsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQkF6RkxnRCxLQUFLSyxFQUFFOzs7Ozs7Ozs7O2dCQW1HdEIzQixpQ0FDQyw4REFBQzJHO29CQUNDQyxTQUFTbkc7b0JBQ1QyRSxPQUFPO3dCQUNMc0QsVUFBVTt3QkFDVkMsUUFBUTt3QkFDUkMsT0FBTzt3QkFDUGxCLE9BQU87d0JBQ1BtQixRQUFRO3dCQUNSNUMsY0FBYzt3QkFDZFgsWUFBWTt3QkFDWkksT0FBTzt3QkFDUG9CLFFBQVE7d0JBQ1JFLFFBQVE7d0JBQ1JyQixVQUFVO3dCQUNWUyxXQUFXO3dCQUNYMEMsUUFBUTt3QkFDUkMsWUFBWTt3QkFDWnhELFNBQVM7d0JBQ1RDLFlBQVk7d0JBQ1pDLGdCQUFnQjtvQkFDbEI7b0JBQ0F1RCxjQUFjLENBQUN4Qjt3QkFDYkEsRUFBRXlCLGFBQWEsQ0FBQzdELEtBQUssQ0FBQzhELFNBQVMsR0FBRzt3QkFDbEMxQixFQUFFeUIsYUFBYSxDQUFDN0QsS0FBSyxDQUFDZ0IsU0FBUyxHQUFHO29CQUNwQztvQkFDQStDLGNBQWMsQ0FBQzNCO3dCQUNiQSxFQUFFeUIsYUFBYSxDQUFDN0QsS0FBSyxDQUFDOEQsU0FBUyxHQUFHO3dCQUNsQzFCLEVBQUV5QixhQUFhLENBQUM3RCxLQUFLLENBQUNnQixTQUFTLEdBQUc7b0JBQ3BDO29CQUNBUCxPQUFPdkgsRUFBRTs4QkFDVjs7Ozs7OzhCQUlILDhEQUFDSzs7Ozs7Ozs7Ozs7Ozs7OztBQUlUIiwic291cmNlcyI6WyJEOlxccHJvamVjdCBzcG9ydFxcbWVkaWEtZGFzaGJvYXJkLWNsZWFuXFxtZWRpYS1kYXNoYm9hcmRcXHNyY1xcYXBwXFxtZWRpYS1saXN0XFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBBdXRoR3VhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvQXV0aEd1YXJkJztcbmltcG9ydCBEYXNoYm9hcmRMYXlvdXQgZnJvbSAnQC9jb21wb25lbnRzL0Rhc2hib2FyZExheW91dCc7XG5pbXBvcnQgeyB1c2VBcHBUcmFuc2xhdGlvbiB9IGZyb20gJ0AvaG9va3MvdXNlQXBwVHJhbnNsYXRpb24nO1xuaW1wb3J0IHsgdXNlVHJhbnNsYXRlZFRvYXN0IH0gZnJvbSAnQC9ob29rcy91c2VUcmFuc2xhdGVkVG9hc3QnO1xuXG5pbnRlcmZhY2UgU2VnbWVudCB7XG4gIGlkOiBzdHJpbmc7XG4gIHNlZ21lbnROdW1iZXI6IG51bWJlcjtcbiAgdGltZUluOiBzdHJpbmc7XG4gIHRpbWVPdXQ6IHN0cmluZztcbiAgZHVyYXRpb246IHN0cmluZztcbiAgY29kZTogc3RyaW5nIHwgbnVsbDtcbn1cblxuaW50ZXJmYWNlIE1lZGlhSXRlbSB7XG4gIGlkOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgdHlwZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nIHwgbnVsbDtcbiAgY2hhbm5lbDogc3RyaW5nO1xuICBzb3VyY2U6IHN0cmluZyB8IG51bGw7XG4gIHN0YXR1czogc3RyaW5nO1xuICBzdGFydERhdGU6IHN0cmluZztcbiAgZW5kRGF0ZTogc3RyaW5nIHwgbnVsbDtcbiAgbm90ZXM6IHN0cmluZyB8IG51bGw7XG4gIGVwaXNvZGVOdW1iZXI6IG51bWJlciB8IG51bGw7XG4gIHNlYXNvbk51bWJlcjogbnVtYmVyIHwgbnVsbDtcbiAgcGFydE51bWJlcjogbnVtYmVyIHwgbnVsbDtcbiAgc2VnbWVudHM6IFNlZ21lbnRbXTtcbiAgY3JlYXRlZEF0OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1lZGlhTGlzdFBhZ2UoKSB7XG4gIGNvbnN0IHsgdCwgdE1lZGlhVHlwZSwgaXNSVEwgfSA9IHVzZUFwcFRyYW5zbGF0aW9uKCk7XG4gIGNvbnN0IHsgc2hvd1N1Y2Nlc3NUb2FzdCwgc2hvd0Vycm9yVG9hc3QsIFRvYXN0Q29udGFpbmVyIH0gPSB1c2VUcmFuc2xhdGVkVG9hc3QoKTtcblxuICBjb25zdCBbbWVkaWFJdGVtcywgc2V0TWVkaWFJdGVtc10gPSB1c2VTdGF0ZTxNZWRpYUl0ZW1bXT4oW10pO1xuICBjb25zdCBbZmlsdGVyZWRJdGVtcywgc2V0RmlsdGVyZWRJdGVtc10gPSB1c2VTdGF0ZTxNZWRpYUl0ZW1bXT4oW10pO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbY29kZVNlYXJjaFRlcm0sIHNldENvZGVTZWFyY2hUZXJtXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3NlbGVjdGVkVHlwZSwgc2V0U2VsZWN0ZWRUeXBlXSA9IHVzZVN0YXRlKCdBTEwnKTtcbiAgY29uc3QgW3NlbGVjdGVkU3RhdHVzLCBzZXRTZWxlY3RlZFN0YXR1c10gPSB1c2VTdGF0ZSgnQUxMJyk7XG4gIGNvbnN0IFtzb3J0QnksIHNldFNvcnRCeV0gPSB1c2VTdGF0ZSgnbmV3ZXN0Jyk7XG4gIGNvbnN0IFtpc0V4cG9ydGluZywgc2V0SXNFeHBvcnRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIGNvbnN0IFtzaG93U2Nyb2xsVG9Ub3AsIHNldFNob3dTY3JvbGxUb1RvcF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaE1lZGlhSXRlbXMoKTtcbiAgfSwgW10pO1xuXG4gIC8vINmF2LHYp9mC2KjYqSDYp9mE2KrZhdix2YrYsSDZhNil2LjZh9in2LEv2KXYrtmB2KfYoSDYstixINin2YTYudmI2K/YqSDZhNij2LnZhNmJXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlU2Nyb2xsID0gKCkgPT4ge1xuICAgICAgc2V0U2hvd1Njcm9sbFRvVG9wKHdpbmRvdy5zY3JvbGxZID4gMzAwKTtcbiAgICB9O1xuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ3Njcm9sbCcsIGhhbmRsZVNjcm9sbCk7XG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpO1xuICB9LCBbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmaWx0ZXJBbmRTb3J0SXRlbXMoKTtcbiAgfSwgW21lZGlhSXRlbXMsIHNlYXJjaFRlcm0sIGNvZGVTZWFyY2hUZXJtLCBzZWxlY3RlZFR5cGUsIHNlbGVjdGVkU3RhdHVzLCBzb3J0QnldKTtcblxuICAvLyDYr9in2YTYqSDYp9mE2LnZiNiv2Kkg2YTYo9i52YTZiSDYp9mE2LXZgdit2KlcbiAgY29uc3Qgc2Nyb2xsVG9Ub3AgPSAoKSA9PiB7XG4gICAgd2luZG93LnNjcm9sbFRvKHtcbiAgICAgIHRvcDogMCxcbiAgICAgIGJlaGF2aW9yOiAnc21vb3RoJ1xuICAgIH0pO1xuICB9O1xuXG4gIGNvbnN0IGZldGNoTWVkaWFJdGVtcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9tZWRpYScpO1xuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0TWVkaWFJdGVtcyhyZXN1bHQuZGF0YSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRFcnJvcihyZXN1bHQuZXJyb3IpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBtZWRpYSBpdGVtczonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcih0KCdtZXNzYWdlcy5uZXR3b3JrRXJyb3InKSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmaWx0ZXJBbmRTb3J0SXRlbXMgPSAoKSA9PiB7XG4gICAgbGV0IGZpbHRlcmVkID0gWy4uLm1lZGlhSXRlbXNdO1xuXG4gICAgLy8g2KfZhNio2K3YqyDYqNin2YTYp9iz2YVcbiAgICBpZiAoc2VhcmNoVGVybSkge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoaXRlbSA9PlxuICAgICAgICBpdGVtLm5hbWUudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpIHx8XG4gICAgICAgIChpdGVtLmRlc2NyaXB0aW9uICYmIGl0ZW0uZGVzY3JpcHRpb24udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpKVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyDYp9mE2KjYrdirINio2KfZhNmD2YjYryAo2YHZiiDYp9mE2LPZitis2YXYp9mG2KopXG4gICAgaWYgKGNvZGVTZWFyY2hUZXJtKSB7XG4gICAgICBmaWx0ZXJlZCA9IGZpbHRlcmVkLmZpbHRlcihpdGVtID0+IHtcbiAgICAgICAgLy8g2KfZhNio2K3YqyDZgdmKINmF2LnYsdmBINin2YTZhdin2K/YqVxuICAgICAgICBpZiAoaXRlbS5pZC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGNvZGVTZWFyY2hUZXJtLnRvTG93ZXJDYXNlKCkpKSB7XG4gICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDYp9mE2KjYrdirINmB2Yog2KPZg9mI2KfYryDYp9mE2LPZitis2YXYp9mG2KpcbiAgICAgICAgaWYgKGl0ZW0uc2VnbWVudHMgJiYgaXRlbS5zZWdtZW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgcmV0dXJuIGl0ZW0uc2VnbWVudHMuc29tZShzZWdtZW50ID0+XG4gICAgICAgICAgICBzZWdtZW50LmNvZGUgJiYgc2VnbWVudC5jb2RlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoY29kZVNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSlcbiAgICAgICAgICApO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfSk7XG4gICAgfVxuXG4gICAgLy8g2YHZhNiq2LHYqSDYqNin2YTZhtmI2LlcbiAgICBpZiAoc2VsZWN0ZWRUeXBlICE9PSAnQUxMJykge1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIoaXRlbSA9PiBpdGVtLnR5cGUgPT09IHNlbGVjdGVkVHlwZSk7XG4gICAgfVxuXG4gICAgLy8g2YHZhNiq2LHYqSDYqNin2YTYrdin2YTYqVxuICAgIGlmIChzZWxlY3RlZFN0YXR1cyAhPT0gJ0FMTCcpIHtcbiAgICAgIGZpbHRlcmVkID0gZmlsdGVyZWQuZmlsdGVyKGl0ZW0gPT4gaXRlbS5zdGF0dXMgPT09IHNlbGVjdGVkU3RhdHVzKTtcbiAgICB9XG5cbiAgICAvLyDYp9mE2KrYsdiq2YrYqFxuICAgIHN3aXRjaCAoc29ydEJ5KSB7XG4gICAgICBjYXNlICduZXdlc3QnOlxuICAgICAgICBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiBuZXcgRGF0ZShiLmNyZWF0ZWRBdCkuZ2V0VGltZSgpIC0gbmV3IERhdGUoYS5jcmVhdGVkQXQpLmdldFRpbWUoKSk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnb2xkZXN0JzpcbiAgICAgICAgZmlsdGVyZWQuc29ydCgoYSwgYikgPT4gbmV3IERhdGUoYS5jcmVhdGVkQXQpLmdldFRpbWUoKSAtIG5ldyBEYXRlKGIuY3JlYXRlZEF0KS5nZXRUaW1lKCkpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ25hbWUnOlxuICAgICAgICBmaWx0ZXJlZC5zb3J0KChhLCBiKSA9PiBhLm5hbWUubG9jYWxlQ29tcGFyZShiLm5hbWUsICdhcicpKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICd0eXBlJzpcbiAgICAgICAgZmlsdGVyZWQuc29ydCgoYSwgYikgPT4gYS50eXBlLmxvY2FsZUNvbXBhcmUoYi50eXBlKSk7XG4gICAgICAgIGJyZWFrO1xuICAgIH1cblxuICAgIHNldEZpbHRlcmVkSXRlbXMoZmlsdGVyZWQpO1xuICB9O1xuXG4gIGNvbnN0IGRlbGV0ZU1lZGlhSXRlbSA9IGFzeW5jIChpZDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFjb25maXJtKHQoJ21lc3NhZ2VzLmNvbmZpcm1EZWxldGUnKSkpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICAvLyDYqtit2YjZitmEINin2YTYqtmI2YPZhiDYpdmE2Ykg2KfZhNi12YrYutipINin2YTZhdiq2YjZgti52KlcbiAgICAgIGNvbnN0IHVzZXIgPSBKU09OLnBhcnNlKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJykgfHwgJ3t9Jyk7XG4gICAgICBjb25zdCB0b2tlbldpdGhSb2xlID0gYHRva2VuXyR7dXNlci5pZCB8fCAndW5rbm93bid9XyR7dXNlci5yb2xlIHx8ICd1bmtub3duJ31gO1xuICAgICAgXG4gICAgICBjb25zb2xlLmxvZygnU2VuZGluZyBkZWxldGUgcmVxdWVzdCB3aXRoIHRva2VuOicsIHRva2VuV2l0aFJvbGUpO1xuICAgICAgXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAvYXBpL21lZGlhP2lkPSR7aWR9YCwge1xuICAgICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7dG9rZW5XaXRoUm9sZX1gXG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICBzZXRNZWRpYUl0ZW1zKG1lZGlhSXRlbXMuZmlsdGVyKGl0ZW0gPT4gaXRlbS5pZCAhPT0gaWQpKTtcbiAgICAgICAgc2hvd1N1Y2Nlc3NUb2FzdCgnbWVkaWFEZWxldGVkJyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzaG93RXJyb3JUb2FzdCgndW5rbm93bkVycm9yJyk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIG1lZGlhIGl0ZW06JywgZXJyb3IpO1xuICAgICAgc2hvd0Vycm9yVG9hc3QoJ3Vua25vd25FcnJvcicpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBleHBvcnRUb0V4Y2VsID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzRXhwb3J0aW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+agCDYqNiv2KEg2KrYtdiv2YrYsSDZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2KouLi4nKTtcblxuICAgICAgLy8g2KXYsdiz2KfZhCDYp9mE2YHZhNin2KrYsSDYp9mE2K3Yp9mE2YrYqSDZhdi5INi32YTYqCDYp9mE2KrYtdiv2YrYsVxuICAgICAgY29uc3QgcGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgICAgaWYgKHNlYXJjaFRlcm0pIHBhcmFtcy5hcHBlbmQoJ3NlYXJjaCcsIHNlYXJjaFRlcm0pO1xuICAgICAgaWYgKGNvZGVTZWFyY2hUZXJtKSBwYXJhbXMuYXBwZW5kKCdjb2RlU2VhcmNoJywgY29kZVNlYXJjaFRlcm0pO1xuICAgICAgaWYgKHNlbGVjdGVkVHlwZSAhPT0gJ0FMTCcpIHBhcmFtcy5hcHBlbmQoJ3R5cGUnLCBzZWxlY3RlZFR5cGUpO1xuICAgICAgaWYgKHNlbGVjdGVkU3RhdHVzICE9PSAnQUxMJykgcGFyYW1zLmFwcGVuZCgnc3RhdHVzJywgc2VsZWN0ZWRTdGF0dXMpO1xuXG4gICAgICBjb25zdCBhcGlVcmwgPSBgL2FwaS9leHBvcnQtdW5pZmllZCR7cGFyYW1zLnRvU3RyaW5nKCkgPyAnPycgKyBwYXJhbXMudG9TdHJpbmcoKSA6ICcnfWA7XG4gICAgICBjb25zb2xlLmxvZygn8J+TiiDYqti12K/ZitixINmF2Lkg2KfZhNmB2YTYp9iq2LE6JywgYXBpVXJsKTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChhcGlVcmwpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcih0KCdtZXNzYWdlcy5leHBvcnRFcnJvcicpKTtcbiAgICAgIH1cblxuICAgICAgLy8g2KfZhNit2LXZiNmEINi52YTZiSDYp9mE2YXZhNmBINmD2YAgYmxvYlxuICAgICAgY29uc3QgYmxvYiA9IGF3YWl0IHJlc3BvbnNlLmJsb2IoKTtcblxuICAgICAgLy8g2KXZhti02KfYoSDYsdin2KjYtyDYp9mE2KrYrdmF2YrZhFxuICAgICAgY29uc3QgZG93bmxvYWRVcmwgPSB3aW5kb3cuVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTtcbiAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7XG4gICAgICBsaW5rLmhyZWYgPSBkb3dubG9hZFVybDtcblxuICAgICAgLy8g2KrYrdiv2YrYryDYp9iz2YUg2KfZhNmF2YTZgVxuICAgICAgY29uc3QgZmlsZU5hbWUgPSBgTWVkaWFfRGF0YWJhc2VfJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXX0ueGxzeGA7XG4gICAgICBsaW5rLmRvd25sb2FkID0gZmlsZU5hbWU7XG5cbiAgICAgIC8vINiq2K3ZhdmK2YQg2KfZhNmF2YTZgVxuICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcbiAgICAgIGxpbmsuY2xpY2soKTtcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7XG5cbiAgICAgIC8vINiq2YbYuNmK2YEg2KfZhNiw2KfZg9ix2KlcbiAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKGRvd25sb2FkVXJsKTtcblxuICAgICAgY29uc29sZS5sb2coJ+KchSDYqtmFINiq2LXYr9mK2LEg2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqINio2YbYrNin2K0nKTtcbiAgICAgIHNob3dTdWNjZXNzVG9hc3QoJ2V4cG9ydFN1Y2Nlc3MnKTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwg2K7Yt9ijINmB2Yog2KfZhNiq2LXYr9mK2LE6JywgZXJyb3IpO1xuICAgICAgc2hvd0Vycm9yVG9hc3QoJ2V4cG9ydEZhaWxlZCcpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0V4cG9ydGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG5cblxuICBjb25zdCBnZXRUeXBlTGFiZWwgPSAodHlwZTogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIHQoYG1lZGlhVHlwZXMuJHt0eXBlfWApIHx8IHR5cGU7XG4gIH07XG5cbiAgY29uc3QgZ2V0U3RhdHVzTGFiZWwgPSAoc3RhdHVzOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gdChgbWVkaWFTdGF0dXMuJHtzdGF0dXN9YCkgfHwgc3RhdHVzO1xuICB9O1xuXG4gIGNvbnN0IGdldENoYW5uZWxMYWJlbCA9IChjaGFubmVsOiBzdHJpbmcpID0+IHtcbiAgICByZXR1cm4gdChgY2hhbm5lbHMuJHtjaGFubmVsfWApIHx8IGNoYW5uZWw7XG4gIH07XG5cbiAgaWYgKGxvYWRpbmcpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBzdHlsZT17eyBcbiAgICAgICAgbWluSGVpZ2h0OiAnMTAwdmgnLCBcbiAgICAgICAgYmFja2dyb3VuZDogJyMxYTFkMjknLFxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcidcbiAgICAgIH19PlxuICAgICAgICA8ZGl2IHN0eWxlPXt7IGNvbG9yOiAnd2hpdGUnLCBmb250U2l6ZTogJzEuNXJlbScgfX0+4o+zIHt0KCdjb21tb24ubG9hZGluZycpfTwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmIChlcnJvcikge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IHN0eWxlPXt7IFxuICAgICAgICBtaW5IZWlnaHQ6ICcxMDB2aCcsIFxuICAgICAgICBiYWNrZ3JvdW5kOiAnIzFhMWQyOScsXG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJ1xuICAgICAgfX0+XG4gICAgICAgIDxkaXYgc3R5bGU9e3sgY29sb3I6ICd3aGl0ZScsIGZvbnRTaXplOiAnMS41cmVtJyB9fT7inYwge3QoJ2NvbW1vbi5lcnJvcicpfToge2Vycm9yfTwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEF1dGhHdWFyZCByZXF1aXJlZFBlcm1pc3Npb25zPXtbJ01FRElBX1JFQUQnXX0+XG4gICAgICA8RGFzaGJvYXJkTGF5b3V0IHRpdGxlPXt0KCdtZWRpYS5saXN0Jyl9IHN1YnRpdGxlPXt0KCdtZWRpYS50aXRsZScpfSBpY29uPVwi8J+OrFwiPlxuICAgICAgICB7Lyog2LHYs9in2YTYqSDYqtmI2LbZitit2YrYqSAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17e1xuICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpJyxcbiAgICAgICAgICBwYWRkaW5nOiAnMjBweCcsXG4gICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTJweCcsXG4gICAgICAgICAgbWFyZ2luQm90dG9tOiAnMjVweCcsXG4gICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJyxcbiAgICAgICAgICBib3hTaGFkb3c6ICcwIDZweCAyMHB4IHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4zKSdcbiAgICAgICAgfX0+XG4gICAgICAgICAgPGgzIHN0eWxlPXt7IG1hcmdpbjogJzAgMCAxMHB4IDAnLCBmb250U2l6ZTogJzEuM3JlbScgfX0+8J+TiiB7dCgnbWVkaWEubGlzdCcpfTwvaDM+XG4gICAgICAgICAgPHAgc3R5bGU9e3sgbWFyZ2luOiAnMCAwIDhweCAwJywgZm9udFNpemU6ICcxcmVtJywgb3BhY2l0eTogMC45IH19PlxuICAgICAgICAgICAge3QoJ21lZGlhLm1lZGlhT3ZlcnZpZXcnKX1cbiAgICAgICAgICA8L3A+XG4gICAgICAgICAgPHAgc3R5bGU9e3sgbWFyZ2luOiAnMCcsIGZvbnRTaXplOiAnMC45cmVtJywgb3BhY2l0eTogMC44IH19PlxuICAgICAgICAgICAg4pyoIHt0KCdtZWRpYS5zZWFyY2hGaWx0ZXJFeHBvcnQnKX1cbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDYo9iy2LHYp9ixINin2YTYpdis2LHYp9ih2KfYqiAqL31cbiAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogJzE1cHgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsIG1hcmdpbkJvdHRvbTogJzI1cHgnLCBmbGV4V3JhcDogJ3dyYXAnIH19PlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2V4cG9ydFRvRXhjZWx9XG4gICAgICAgICAgICBkaXNhYmxlZD17aXNFeHBvcnRpbmd9XG4gICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiBpc0V4cG9ydGluZ1xuICAgICAgICAgICAgICAgID8gJ2xpbmVhci1ncmFkaWVudCg0NWRlZywgIzZjNzU3ZCwgIzVhNjI2OCknXG4gICAgICAgICAgICAgICAgOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMTdhMmI4LCAjMTM4NDk2KScsXG4gICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICBwYWRkaW5nOiAnMTJweCAyNXB4JyxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMjVweCcsXG4gICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICBmb250V2VpZ2h0OiAnYm9sZCcsXG4gICAgICAgICAgICAgIGN1cnNvcjogaXNFeHBvcnRpbmcgPyAnbm90LWFsbG93ZWQnIDogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDRweCAxNXB4IHJnYmEoMjMsMTYyLDE4NCwwLjMpJyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJ1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNFeHBvcnRpbmcgPyAn4o+zICcgKyB0KCdtZWRpYS5leHBvcnRpbmcnKSA6ICfwn5OKICcgKyB0KCdtZWRpYS5leHBvcnRFeGNlbCcpfVxuICAgICAgICAgIDwvYnV0dG9uPlxuXG5cblxuXG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDYp9mE2KjYrdirINmI2KfZhNmB2YTYqtix2KkgKi99XG4gICAgICAgIDxkaXYgc3R5bGU9e3tcbiAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzRhNTU2OCcsXG4gICAgICAgICAgYm9yZGVyUmFkaXVzOiAnMTVweCcsXG4gICAgICAgICAgcGFkZGluZzogJzI1cHgnLFxuICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzI1cHgnLFxuICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjNmI3MjgwJ1xuICAgICAgICB9fT5cbiAgICAgICAgICA8aDIgc3R5bGU9e3sgY29sb3I6ICcjZjNmNGY2JywgbWFyZ2luQm90dG9tOiAnMjBweCcsIGZvbnRTaXplOiAnMS4zcmVtJyB9fT5cbiAgICAgICAgICAgIPCflI0ge3QoJ21lZGlhLnNlYXJjaEFuZEZpbHRlcicpfVxuICAgICAgICAgIDwvaDI+XG5cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ3JpZFRlbXBsYXRlQ29sdW1uczogJ3JlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKScsIGdhcDogJzE1cHgnIH19PlxuICAgICAgICAgICAgey8qINin2YTYqNit2Ksg2KjYp9mE2KfYs9mFICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGRpc3BsYXk6ICdibG9jaycsIG1hcmdpbkJvdHRvbTogJzVweCcsIGNvbG9yOiAnI2YzZjRmNicsIGZvbnRTaXplOiAnMC45cmVtJyB9fT5cbiAgICAgICAgICAgICAgICB7dCgnbWVkaWEuc2VhcmNoQnlOYW1lJyl9OlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3QoJ21lZGlhLnNlYXJjaFBsYWNlaG9sZGVyJyl9XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hUZXJtKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6ICcxMDAlJyxcbiAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcxMHB4JyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjNmI3MjgwJyxcbiAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzFyZW0nLFxuICAgICAgICAgICAgICAgICAgZGlyZWN0aW9uOiAncnRsJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyMxZjI5MzcnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog2KfZhNio2K3YqyDYqNin2YTZg9mI2K8gKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgc3R5bGU9e3sgZGlzcGxheTogJ2Jsb2NrJywgbWFyZ2luQm90dG9tOiAnNXB4JywgY29sb3I6ICcjZjNmNGY2JywgZm9udFNpemU6ICcwLjlyZW0nIH19PlxuICAgICAgICAgICAgICAgIHt0KCdtZWRpYS5zZWFyY2hCeUNvZGUnKX06XG4gICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dCgnbWVkaWEuY29kZVBsYWNlaG9sZGVyJyl9XG4gICAgICAgICAgICAgICAgdmFsdWU9e2NvZGVTZWFyY2hUZXJtfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29kZVNlYXJjaFRlcm0oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM2YjcyODAnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgICBkaXJlY3Rpb246ICdydGwnLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzFmMjkzNydcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiDZgdmE2KrYsdipINio2KfZhNmG2YjYuSAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snLCBtYXJnaW5Cb3R0b206ICc1cHgnLCBjb2xvcjogJyNmM2Y0ZjYnLCBmb250U2l6ZTogJzAuOXJlbScgfX0+XG4gICAgICAgICAgICAgICAge3QoJ21lZGlhLm1lZGlhVHlwZScpfTpcbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgIHZhbHVlPXtzZWxlY3RlZFR5cGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWxlY3RlZFR5cGUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM2YjcyODAnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgICBkaXJlY3Rpb246IGlzUlRMID8gJ3J0bCcgOiAnbHRyJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyMxZjI5MzcnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJBTExcIj57dCgnbWVkaWFUeXBlcy5BTEwnKX08L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiRklMTVwiPnt0KCdtZWRpYVR5cGVzLkZJTE0nKX08L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiU0VSSUVTXCI+e3QoJ21lZGlhVHlwZXMuU0VSSUVTJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBST0dSQU1cIj57dCgnbWVkaWFUeXBlcy5QUk9HUkFNJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlNPTkdcIj57dCgnbWVkaWFUeXBlcy5TT05HJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkZJTExFUlwiPnt0KCdtZWRpYVR5cGVzLkZJTExFUicpfTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJTVElOR1wiPnt0KCdtZWRpYVR5cGVzLlNUSU5HJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlBST01PXCI+e3QoJ21lZGlhVHlwZXMuUFJPTU8nKX08L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiTkVYVFwiPnt0KCdtZWRpYVR5cGVzLk5FWFQnKX08L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiTk9XXCI+e3QoJ21lZGlhVHlwZXMuTk9XJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cItiz2YbYudmI2K9cIj57dCgnbWVkaWFUeXBlcy7Ys9mG2LnZiNivJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIti52K/ZhtinXCI+e3QoJ21lZGlhVHlwZXMu2LnYr9mG2KcnKX08L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiTUlOSVwiPnt0KCdtZWRpYVR5cGVzLk1JTkknKX08L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiQ1JPU1NcIj57dCgnbWVkaWFUeXBlcy5DUk9TUycpfTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7Lyog2YHZhNiq2LHYqSDYqNin2YTYrdin2YTYqSAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBzdHlsZT17eyBkaXNwbGF5OiAnYmxvY2snLCBtYXJnaW5Cb3R0b206ICc1cHgnLCBjb2xvcjogJyNmM2Y0ZjYnLCBmb250U2l6ZTogJzAuOXJlbScgfX0+XG4gICAgICAgICAgICAgICAge3QoJ21lZGlhLm1lZGlhU3RhdHVzJyl9OlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkU3RhdHVzfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VsZWN0ZWRTdGF0dXMoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzEwcHgnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM2YjcyODAnLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMXJlbScsXG4gICAgICAgICAgICAgICAgICBkaXJlY3Rpb246IGlzUlRMID8gJ3J0bCcgOiAnbHRyJyxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyMxZjI5MzcnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJBTExcIj57dCgnbWVkaWFTdGF0dXMuQUxMJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlZBTElEXCI+e3QoJ21lZGlhU3RhdHVzLlZBTElEJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlJFSkVDVEVEX0NFTlNPUlNISVBcIj57dCgnbWVkaWFTdGF0dXMuUkVKRUNURURfQ0VOU09SU0hJUCcpfTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJSRUpFQ1RFRF9URUNITklDQUxcIj57dCgnbWVkaWFTdGF0dXMuUkVKRUNURURfVEVDSE5JQ0FMJyl9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIkVYUElSRURcIj57dCgnbWVkaWFTdGF0dXMuRVhQSVJFRCcpfTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJIT0xEXCI+e3QoJ21lZGlhU3RhdHVzLkhPTEQnKX08L29wdGlvbj5cbiAgICAgICAgICAgICAgPC9zZWxlY3Q+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qINin2YTYqtix2KrZitioICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIHN0eWxlPXt7IGRpc3BsYXk6ICdibG9jaycsIG1hcmdpbkJvdHRvbTogJzVweCcsIGNvbG9yOiAnI2YzZjRmNicsIGZvbnRTaXplOiAnMC45cmVtJyB9fT5cbiAgICAgICAgICAgICAgICB7dCgnbWVkaWEuc29ydEJ5Jyl9OlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8c2VsZWN0XG4gICAgICAgICAgICAgICAgdmFsdWU9e3NvcnRCeX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFNvcnRCeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAnMTAwJScsXG4gICAgICAgICAgICAgICAgICBwYWRkaW5nOiAnMTBweCcsXG4gICAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgIzZiNzI4MCcsXG4gICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxcmVtJyxcbiAgICAgICAgICAgICAgICAgIGRpcmVjdGlvbjogaXNSVEwgPyAncnRsJyA6ICdsdHInLFxuICAgICAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzFmMjkzNydcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm5ld2VzdFwiPnt0KCdtZWRpYS5uZXdlc3QnKX08L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwib2xkZXN0XCI+e3QoJ21lZGlhLm9sZGVzdCcpfTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJuYW1lXCI+e3QoJ21lZGlhLmJ5TmFtZScpfTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJ0eXBlXCI+e3QoJ21lZGlhLmJ5VHlwZScpfTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qINil2K3Ytdin2KbZitin2Kog2KfZhNio2K3YqyAqL31cbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBtYXJnaW5Ub3A6ICcxNXB4JyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICcxMHB4JyxcbiAgICAgICAgICAgIGJhY2tncm91bmQ6ICcjMWYyOTM3JyxcbiAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLFxuICAgICAgICAgICAgY29sb3I6ICcjZDFkNWRiJyxcbiAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjNmI3MjgwJ1xuICAgICAgICAgIH19PlxuICAgICAgICAgICAg8J+TiiB7dCgnbWVkaWEuc2VhcmNoU3RhdHMnLCB7IGZpbHRlcmVkOiBmaWx0ZXJlZEl0ZW1zLmxlbmd0aCwgdG90YWw6IG1lZGlhSXRlbXMubGVuZ3RoIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTWVkaWEgSXRlbXMgKi99XG4gICAgICAgIHtmaWx0ZXJlZEl0ZW1zLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7XG4gICAgICAgICAgICBiYWNrZ3JvdW5kOiAnIzRhNTU2OCcsXG4gICAgICAgICAgICBib3JkZXJSYWRpdXM6ICcxNXB4JyxcbiAgICAgICAgICAgIHBhZGRpbmc6ICc1MHB4JyxcbiAgICAgICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsXG4gICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgIzZiNzI4MCdcbiAgICAgICAgICB9fT5cbiAgICAgICAgICAgIDxoMiBzdHlsZT17eyBjb2xvcjogJyNkMWQ1ZGInLCBmb250U2l6ZTogJzEuNXJlbScgfX0+XG4gICAgICAgICAgICAgIPCfk60ge3QoJ21lZGlhLm5vTWVkaWFGb3VuZCcpfVxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIHN0eWxlPXt7IGNvbG9yOiAnI2EwYWVjMCcsIG1hcmdpblRvcDogJzEwcHgnIH19PlxuICAgICAgICAgICAgICB7dCgnbWVkaWEuc3RhcnRBZGRpbmcnKX1cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdncmlkJywgZ2FwOiAnMjBweCcgfX0+XG4gICAgICAgICAgICB7ZmlsdGVyZWRJdGVtcy5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e2l0ZW0uaWR9IHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyM0YTU1NjgnLFxuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzE1cHgnLFxuICAgICAgICAgICAgICAgIHBhZGRpbmc6ICcyNXB4JyxcbiAgICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgIzZiNzI4MCdcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiAnZ3JpZCcsIGdyaWRUZW1wbGF0ZUNvbHVtbnM6ICcxZnIgYXV0bycsIGdhcDogJzIwcHgnLCBhbGlnbkl0ZW1zOiAnc3RhcnQnIH19PlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGgzIHN0eWxlPXt7IGNvbG9yOiAnI2YzZjRmNicsIG1hcmdpbkJvdHRvbTogJzE1cHgnLCBmb250U2l6ZTogJzEuNHJlbScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2dyaWQnLCBncmlkVGVtcGxhdGVDb2x1bW5zOiAncmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjAwcHgsIDFmcikpJywgZ2FwOiAnMTVweCcsIG1hcmdpbkJvdHRvbTogJzE1cHgnLCBjb2xvcjogJyNkMWQ1ZGInIH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPnt0KCdjb21tb24udHlwZScpfTo8L3N0cm9uZz4ge2dldFR5cGVMYWJlbChpdGVtLnR5cGUpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPnt0KCdtZWRpYS5jaGFubmVsJyl9Ojwvc3Ryb25nPiB7Z2V0Q2hhbm5lbExhYmVsKGl0ZW0uY2hhbm5lbCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+e3QoJ2NvbW1vbi5zdGF0dXMnKX06PC9zdHJvbmc+IHtnZXRTdGF0dXNMYWJlbChpdGVtLnN0YXR1cyl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzdHJvbmc+e3QoJ21lZGlhLnNlZ21lbnRDb3VudCcpfTo8L3N0cm9uZz4ge2l0ZW0uc2VnbWVudHMubGVuZ3RofVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7aXRlbS5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPHAgc3R5bGU9e3sgY29sb3I6ICcjYTBhZWMwJywgbWFyZ2luQm90dG9tOiAnMTBweCcgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPnt0KCdtZWRpYS5kZXNjcmlwdGlvbicpfTo8L3N0cm9uZz4ge2l0ZW0uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLnNlZ21lbnRzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMTVweCcsIGNvbG9yOiAnI2QxZDVkYicgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPnt0KCdtZWRpYS5zZWdtZW50cycpfTo8L3N0cm9uZz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgc3R5bGU9e3sgZGlzcGxheTogJ2dyaWQnLCBnYXA6ICc4cHgnLCBtYXJnaW5Ub3A6ICc4cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5zZWdtZW50cy5tYXAoKHNlZ21lbnQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2Ake2l0ZW0uaWR9X3NlZ21lbnRfJHtpbmRleH1gfSBzdHlsZT17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZDogJyMxZjI5MzcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzhweCAxMnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzhweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuOXJlbScsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyNkMWQ1ZGInLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkICM2YjcyODAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPiN7c2VnbWVudC5zZWdtZW50TnVtYmVyfTwvc3Ryb25nPiAtXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c2VnbWVudC5jb2RlICYmIHNlZ21lbnQuY29kZS50cmltKCkgIT09ICcnID8gYCAke3NlZ21lbnQuY29kZX0gLSBgIDogYCBbJHt0KCdtZWRpYS5ub0NvZGUnKX1dIC0gYH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWdtZW50LnRpbWVJbn0g4oaSIHtzZWdtZW50LnRpbWVPdXR9ICh7c2VnbWVudC5kdXJhdGlvbn0pXG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IGRpc3BsYXk6ICdmbGV4JywgZmxleERpcmVjdGlvbjogJ2NvbHVtbicsIGdhcDogJzEwcHgnIH19PlxuICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8g2KrZiNis2YrZhyDZhNi12YHYrdipINin2YTYqti52K/ZitmEINmF2Lkg2YXYudix2YEg2KfZhNmF2KfYr9ipXG4gICAgICAgICAgICAgICAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IGAvZWRpdC1tZWRpYT9pZD0ke2l0ZW0uaWR9YDtcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kOiAnbGluZWFyLWdyYWRpZW50KDQ1ZGVnLCAjMDA3YmZmLCAjMDA1NmIzKScsXG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAnOHB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmc6ICc4cHggMTZweCcsXG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC45cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIG1hcmdpbkJvdHRvbTogJzVweCdcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAg4pyP77iPIHt0KCdtZWRpYS5lZGl0Jyl9XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBkZWxldGVNZWRpYUl0ZW0oaXRlbS5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoNDVkZWcsICNkYzM1NDUsICNjODIzMzMpJyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6ICc4cHgnLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGFkZGluZzogJzhweCAxNnB4JyxcbiAgICAgICAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInLFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjlyZW0nXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIPCfl5HvuI8ge3QoJ21lZGlhLmRlbGV0ZScpfVxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiDYstixINin2YTYudmI2K/YqSDZhNij2LnZhNmJICovfVxuICAgICAgICB7c2hvd1Njcm9sbFRvVG9wICYmIChcbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtzY3JvbGxUb1RvcH1cbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIHBvc2l0aW9uOiAnZml4ZWQnLFxuICAgICAgICAgICAgICBib3R0b206ICczMHB4JyxcbiAgICAgICAgICAgICAgcmlnaHQ6ICczMHB4JyxcbiAgICAgICAgICAgICAgd2lkdGg6ICc2MHB4JyxcbiAgICAgICAgICAgICAgaGVpZ2h0OiAnNjBweCcsXG4gICAgICAgICAgICAgIGJvcmRlclJhZGl1czogJzUwJScsXG4gICAgICAgICAgICAgIGJhY2tncm91bmQ6ICdsaW5lYXItZ3JhZGllbnQoNDVkZWcsICMwMDdiZmYsICMwMDU2YjMpJyxcbiAgICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICAgIGJvcmRlcjogJ25vbmUnLFxuICAgICAgICAgICAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICAgICAgICAgICAgZm9udFNpemU6ICcyNHB4JyxcbiAgICAgICAgICAgICAgYm94U2hhZG93OiAnMCA0cHggMTJweCByZ2JhKDAsIDEyMywgMjU1LCAwLjMpJyxcbiAgICAgICAgICAgICAgekluZGV4OiAxMDAwLFxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuM3MgZWFzZScsXG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcbiAgICAgICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJ1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIG9uTW91c2VFbnRlcj17KGUpID0+IHtcbiAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICdzY2FsZSgxLjEpJztcbiAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLmJveFNoYWRvdyA9ICcwIDZweCAxNnB4IHJnYmEoMCwgMTIzLCAyNTUsIDAuNCknO1xuICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17KGUpID0+IHtcbiAgICAgICAgICAgICAgZS5jdXJyZW50VGFyZ2V0LnN0eWxlLnRyYW5zZm9ybSA9ICdzY2FsZSgxKSc7XG4gICAgICAgICAgICAgIGUuY3VycmVudFRhcmdldC5zdHlsZS5ib3hTaGFkb3cgPSAnMCA0cHggMTJweCByZ2JhKDAsIDEyMywgMjU1LCAwLjMpJztcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgICB0aXRsZT17dCgnbWVkaWEuc2Nyb2xsVG9Ub3AnKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICDirIbvuI9cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgKX1cbiAgICAgICAgPFRvYXN0Q29udGFpbmVyIC8+XG4gICAgICA8L0Rhc2hib2FyZExheW91dD5cbiAgICA8L0F1dGhHdWFyZD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkF1dGhHdWFyZCIsIkRhc2hib2FyZExheW91dCIsInVzZUFwcFRyYW5zbGF0aW9uIiwidXNlVHJhbnNsYXRlZFRvYXN0IiwiTWVkaWFMaXN0UGFnZSIsInQiLCJ0TWVkaWFUeXBlIiwiaXNSVEwiLCJzaG93U3VjY2Vzc1RvYXN0Iiwic2hvd0Vycm9yVG9hc3QiLCJUb2FzdENvbnRhaW5lciIsIm1lZGlhSXRlbXMiLCJzZXRNZWRpYUl0ZW1zIiwiZmlsdGVyZWRJdGVtcyIsInNldEZpbHRlcmVkSXRlbXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsImNvZGVTZWFyY2hUZXJtIiwic2V0Q29kZVNlYXJjaFRlcm0iLCJzZWxlY3RlZFR5cGUiLCJzZXRTZWxlY3RlZFR5cGUiLCJzZWxlY3RlZFN0YXR1cyIsInNldFNlbGVjdGVkU3RhdHVzIiwic29ydEJ5Iiwic2V0U29ydEJ5IiwiaXNFeHBvcnRpbmciLCJzZXRJc0V4cG9ydGluZyIsInNob3dTY3JvbGxUb1RvcCIsInNldFNob3dTY3JvbGxUb1RvcCIsImZldGNoTWVkaWFJdGVtcyIsImhhbmRsZVNjcm9sbCIsIndpbmRvdyIsInNjcm9sbFkiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImZpbHRlckFuZFNvcnRJdGVtcyIsInNjcm9sbFRvVG9wIiwic2Nyb2xsVG8iLCJ0b3AiLCJiZWhhdmlvciIsInJlc3BvbnNlIiwiZmV0Y2giLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsImRhdGEiLCJjb25zb2xlIiwiZmlsdGVyZWQiLCJmaWx0ZXIiLCJpdGVtIiwibmFtZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJkZXNjcmlwdGlvbiIsImlkIiwic2VnbWVudHMiLCJsZW5ndGgiLCJzb21lIiwic2VnbWVudCIsImNvZGUiLCJ0eXBlIiwic3RhdHVzIiwic29ydCIsImEiLCJiIiwiRGF0ZSIsImNyZWF0ZWRBdCIsImdldFRpbWUiLCJsb2NhbGVDb21wYXJlIiwiZGVsZXRlTWVkaWFJdGVtIiwiY29uZmlybSIsInVzZXIiLCJKU09OIiwicGFyc2UiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidG9rZW5XaXRoUm9sZSIsInJvbGUiLCJsb2ciLCJtZXRob2QiLCJoZWFkZXJzIiwiZXhwb3J0VG9FeGNlbCIsInBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsImFwcGVuZCIsImFwaVVybCIsInRvU3RyaW5nIiwib2siLCJFcnJvciIsImJsb2IiLCJkb3dubG9hZFVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJocmVmIiwiZmlsZU5hbWUiLCJ0b0lTT1N0cmluZyIsInNwbGl0IiwiZG93bmxvYWQiLCJib2R5IiwiYXBwZW5kQ2hpbGQiLCJjbGljayIsInJlbW92ZUNoaWxkIiwicmV2b2tlT2JqZWN0VVJMIiwiZ2V0VHlwZUxhYmVsIiwiZ2V0U3RhdHVzTGFiZWwiLCJnZXRDaGFubmVsTGFiZWwiLCJjaGFubmVsIiwiZGl2Iiwic3R5bGUiLCJtaW5IZWlnaHQiLCJiYWNrZ3JvdW5kIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJqdXN0aWZ5Q29udGVudCIsImNvbG9yIiwiZm9udFNpemUiLCJyZXF1aXJlZFBlcm1pc3Npb25zIiwidGl0bGUiLCJzdWJ0aXRsZSIsImljb24iLCJwYWRkaW5nIiwiYm9yZGVyUmFkaXVzIiwibWFyZ2luQm90dG9tIiwidGV4dEFsaWduIiwiYm94U2hhZG93IiwiaDMiLCJtYXJnaW4iLCJwIiwib3BhY2l0eSIsImdhcCIsImZsZXhXcmFwIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwiYm9yZGVyIiwiZm9udFdlaWdodCIsImN1cnNvciIsImgyIiwiZ3JpZFRlbXBsYXRlQ29sdW1ucyIsImxhYmVsIiwiaW5wdXQiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwid2lkdGgiLCJkaXJlY3Rpb24iLCJzZWxlY3QiLCJvcHRpb24iLCJtYXJnaW5Ub3AiLCJ0b3RhbCIsIm1hcCIsInN0cm9uZyIsImluZGV4Iiwic2VnbWVudE51bWJlciIsInRyaW0iLCJ0aW1lSW4iLCJ0aW1lT3V0IiwiZHVyYXRpb24iLCJmbGV4RGlyZWN0aW9uIiwibG9jYXRpb24iLCJwb3NpdGlvbiIsImJvdHRvbSIsInJpZ2h0IiwiaGVpZ2h0IiwiekluZGV4IiwidHJhbnNpdGlvbiIsIm9uTW91c2VFbnRlciIsImN1cnJlbnRUYXJnZXQiLCJ0cmFuc2Zvcm0iLCJvbk1vdXNlTGVhdmUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/media-list/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthGuard.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthGuard.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthGuard: () => (/* binding */ AuthGuard),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AuthGuard,useAuth auto */ \n\n\nfunction AuthGuard({ children, requiredPermissions = [], requiredRole, fallbackComponent }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [hasAccess, setHasAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthGuard.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthGuard.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            // التحقق من وجود بيانات المستخدم في localStorage\n            const userData = localStorage.getItem('user');\n            const token = localStorage.getItem('token');\n            if (!userData || !token) {\n                router.push('/login');\n                return;\n            }\n            const parsedUser = JSON.parse(userData);\n            setUser(parsedUser);\n            // التحقق من الصلاحيات\n            const access = checkPermissions(parsedUser, requiredPermissions, requiredRole);\n            setHasAccess(access);\n            if (!access && fallbackComponent === undefined) {\n                router.push('/unauthorized');\n            }\n        } catch (error) {\n            console.error('Auth check error:', error);\n            router.push('/login');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const checkPermissions = (user, permissions, role)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    // المدير له صلاحيات كاملة\n    if (user.role === 'ADMIN') {\n      console.log('✅ المستخدم هو مدير النظام - تم منح جميع الصلاحيات');\n      return true;\n    }\n\n    // التحقق من الدور المطلوب\n    if (role && user.role !== role) {\n      console.log(`❌ المستخدم ليس لديه الدور المطلوب: ${role}`);\n      return false;\n    }\n\n    // التحقق من الصلاحيات المطلوبة\n    if (permissions.length > 0) {\n      const userPermissions = getUserPermissions(user.role);\n      console.log('🔍 التحقق من الصلاحيات:', {\n        required: permissions,\n        userHas: userPermissions\n      });\n      \n      const hasAllPermissions = permissions.every(permission => \n        userPermissions.includes(permission) || userPermissions.includes('ALL')\n      );\n      \n      if (!hasAllPermissions) {\n        console.log('❌ المستخدم ليس لديه جميع الصلاحيات المطلوبة');\n      } else {\n        console.log('✅ المستخدم لديه جميع الصلاحيات المطلوبة');\n      }\n      \n      return hasAllPermissions;\n    }\n\n    return true;\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '50px',\n                            height: '50px',\n                            border: '4px solid #f3f3f3',\n                            borderTop: '4px solid #667eea',\n                            borderRadius: '50%',\n                            margin: '0 auto 20px'\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#333',\n                            margin: 0\n                        },\n                        children: \"⏳ جاري التحقق من الصلاحيات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 135,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    if (!hasAccess) {\n        if (fallbackComponent) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: fallbackComponent\n            }, void 0, false);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'white',\n                    borderRadius: '20px',\n                    padding: '40px',\n                    textAlign: 'center',\n                    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',\n                    maxWidth: '500px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '4rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"\\uD83D\\uDEAB\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#dc3545',\n                            marginBottom: '15px',\n                            fontSize: '1.5rem'\n                        },\n                        children: \"غير مصرح لك بالوصول\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#6c757d',\n                            marginBottom: '25px',\n                            fontSize: '1rem'\n                        },\n                        children: \"ليس لديك الصلاحيات المطلوبة للوصول إلى هذه الصفحة\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#f8f9fa',\n                            padding: '15px',\n                            borderRadius: '10px',\n                            marginBottom: '25px',\n                            textAlign: 'right'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"معلومات المستخدم:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 47\n                            }, this),\n                            \"الاسم: \",\n                            user?.name,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 32\n                            }, this),\n                            \"الدور: \",\n                            user?.role,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 32\n                            }, this),\n                            \"الصلاحيات المطلوبة: \",\n                            requiredPermissions.join(', ') || 'غير محدد'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>router.push('/'),\n                        style: {\n                            background: 'linear-gradient(45deg, #667eea, #764ba2)',\n                            color: 'white',\n                            border: 'none',\n                            borderRadius: '10px',\n                            padding: '12px 25px',\n                            fontSize: '1rem',\n                            cursor: 'pointer',\n                            fontFamily: 'Cairo, Arial, sans-serif'\n                        },\n                        children: \"\\uD83C\\uDFE0 العودة للرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n                lineNumber: 171,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\AuthGuard.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n// Hook لاستخدام بيانات المستخدم الحالي\nfunction useAuth() {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAuth.useEffect\": ()=>{\n            const userData = localStorage.getItem('user');\n            if (userData) {\n                setUser(JSON.parse(userData));\n            }\n            setIsLoading(false);\n        }\n    }[\"useAuth.useEffect\"], []);\n    const logout = ()=>{\n        localStorage.removeItem('user');\n        localStorage.removeItem('token');\n        window.location.href = '/login';\n    };\n    const hasPermission = (permission)=>{\n        console.log('⚠️ تجاوز التحقق من الصلاحيات مؤقتاً للتطوير');\n        return true; // مؤقتاً: السماح بجميع العمليات لجميع المستخدمين (للتطوير فقط)\n    /*\n    if (!user) return false;\n    if (user.role === 'ADMIN') return true;\n\n    const userPermissions = getUserPermissions(user.role);\n    return userPermissions.includes(permission) || userPermissions.includes('ALL');\n    */ };\n    const getUserPermissions = (role)=>{\n        const rolePermissions = {\n            'ADMIN': [\n                'ALL'\n            ],\n            'CONTENT_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE'\n            ],\n            'MEDIA_MANAGER': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE',\n                'SCHEDULE_READ'\n            ],\n            'SCHEDULER': [\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'FULL_VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ',\n                'MAP_READ',\n                'BROADCAST_READ',\n                'REPORT_READ',\n                'DASHBOARD_READ'\n            ],\n            'DATA_ENTRY': [\n                'MEDIA_CREATE',\n                'MEDIA_READ',\n                'MEDIA_UPDATE',\n                'MEDIA_DELETE'\n            ],\n            'MAP_SCHEDULER': [\n                'MAP_CREATE',\n                'MAP_READ',\n                'MAP_UPDATE',\n                'MAP_DELETE',\n                'SCHEDULE_CREATE',\n                'SCHEDULE_READ',\n                'SCHEDULE_UPDATE',\n                'SCHEDULE_DELETE',\n                'MEDIA_READ'\n            ],\n            'VIEWER': [\n                'MEDIA_READ',\n                'SCHEDULE_READ'\n            ]\n        };\n        return rolePermissions[role] || [];\n    };\n    return {\n        user,\n        isLoading,\n        logout,\n        hasPermission,\n        isAdmin: user?.role === 'ADMIN',\n        isMediaManager: user?.role === 'MEDIA_MANAGER',\n        isScheduler: user?.role === 'SCHEDULER',\n        isViewer: user?.role === 'VIEWER',\n        isFullViewer: user?.role === 'FULL_VIEWER',\n        isDataEntry: user?.role === 'DATA_ENTRY',\n        isMapScheduler: user?.role === 'MAP_SCHEDULER',\n        isContentManager: user?.role === 'CONTENT_MANAGER'\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthGuard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/DashboardLayout.tsx":
/*!********************************************!*\
  !*** ./src/components/DashboardLayout.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(ssr)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction DashboardLayout({ children, title = '', subtitle = '', icon = '📊', requiredPermissions, requiredRole, fullWidth = false }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL, currentLang, changeLanguage, isReady } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardLayout.useEffect\": ()=>{\n            const timer = setInterval({\n                \"DashboardLayout.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"DashboardLayout.useEffect.timer\"], 1000);\n            return ({\n                \"DashboardLayout.useEffect\": ()=>clearInterval(timer)\n            })[\"DashboardLayout.useEffect\"];\n        }\n    }[\"DashboardLayout.useEffect\"], []);\n    const navigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: false,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            active: false,\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.unifiedSystem'),\n            icon: '📤',\n            active: false,\n            path: '/unified-system',\n            adminOnly: true,\n            superAdminOnly: true\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard'\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics'\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.superAdminOnly && (user?.role !== 'ADMIN' || user?.username !== 'admin')) return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        requiredPermissions: requiredPermissions,\n        requiredRole: requiredRole,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"dashboard-layout\",\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: isRTL ? 'rtl' : 'ltr'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: navigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const newLang = currentLang === 'ar' ? 'en' : 'ar';\n                                        changeLanguage(newLang);\n                                    },\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '5px'\n                                    },\n                                    title: currentLang === 'ar' ? 'Switch to English' : 'التبديل للعربية',\n                                    children: [\n                                        \"\\uD83C\\uDF10\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: currentLang === 'ar' ? 'EN' : 'عر'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    title: t('auth.logout'),\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        ...isRTL ? {\n                            marginRight: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-right 0.3s ease'\n                        } : {\n                            marginLeft: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-left 0.3s ease'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: subtitle\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: t('common.active')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: currentTime.toLocaleTimeString(isRTL ? 'ar-EG' : 'en-US')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"dashboard-card\",\n                            style: {\n                                background: '#2d3748',\n                                borderRadius: '20px',\n                                padding: '25px',\n                                border: '1px solid #4a5568',\n                                boxShadow: '0 10px 30px rgba(0,0,0,0.2)',\n                                maxWidth: fullWidth ? 'none' : '1000px',\n                                margin: '0 auto',\n                                width: fullWidth ? '100%' : 'auto'\n                            },\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\DashboardLayout.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Logo.tsx":
/*!*********************************!*\
  !*** ./src/components/Logo.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Logo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Logo({ size = 'medium', className, style }) {\n    const sizes = {\n        small: {\n            fontSize: '1rem',\n            gap: '3px',\n            xSize: '1.2rem'\n        },\n        medium: {\n            fontSize: '1.2rem',\n            gap: '5px',\n            xSize: '1.5rem'\n        },\n        large: {\n            fontSize: '2rem',\n            gap: '8px',\n            xSize: '2.5rem'\n        }\n    };\n    const currentSize = sizes[size];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        style: {\n            display: 'flex',\n            alignItems: 'center',\n            fontSize: currentSize.fontSize,\n            fontWeight: '900',\n            fontFamily: 'Arial, sans-serif',\n            gap: currentSize.gap,\n            direction: 'ltr',\n            ...style\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '800',\n                    letterSpacing: '1px'\n                },\n                children: \"Prime\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    color: '#6c757d',\n                    fontSize: '0.8em',\n                    fontWeight: '300'\n                },\n                children: \"-\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                style: {\n                    background: 'linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%)',\n                    WebkitBackgroundClip: 'text',\n                    WebkitTextFillColor: 'transparent',\n                    fontWeight: '900',\n                    fontSize: currentSize.xSize\n                },\n                children: \"X\"\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Logo.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Logo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(ssr)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(ssr)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(ssr)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Sidebar({ isOpen, onToggle }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const menuItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.importSchedule'),\n            icon: '📤',\n            path: '/daily-schedule/import',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            path: '/statistics',\n            permission: null\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && user?.role !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    ...isRTL ? {\n                        right: isOpen ? 0 : '-280px',\n                        borderLeft: '1px solid #2d3748'\n                    } : {\n                        left: isOpen ? 0 : '-280px',\n                        borderRight: '1px solid #2d3748'\n                    },\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    transition: `${isRTL ? 'right' : 'left'} 0.3s ease`,\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: \"small\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                margin: 0,\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: t('dashboard.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#2d3748' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    borderTop: 'none',\n                                    borderBottom: 'none',\n                                    ...isRTL ? {\n                                        borderLeft: 'none',\n                                        borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    } : {\n                                        borderRight: 'none',\n                                        borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    },\n                                    padding: isRTL ? '12px 20px 12px 8px' : '12px 8px 12px 20px',\n                                    textAlign: isRTL ? 'right' : 'left',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    transition: 'all 0.2s ease',\n                                    direction: isRTL ? 'rtl' : 'ltr'\n                                },\n                                children: item.name\n                            }, index, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('user');\n                                localStorage.removeItem('token');\n                                router.push('/login');\n                            },\n                            style: {\n                                width: '100%',\n                                background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '10px',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '8px',\n                                fontSize: '0.9rem',\n                                fontWeight: 'bold',\n                                marginBottom: '15px'\n                            },\n                            children: [\n                                \"\\uD83D\\uDEAA \",\n                                t('navigation.logout')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Toast.tsx":
/*!**********************************!*\
  !*** ./src/components/Toast.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default,useToast auto */ \n\nfunction Toast({ message, type, duration = 3000, onClose }) {\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"Toast.useEffect.timer\": ()=>{\n                    setIsVisible(false);\n                    setTimeout(onClose, 300); // انتظار انتهاء الأنيميشن\n                }\n            }[\"Toast.useEffect.timer\"], duration);\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timer)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        duration,\n        onClose\n    ]);\n    const getToastStyles = ()=>{\n        const baseStyles = {\n            position: 'relative',\n            padding: '15px 20px',\n            borderRadius: '10px',\n            color: 'white',\n            fontWeight: 'bold',\n            fontSize: '1rem',\n            boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n            transform: isVisible ? 'translateX(0)' : 'translateX(100%)',\n            transition: 'transform 0.3s ease, opacity 0.3s ease',\n            opacity: isVisible ? 1 : 0,\n            minWidth: '300px',\n            maxWidth: '500px',\n            direction: 'rtl',\n            fontFamily: 'Cairo, Arial, sans-serif'\n        };\n        const typeStyles = {\n            success: {\n                background: 'linear-gradient(45deg, #28a745, #20c997)'\n            },\n            error: {\n                background: 'linear-gradient(45deg, #dc3545, #c82333)'\n            },\n            warning: {\n                background: 'linear-gradient(45deg, #ffc107, #e0a800)'\n            },\n            info: {\n                background: 'linear-gradient(45deg, #007bff, #0056b3)'\n            }\n        };\n        return {\n            ...baseStyles,\n            ...typeStyles[type]\n        };\n    };\n    const getIcon = ()=>{\n        const icons = {\n            success: '✅',\n            error: '❌',\n            warning: '⚠️',\n            info: 'ℹ️'\n        };\n        return icons[type];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: getToastStyles(),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '10px'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        fontSize: '1.2rem'\n                    },\n                    children: getIcon()\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: message\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setIsVisible(false);\n                        setTimeout(onClose, 300);\n                    },\n                    style: {\n                        background: 'rgba(255,255,255,0.2)',\n                        border: 'none',\n                        color: 'white',\n                        borderRadius: '50%',\n                        width: '25px',\n                        height: '25px',\n                        cursor: 'pointer',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        marginLeft: 'auto'\n                    },\n                    children: \"\\xd7\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n// Hook لاستخدام Toast\nfunction useToast() {\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const showToast = (message, type = 'info')=>{\n        // التحقق من عدم وجود رسالة مشابهة\n        const existingToast = toasts.find((toast)=>toast.message === message && toast.type === type);\n        if (existingToast) {\n            return; // لا تضيف رسالة مكررة\n        }\n        // إنشاء ID فريد باستخدام timestamp + random number\n        const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        setToasts((prev)=>[\n                ...prev,\n                {\n                    id,\n                    message,\n                    type\n                }\n            ]);\n        // حد أقصى 5 رسائل في نفس الوقت\n        setToasts((prev)=>prev.slice(-4)); // احتفظ بآخر 4 + الجديدة = 5\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const ToastContainer = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                position: 'fixed',\n                top: '20px',\n                right: '20px',\n                zIndex: 1000,\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '10px'\n            },\n            children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                    message: toast.message,\n                    type: toast.type,\n                    onClose: ()=>removeToast(toast.id)\n                }, toast.id, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this))\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Toast.tsx\",\n            lineNumber: 129,\n            columnNumber: 5\n        }, this);\n    return {\n        showToast,\n        ToastContainer\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Toast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAppTranslation.ts":
/*!****************************************!*\
  !*** ./src/hooks/useAppTranslation.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppTranslation: () => (/* binding */ useAppTranslation)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// نظام ترجمة احترافي مثل التطبيقات الكبيرة\nconst useAppTranslation = ()=>{\n    const { i18n, t: i18nT } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)('common');\n    const [currentLang, setCurrentLang] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('ar');\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تهيئة اللغة عند بدء التطبيق\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAppTranslation.useEffect\": ()=>{\n            const initLanguage = {\n                \"useAppTranslation.useEffect.initLanguage\": ()=>{\n                    try {\n                        // جلب اللغة المحفوظة أو استخدام العربية كافتراضي\n                        const savedLang = localStorage.getItem('language') || 'ar';\n                        const validLang = savedLang === 'en' || savedLang === 'ar' ? savedLang : 'ar';\n                        setCurrentLang(validLang);\n                        i18n.changeLanguage(validLang);\n                        setIsReady(true);\n                        console.log('🌐 Language initialized:', validLang);\n                    } catch (error) {\n                        console.error('❌ Language initialization error:', error);\n                        setCurrentLang('ar');\n                        setIsReady(true);\n                    }\n                }\n            }[\"useAppTranslation.useEffect.initLanguage\"];\n            initLanguage();\n        }\n    }[\"useAppTranslation.useEffect\"], [\n        i18n\n    ]);\n    // مراقبة تغيير اللغة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useAppTranslation.useEffect\": ()=>{\n            const handleLanguageChange = {\n                \"useAppTranslation.useEffect.handleLanguageChange\": (lng)=>{\n                    const validLang = lng === 'en' || lng === 'ar' ? lng : 'ar';\n                    setCurrentLang(validLang);\n                    localStorage.setItem('language', validLang);\n                    console.log('🔄 Language changed to:', validLang);\n                }\n            }[\"useAppTranslation.useEffect.handleLanguageChange\"];\n            i18n.on('languageChanged', handleLanguageChange);\n            return ({\n                \"useAppTranslation.useEffect\": ()=>i18n.off('languageChanged', handleLanguageChange)\n            })[\"useAppTranslation.useEffect\"];\n        }\n    }[\"useAppTranslation.useEffect\"], [\n        i18n\n    ]);\n    // دالة ترجمة آمنة ومضمونة مع interpolation\n    const t = (key, options, fallback)=>{\n        try {\n            if (!isReady) return fallback || key;\n            let translation = i18nT(key, options);\n            // إذا كانت الترجمة مفقودة، استخدم الاحتياطي أو المفتاح\n            if (translation === key && fallback) {\n                translation = fallback;\n            }\n            // معالجة interpolation يدوياً إذا لم يعمل i18next\n            if (options && translation && typeof translation === 'string') {\n                Object.keys(options).forEach((optionKey)=>{\n                    const placeholder = `{{${optionKey}}}`;\n                    if (translation.includes(placeholder)) {\n                        translation = translation.replace(new RegExp(placeholder, 'g'), String(options[optionKey]));\n                    }\n                });\n            }\n            return translation || fallback || key;\n        } catch (error) {\n            console.error('❌ Translation error for key:', key, error);\n            return fallback || key;\n        }\n    };\n    // دالة تغيير اللغة\n    const changeLanguage = (newLang)=>{\n        try {\n            i18n.changeLanguage(newLang);\n        } catch (error) {\n            console.error('❌ Language change error:', error);\n        }\n    };\n    // دالة ترجمة أنواع المواد (محفوظة كما هي)\n    const tMediaType = (type)=>{\n        const mediaTypeMap = {\n            'FILM': {\n                ar: 'Film',\n                en: 'Film'\n            },\n            'SERIES': {\n                ar: 'Series',\n                en: 'Series'\n            },\n            'PROGRAM': {\n                ar: 'Program',\n                en: 'Program'\n            },\n            'SONG': {\n                ar: 'Song',\n                en: 'Song'\n            },\n            'FILLER': {\n                ar: 'Filler',\n                en: 'Filler'\n            },\n            'STING': {\n                ar: 'Sting',\n                en: 'Sting'\n            },\n            'PROMO': {\n                ar: 'Promo',\n                en: 'Promo'\n            },\n            'NEXT': {\n                ar: 'Next',\n                en: 'Next'\n            },\n            'NOW': {\n                ar: 'Now',\n                en: 'Now'\n            },\n            'سنعود': {\n                ar: 'سنعود',\n                en: 'سنعود'\n            },\n            'عدنا': {\n                ar: 'عدنا',\n                en: 'عدنا'\n            },\n            'MINI': {\n                ar: 'Mini',\n                en: 'Mini'\n            },\n            'CROSS': {\n                ar: 'Cross',\n                en: 'Cross'\n            },\n            'ALL': {\n                ar: 'جميع الأنواع',\n                en: 'All Types'\n            }\n        };\n        return mediaTypeMap[type]?.[currentLang] || type;\n    };\n    // دالة ترجمة أسماء الأدوار\n    const tRole = (role)=>{\n        const roleMap = {\n            'ADMIN': {\n                ar: 'مدير النظام',\n                en: 'System Admin'\n            },\n            'CONTENT_MANAGER': {\n                ar: 'مدير المحتوى',\n                en: 'Content Manager'\n            },\n            'MEDIA_MANAGER': {\n                ar: 'مدير المواد الإعلامية',\n                en: 'Media Manager'\n            },\n            'SCHEDULER': {\n                ar: 'مجدول البرامج',\n                en: 'Scheduler'\n            },\n            'FULL_VIEWER': {\n                ar: 'مستخدم رؤية كاملة',\n                en: 'Full Viewer'\n            },\n            'DATA_ENTRY': {\n                ar: 'مدخل بيانات',\n                en: 'Data Entry'\n            },\n            'MAP_SCHEDULER': {\n                ar: 'مسؤول الخريطة والجدول',\n                en: 'Map Scheduler'\n            },\n            'VIEWER': {\n                ar: 'مستخدم عرض',\n                en: 'Viewer'\n            }\n        };\n        return roleMap[role]?.[currentLang] || role;\n    };\n    // دالة ترجمة أوصاف الأدوار\n    const tRoleDesc = (role)=>{\n        const roleDescMap = {\n            'ADMIN': {\n                ar: 'وصول كامل للنظام وإدارة المستخدمين',\n                en: 'Full system access and user management'\n            },\n            'CONTENT_MANAGER': {\n                ar: 'إدارة المحتوى الإعلامي والجداول',\n                en: 'Media content and schedule management'\n            },\n            'MEDIA_MANAGER': {\n                ar: 'إدارة مكتبة المواد الإعلامية والمحتوى',\n                en: 'Media library and content management'\n            },\n            'SCHEDULER': {\n                ar: 'إنشاء وإدارة جداول البث',\n                en: 'Create and manage broadcast schedules'\n            },\n            'FULL_VIEWER': {\n                ar: 'عرض جميع بيانات النظام والتقارير',\n                en: 'View all system data and reports'\n            },\n            'DATA_ENTRY': {\n                ar: 'إضافة وتعديل المحتوى الإعلامي',\n                en: 'Add and edit media content'\n            },\n            'MAP_SCHEDULER': {\n                ar: 'إدارة خرائط البرامج والجداول الإذاعية اليومية مع عرض قاعدة البيانات بدون تعديل',\n                en: 'Manage program maps and daily schedules with database view without editing'\n            },\n            'VIEWER': {\n                ar: 'عرض المحتوى فقط بدون إمكانيات التعديل أو الإضافة',\n                en: 'View content only without editing or adding capabilities'\n            }\n        };\n        return roleDescMap[role]?.[currentLang] || role;\n    };\n    return {\n        t,\n        tMediaType,\n        tRole,\n        tRoleDesc,\n        currentLang,\n        isRTL: currentLang === 'ar',\n        isReady,\n        changeLanguage,\n        i18n\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAppTranslation.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useTranslatedToast.ts":
/*!*****************************************!*\
  !*** ./src/hooks/useTranslatedToast.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTranslatedToast: () => (/* binding */ useTranslatedToast)\n/* harmony export */ });\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_Toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Toast */ \"(ssr)/./src/components/Toast.tsx\");\n\n\nconst useTranslatedToast = ()=>{\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_0__.useTranslation)();\n    const { showToast, ToastContainer } = (0,_components_Toast__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    const showTranslatedToast = (messageKey, type = 'info')=>{\n        const message = t(`messages.${type}.${messageKey}`);\n        showToast(message, type);\n    };\n    const showSuccessToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'success');\n    };\n    const showErrorToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'error');\n    };\n    const showInfoToast = (messageKey)=>{\n        showTranslatedToast(messageKey, 'info');\n    };\n    return {\n        showTranslatedToast,\n        showSuccessToast,\n        showErrorToast,\n        showInfoToast,\n        ToastContainer\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useTranslatedToast.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/i18n.ts":
/*!*************************!*\
  !*** ./src/lib/i18n.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(ssr)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(ssr)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _public_locales_ar_common_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../public/locales/ar/common.json */ \"(ssr)/./public/locales/ar/common.json\");\n/* harmony import */ var _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../public/locales/en/common.json */ \"(ssr)/./public/locales/en/common.json\");\n\n\n// Import translation files\n\n\nconst resources = {\n    ar: {\n        common: _public_locales_ar_common_json__WEBPACK_IMPORTED_MODULE_2__\n    },\n    en: {\n        common: _public_locales_en_common_json__WEBPACK_IMPORTED_MODULE_3__\n    }\n};\n// Get saved language from localStorage or default to Arabic\nconst getInitialLanguage = ()=>{\n    if (false) {}\n    return 'ar';\n};\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    lng: getInitialLanguage(),\n    fallbackLng: 'ar',\n    debug: false,\n    interpolation: {\n        escapeValue: false\n    },\n    react: {\n        useSuspense: false\n    },\n    // Configure namespaces\n    defaultNS: 'common',\n    ns: [\n        'common'\n    ]\n});\n// Listen for language changes and update document direction\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].on('languageChanged', (lng)=>{\n    if (false) {}\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/i18n.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/i18next","vendor-chunks/react-i18next","vendor-chunks/@swc","vendor-chunks/html-parse-stringify","vendor-chunks/void-elements"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmedia-list%2Fpage&page=%2Fmedia-list%2Fpage&appPaths=%2Fmedia-list%2Fpage&pagePath=private-next-app-dir%2Fmedia-list%2Fpage.tsx&appDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cproject%20sport%5Cmedia-dashboard-clean%5Cmedia-dashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();