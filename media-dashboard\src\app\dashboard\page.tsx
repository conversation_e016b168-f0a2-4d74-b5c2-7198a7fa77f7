'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import Sidebar from '@/components/Sidebar';
import StatsCard from '@/components/StatsCard';
import NavigationCard from '@/components/NavigationCard';
import Logo from '@/components/Logo';
import { useAppTranslation } from '@/hooks/useAppTranslation';
import '@/styles/dashboard.css';

export default function Dashboard() {
  const router = useRouter();
  const { user, logout, hasPermission } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { t, isRTL, currentLang, changeLanguage, isReady } = useAppTranslation();

  // تعريف عناصر التنقل في القائمة العلوية
  const topNavigationItems = [
    { name: t('navigation.dashboard'), icon: '📊', active: true, path: '/dashboard' },
    { name: t('navigation.mediaList'), icon: '🎬', active: false, path: '/media-list' },
    { name: t('navigation.addMedia'), icon: '➕', active: false, path: '/add-media' },
    { name: t('navigation.weeklySchedule'), icon: '📅', active: false, path: '/weekly-schedule' },
    { name: t('navigation.dailySchedule'), icon: '📊', active: false, path: '/daily-schedule' },
    { name: t('navigation.reports'), icon: '📋', active: false, path: '/reports' },
    ...(user?.role === 'ADMIN' && user?.username === 'admin' ? [{ name: t('navigation.unifiedSystem'), icon: '📤', active: false, path: '/unified-system' }] : []),
    { name: t('navigation.adminDashboard'), icon: '👥', active: false, path: '/admin-dashboard' },
    { name: t('navigation.statistics'), icon: '📈', active: false, path: '/statistics' }
  ];

  // تحديث الوقت كل ثانية
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // بيانات الإحصائيات الحقيقية
  const [stats, setStats] = useState({
    totalMedia: 0,
    validMedia: 0,
    rejectedMedia: 0,
    expiredMedia: 0,
    pendingMedia: 0,
    activeUsers: 0,
    onlineUsers: 0,
    todayAdded: 0
  });

  // حالة تنبيه المواد المنتهية
  const [expiredAlertShown, setExpiredAlertShown] = useState(false);

  const [loading, setLoading] = useState(true);

  // تخزين المواد المنتهية للتنبيه
  const [expiredMediaItems, setExpiredMediaItems] = useState([]);
  const [showExpiredAlert, setShowExpiredAlert] = useState(false);

  // جلب البيانات الحقيقية
  useEffect(() => {
    fetchRealStats();
    // تحديث البيانات كل 30 ثانية
    const interval = setInterval(fetchRealStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchRealStats = async () => {
    try {
      setLoading(true);

      // جلب البيانات من API مع timeout أطول
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 ثانية timeout

      const [mediaResponse, usersResponse] = await Promise.all([
        fetch('/api/media', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          }
        }),
        fetch('/api/users', {
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
          }
        })
      ]);

      clearTimeout(timeoutId);

      let mediaData = [];
      let userData = [];

      if (mediaResponse.ok) {
        try {
          const mediaResult = await mediaResponse.json();
          mediaData = mediaResult.success ? mediaResult.data : [];
        } catch (e) {
          console.warn('Failed to parse media response:', e);
        }
      }

      if (usersResponse.ok) {
        try {
          const usersResult = await usersResponse.json();
          userData = usersResult.success ? usersResult.users : [];
        } catch (e) {
          console.warn('Failed to parse users response:', e);
        }
      }

      // حساب الإحصائيات الحقيقية
      const totalMedia = mediaData.length;
      const validMedia = mediaData.filter(item => item.status === 'VALID').length;
      const rejectedMedia = mediaData.filter(item =>
        item.status === 'REJECTED_CENSORSHIP' || item.status === 'REJECTED_TECHNICAL'
      ).length;
      
      // التحقق من المواد المنتهية حسب التاريخ
      const today = new Date();
      const expiredByDateItems = mediaData.filter(item => 
        item.endDate && new Date(item.endDate) < today
      );
      
      // المواد المنتهية (إما بالحالة أو بالتاريخ)
      const expiredStatusItems = mediaData.filter(item => item.status === 'EXPIRED');
      const allExpiredItems = [...new Set([...expiredByDateItems, ...expiredStatusItems])];
      const expiredMedia = allExpiredItems.length;
      
      // تحديث قائمة المواد المنتهية للتنبيه
      setExpiredMediaItems(allExpiredItems);

      // إظهار التنبيه مرة واحدة فقط في الجلسة
      const alertKey = `expired_alert_${new Date().toDateString()}`;
      const alertShownToday = localStorage.getItem(alertKey);

      if (allExpiredItems.length > 0 && !alertShownToday && !expiredAlertShown) {
        setShowExpiredAlert(true);
        setExpiredAlertShown(true);
        localStorage.setItem(alertKey, 'true');
        console.log(`⚠️ تم العثور على ${allExpiredItems.length} مادة منتهية - عرض التنبيه`);
      } else {
        setShowExpiredAlert(false);
      }
      
      const pendingMedia = mediaData.filter(item => item.status === 'PENDING').length;

      // حساب المواد المضافة اليوم
      const todayStr = new Date().toDateString();
      const todayAdded = mediaData.filter(item => {
        if (!item.createdAt) return false;
        return new Date(item.createdAt).toDateString() === todayStr;
      }).length;

      setStats({
        totalMedia,
        validMedia,
        rejectedMedia,
        expiredMedia,
        pendingMedia,
        activeUsers: userData.length,
        onlineUsers: userData.filter(u => u.isActive).length,
        todayAdded
      });
    } catch (error) {
      console.error('Error fetching stats:', error);

      // التحقق من نوع الخطأ
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Request was aborted due to timeout');
      }

      // بيانات افتراضية في حالة الخطأ
      setStats({
        totalMedia: 0,
        validMedia: 0,
        rejectedMedia: 0,
        expiredMedia: 0,
        pendingMedia: 0,
        activeUsers: 4,
        onlineUsers: 1,
        todayAdded: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const navigationItems = [
    { name: t('navigation.dashboard'), icon: '📊', active: true, path: '/dashboard' },
    { name: t('navigation.mediaList'), icon: '🎬', active: false, path: '/media-list', permission: 'MEDIA_READ' },
    { name: t('navigation.addMedia'), icon: '➕', active: false, path: '/add-media', permission: 'MEDIA_CREATE' },
    { name: t('navigation.weeklySchedule'), icon: '📅', active: false, path: '/weekly-schedule', permission: 'SCHEDULE_READ' },
    { name: t('navigation.adminDashboard'), icon: '👥', active: false, path: '/admin-dashboard', adminOnly: true },
    { name: t('navigation.statistics'), icon: '📈', active: false, path: '/statistics', adminOnly: true },

  ].filter(item => {
    if (item.adminOnly && user?.role !== 'ADMIN') return false;
    if (item.permission && !hasPermission(item.permission)) return false;
    return true;
  });

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: 'rtl'
      }}>
        <div style={{
          textAlign: 'center',
          color: 'white'
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginBottom: '30px'
          }}>
            <Logo size="large" style={{ fontSize: '2rem' }} />
          </div>
          <div style={{ fontSize: '3rem', marginBottom: '20px' }}>⏳</div>
          <div style={{ fontSize: '1.5rem', marginBottom: '10px' }}>{t('common.loadingData')}</div>
          <div style={{ color: '#a0aec0', fontSize: '1rem' }}>{t('messages.pleaseWait')}</div>
        </div>
      </div>
    );
  }

  return (
    <AuthGuard>
      <div style={{
        minHeight: '100vh',
        background: '#1a1d29',
        color: 'white',
        fontFamily: 'Cairo, Arial, sans-serif',
        direction: isRTL ? 'rtl' : 'ltr'
      }}>
        {/* الشريط الجانبي */}
        <Sidebar isOpen={sidebarOpen} onToggle={() => setSidebarOpen(!sidebarOpen)} />
        
        {/* تنبيه المواد المنتهية */}
        {showExpiredAlert && (
          <div style={{
            position: 'fixed',
            top: '20px',
            left: '20px',
            right: '20px',
            zIndex: 1000,
            background: '#ef4444',
            color: 'white',
            padding: '15px 20px',
            borderRadius: '8px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            maxHeight: '300px',
            overflow: 'auto'
          }}>
            <div>
              <div style={{ fontWeight: 'bold', fontSize: '1.1rem', marginBottom: '10px' }}>
                ⚠️ {t('messages.warning')}: {t('stats.expiredMedia')} {expiredMediaItems.length}
              </div>
              <div style={{ fontSize: '0.9rem' }}>
                <ul style={{ paddingRight: '20px', margin: '5px 0' }}>
                  {expiredMediaItems.slice(0, 5).map((item, index) => (
                    <li key={index}>
                      {item.name} - {item.endDate ? `${t('messages.expiredOn')}: ${new Date(item.endDate).toLocaleDateString('ar-EG')}` : t('messages.expired')}
                    </li>
                  ))}
                  {expiredMediaItems.length > 5 && (
                    <li>... {t('messages.andMore')} ({expiredMediaItems.length - 5} {t('messages.otherItems')})</li>
                  )}
                </ul>
              </div>
            </div>
            <button
              onClick={() => {
                setShowExpiredAlert(false);
                setExpiredAlertShown(true);
                // حفظ في localStorage لمنع ظهور التنبيه مرة أخرى اليوم
                const alertKey = `expired_alert_${new Date().toDateString()}`;
                localStorage.setItem(alertKey, 'true');
                console.log('🔕 تم إغلاق تنبيه المواد المنتهية نهائياً لليوم');
              }}
              style={{
                background: 'rgba(255, 255, 255, 0.2)',
                border: 'none',
                color: 'white',
                borderRadius: '4px',
                padding: '5px 10px',
                cursor: 'pointer',
                marginRight: '10px'
              }}
            >
              {t('common.close')}
            </button>
          </div>
        )}

        {/* شريط التنقل العلوي */}
        <div style={{
          background: '#1a1d29',
          padding: '15px 30px',
          borderBottom: '1px solid #2d3748',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          {/* زر القائمة واللوجو */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.5rem',
                cursor: 'pointer',
                padding: '5px'
              }}
            >
              ☰
            </button>
            <Logo size="medium" />
          </div>

          {/* عناصر التنقل */}
          <div style={{ display: 'flex', gap: '5px' }}>
            {topNavigationItems.map((item, index) => (
              <button
                key={index}
                onClick={() => router.push(item.path)}
                style={{
                  background: item.active ? '#4299e1' : 'transparent',
                  color: item.active ? 'white' : '#a0aec0',
                  border: 'none',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  transition: 'all 0.2s'
                }}
                onMouseEnter={(e) => {
                  if (!item.active) {
                    e.target.style.background = '#2d3748';
                    e.target.style.color = 'white';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.active) {
                    e.target.style.background = 'transparent';
                    e.target.style.color = '#a0aec0';
                  }
                }}
              >
                <span>{item.icon}</span>
                {item.name}
              </button>
            ))}
          </div>

          {/* أدوات المستخدم */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
            {/* أيقونة تغيير اللغة */}
            <button
              onClick={() => {
                const newLang = currentLang === 'ar' ? 'en' : 'ar';
                changeLanguage(newLang);
              }}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.2rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '5px'
              }}
              title={currentLang === 'ar' ? 'Switch to English' : 'التبديل للعربية'}
            >
              🌐
              <span style={{ fontSize: '0.8rem' }}>
                {currentLang === 'ar' ? 'EN' : 'عر'}
              </span>
            </button>
            <button
              onClick={logout}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#a0aec0',
                fontSize: '1.2rem',
                cursor: 'pointer'
              }}
              title={t('auth.logout')}
            >
              🚪
            </button>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div style={{
          padding: '30px',
          ...(isRTL ? {
            marginRight: sidebarOpen ? '280px' : '0',
            transition: 'margin-right 0.3s ease'
          } : {
            marginLeft: sidebarOpen ? '280px' : '0',
            transition: 'margin-left 0.3s ease'
          })
        }}>
          {/* رأس الصفحة */}
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-start',
            marginBottom: '30px'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
              <div style={{
                width: '50px',
                height: '50px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem'
              }}>
                📊
              </div>
              <div>
                <h1 style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  margin: '0 0 5px 0',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  {t('dashboard.title')}
                </h1>
                <p style={{
                  color: '#a0aec0',
                  margin: 0,
                  fontSize: '1rem'
                }}>
                  {t('dashboard.subtitle')}
                </p>
                <p style={{
                  color: '#68d391',
                  margin: '5px 0 0 0',
                  fontSize: '0.9rem'
                }}>
                  {t('dashboard.overview')}: {stats.totalMedia} {t('stats.totalMedia')} - {stats.activeUsers} {t('stats.activeUsers')}
                </p>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '20px',
              color: '#a0aec0'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  background: '#68d391',
                  borderRadius: '50%'
                }}></div>
                <span style={{ fontSize: '0.9rem' }}>{t('common.loading')}</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span>🔄</span>
                <span style={{ fontSize: '0.9rem' }}>
                  {t('dashboard.sync')}: {currentTime.toLocaleTimeString('ar-EG')}
                </span>
              </div>
            </div>
          </div>

          {/* بطاقات التنقل - الصف الأول (4 بطاقات) */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '20px',
            marginBottom: '20px'
          }}>
            <NavigationCard
              icon="🎬"
              title={t('navigation.mediaList')}
              subtitle={t('media.list')}
              path="/media-list"
              permission="MEDIA_READ"
              height="280px"
            />

            <NavigationCard
              icon="➕"
              title={t('navigation.addMedia')}
              subtitle={t('media.addNew')}
              path="/add-media"
              permission="MEDIA_CREATE"
              height="280px"
            />

            <NavigationCard
              icon="📅"
              title={t('navigation.weeklySchedule')}
              subtitle={t('schedule.weekly')}
              path="/weekly-schedule"
              permission="SCHEDULE_READ"
              height="280px"
            />

            <NavigationCard
              icon="📊"
              title={t('navigation.dailySchedule')}
              subtitle={t('schedule.daily')}
              path="/daily-schedule"
              permission="SCHEDULE_READ"
              height="280px"
            />
          </div>

          {/* الصف الثاني - 4 بطاقات */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(4, 1fr)',
            gap: '20px',
            marginBottom: '20px'
          }}>
            <NavigationCard
              icon="📋"
              title={t('navigation.reports')}
              subtitle={t('dashboard.recentActivity')}
              path="/reports"
              permission="SCHEDULE_READ"
              height="280px"
            />

            {/* زر الاستيراد/التصدير - للأدمن مدير التحكم فقط */}
            {user?.role === 'ADMIN' && user?.username === 'admin' && (
              <NavigationCard
                icon="📤"
                title={t('navigation.unifiedSystem')}
                subtitle={t('common.import') + '/' + t('common.export')}
                path="/unified-system"
                height="280px"
              />
            )}

            <NavigationCard
              icon="👥"
              title={t('navigation.adminDashboard')}
              subtitle={t('admin.users')}
              path="/admin-dashboard"
              height="280px"
            />

            <NavigationCard
              icon="📈"
              title={t('navigation.statistics')}
              subtitle={t('dashboard.statistics')}
              path="/statistics"
              height="280px"
            />


          </div>
        </div>

        {/* النص السفلي */}
        <div style={{
          position: 'fixed',
          bottom: '20px',
          left: '20px',
          color: '#6c757d',
          fontSize: '0.75rem',
          fontFamily: 'Arial, sans-serif',
          direction: 'ltr'
        }}>
          Powered By Mahmoud Ismail
        </div>
      </div>
    </AuthGuard>
  );
}
