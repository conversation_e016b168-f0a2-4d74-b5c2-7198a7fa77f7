"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/weekly-schedule/page",{

/***/ "(app-pages-browser)/./src/app/weekly-schedule/page.tsx":
/*!******************************************!*\
  !*** ./src/app/weekly-schedule/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WeeklySchedulePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WeeklySchedulePage() {\n    _s();\n    const { isViewer } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [scheduleItems, setScheduleItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [availableMedia, setAvailableMedia] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedWeek, setSelectedWeek] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedType, setSelectedType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const scrollPositionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const shouldRestoreScroll = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [readOnlyMode, setReadOnlyMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // مرجع للجدول لتثبيت موضع التمرير\n    const scheduleTableRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const visibleRowRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const visibleRowIndexRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(-1);\n    // تحديد وضع القراءة فقط للمستخدمين الذين ليس لديهم صلاحيات التعديل\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (isViewer) {\n                setReadOnlyMode(true);\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        isViewer\n    ]);\n    // حالات المواد المؤقتة\n    const [tempMediaName, setTempMediaName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaType, setTempMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('PROGRAM');\n    const [tempMediaDuration, setTempMediaDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('01:00:00');\n    const [tempMediaNotes, setTempMediaNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [tempMediaItems, setTempMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // أيام الأسبوع\n    const days = [\n        t('common.sunday'),\n        t('common.monday'),\n        t('common.tuesday'),\n        t('common.wednesday'),\n        t('common.thursday'),\n        t('common.friday'),\n        t('common.saturday')\n    ];\n    // حساب تواريخ الأسبوع بالأرقام العربية العادية\n    const getWeekDates = ()=>{\n        if (!selectedWeek) return [\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--',\n            '--/--'\n        ];\n        // التأكد من أن selectedWeek يمثل يوم الأحد\n        const inputDate = new Date(selectedWeek + 'T00:00:00'); // استخدام منتصف الليل\n        console.log('📅 التاريخ المدخل:', selectedWeek);\n        console.log('📅 يوم الأسبوع للتاريخ المدخل:', inputDate.getDay(), '(0=أحد)');\n        // التأكد من أن نبدأ من يوم الأحد\n        const sundayDate = new Date(inputDate);\n        const dayOfWeek = inputDate.getDay();\n        if (dayOfWeek !== 0) {\n            // إذا لم يكن الأحد، نحسب الأحد السابق\n            sundayDate.setDate(inputDate.getDate() - dayOfWeek);\n        }\n        console.log('📅 يوم الأحد المحسوب:', sundayDate.toISOString().split('T')[0]);\n        const dates = [];\n        for(let i = 0; i < 7; i++){\n            // إنشاء تاريخ جديد لكل يوم بدءاً من الأحد\n            const currentDate = new Date(sundayDate);\n            currentDate.setDate(sundayDate.getDate() + i);\n            // استخدام التنسيق العربي للتاريخ (يوم/شهر/سنة)\n            const day = currentDate.getDate();\n            const month = currentDate.getMonth() + 1; // الشهور تبدأ من 0\n            const year = currentDate.getFullYear();\n            // تنسيق التاريخ بالشكل dd/mm/yyyy\n            const dateStr = \"\".concat(day.toString().padStart(2, '0'), \"/\").concat(month.toString().padStart(2, '0'), \"/\").concat(year);\n            dates.push(dateStr);\n            // التحقق من صحة اليوم\n            const actualDayOfWeek = currentDate.getDay();\n            console.log(\"  يوم \".concat(i, \" (\").concat(days[i], \"): \").concat(currentDate.toISOString().split('T')[0], \" → \").concat(dateStr, \" [يوم الأسبوع: \").concat(actualDayOfWeek, \"]\"));\n            // تحذير إذا كان اليوم لا يتطابق مع المتوقع\n            if (actualDayOfWeek !== i) {\n                console.warn(\"⚠️ عدم تطابق: متوقع يوم \".concat(i, \" لكن حصلنا على \").concat(actualDayOfWeek));\n            }\n        }\n        return dates;\n    };\n    // استخدام useEffect لتحديث التواريخ عند تغيير الأسبوع المحدد\n    const [weekDates, setWeekDates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--',\n        '--/--'\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            const dates = getWeekDates();\n            console.log('🔄 تحديث التواريخ:', dates);\n            setWeekDates(dates);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // الساعات من 08:00 إلى 07:00 (24 ساعة)\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>{\n        const hour = (i + 8) % 24;\n        return \"\".concat(hour.toString().padStart(2, '0'), \":00\");\n    });\n    // حساب المدة الإجمالية للمادة\n    const calculateTotalDuration = (segments)=>{\n        if (!segments || segments.length === 0) return '01:00:00';\n        let totalSeconds = 0;\n        segments.forEach((segment)=>{\n            const [hours, minutes, seconds] = segment.duration.split(':').map(Number);\n            totalSeconds += hours * 3600 + minutes * 60 + seconds;\n        });\n        const hours_calc = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const secs = totalSeconds % 60;\n        return \"\".concat(hours_calc.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(secs.toString().padStart(2, '0'));\n    };\n    // إنشاء نص عرض المادة مع التفاصيل\n    const getMediaDisplayText = (item)=>{\n        let displayText = item.name || t('schedule.unknown');\n        // إضافة تفاصيل الحلقات والأجزاء\n        if (item.type === 'SERIES') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.season'), \" \").concat(item.seasonNumber, \" \").concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            }\n        } else if (item.type === 'PROGRAM') {\n            if (item.seasonNumber && item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.season'), \" \").concat(item.seasonNumber, \" \").concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            } else if (item.episodeNumber) {\n                displayText += \" - \".concat(t('schedule.episode'), \" \").concat(item.episodeNumber);\n            }\n        } else if (item.type === 'MOVIE' && item.partNumber) {\n            displayText += \" - \".concat(t('schedule.part'), \" \").concat(item.partNumber);\n        }\n        return displayText;\n    };\n    // تحديد الأسبوع الحالي\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            // استخدام التاريخ المحلي مع تجنب مشاكل المنطقة الزمنية\n            const today = new Date();\n            // تحويل إلى تاريخ محلي بدون وقت\n            const year = today.getFullYear();\n            const month = today.getMonth();\n            const day = today.getDate();\n            const localDate = new Date(year, month, day);\n            // إضافة تسجيل للتحقق\n            console.log('🔍 حساب الأسبوع الحالي:');\n            console.log('  📅 اليوم الفعلي:', \"\".concat(year, \"-\").concat((month + 1).toString().padStart(2, '0'), \"-\").concat(day.toString().padStart(2, '0')));\n            console.log('  📊 يوم الأسبوع:', localDate.getDay(), '(0=أحد)');\n            // حساب يوم الأحد لهذا الأسبوع\n            const sunday = new Date(localDate);\n            sunday.setDate(localDate.getDate() - localDate.getDay());\n            // تحويل إلى string بطريقة محلية\n            const weekStart = \"\".concat(sunday.getFullYear(), \"-\").concat((sunday.getMonth() + 1).toString().padStart(2, '0'), \"-\").concat(sunday.getDate().toString().padStart(2, '0'));\n            console.log('  📅 بداية الأسبوع المحسوبة:', weekStart);\n            console.log('  📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');\n            setSelectedWeek(weekStart);\n        }\n    }[\"WeeklySchedulePage.useEffect\"], []);\n    // جلب البيانات\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WeeklySchedulePage.useEffect\": ()=>{\n            if (selectedWeek) {\n                fetchScheduleData();\n            }\n        }\n    }[\"WeeklySchedulePage.useEffect\"], [\n        selectedWeek\n    ]);\n    // حفظ الصف المرئي قبل تحديث البيانات\n    const saveVisibleRowIndex = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\": ()=>{\n            if (!scheduleTableRef.current) {\n                console.log('⚠️ لم يتم العثور على مرجع الجدول');\n                return;\n            }\n            // الحصول على جميع صفوف الساعات في الجدول\n            const hourRows = scheduleTableRef.current.querySelectorAll('.hour-row');\n            if (!hourRows.length) {\n                console.log('⚠️ لم يتم العثور على صفوف الساعات');\n                return;\n            }\n            console.log(\"\\uD83D\\uDD0D تم العثور على \".concat(hourRows.length, \" صف ساعة\"));\n            // تحديد الصف المرئي حاليًا في منتصف الشاشة\n            const viewportHeight = window.innerHeight;\n            const viewportMiddle = window.scrollY + viewportHeight / 2;\n            console.log(\"\\uD83D\\uDCCF منتصف الشاشة: \".concat(viewportMiddle, \"px (ارتفاع الشاشة: \").concat(viewportHeight, \"px, موضع التمرير: \").concat(window.scrollY, \"px)\"));\n            let closestRow = null;\n            let closestDistance = Infinity;\n            let closestIndex = -1;\n            // البحث عن أقرب صف للمنتصف\n            hourRows.forEach({\n                \"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\": (row, index)=>{\n                    const rect = row.getBoundingClientRect();\n                    const rowTop = window.scrollY + rect.top;\n                    const rowBottom = rowTop + rect.height;\n                    const rowMiddle = rowTop + rect.height / 2;\n                    const distance = Math.abs(viewportMiddle - rowMiddle);\n                    console.log(\"  صف \".concat(index, \" (\").concat(hours[index], \"): العلوي=\").concat(rect.top.toFixed(0), \", الارتفاع=\").concat(rect.height.toFixed(0), \", المسافة=\").concat(distance.toFixed(0)));\n                    if (distance < closestDistance) {\n                        closestDistance = distance;\n                        closestRow = row;\n                        closestIndex = index;\n                    }\n                }\n            }[\"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\"]);\n            if (closestRow) {\n                visibleRowRef.current = closestRow;\n                visibleRowIndexRef.current = closestIndex;\n                console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي: الساعة \".concat(hours[closestIndex], \", الفهرس \").concat(closestIndex, \", المسافة=\").concat(closestDistance.toFixed(0), \"px\"));\n            } else {\n                console.log('⚠️ لم يتم العثور على صف مرئي');\n            }\n        }\n    }[\"WeeklySchedulePage.useCallback[saveVisibleRowIndex]\"], [\n        hours\n    ]);\n    // استعادة موضع التمرير بعد تحديث البيانات - نسخة مبسطة وأكثر فعالية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)({\n        \"WeeklySchedulePage.useLayoutEffect\": ()=>{\n            if (shouldRestoreScroll.current && scrollPositionRef.current !== undefined) {\n                console.log(\"\\uD83D\\uDD04 استعادة موضع التمرير: \".concat(scrollPositionRef.current, \"px\"));\n                // تأخير للتأكد من اكتمال الرندر\n                const timer = setTimeout({\n                    \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                        const targetPosition = scrollPositionRef.current;\n                        // استعادة الموضع مباشرة\n                        window.scrollTo({\n                            top: targetPosition,\n                            behavior: 'instant'\n                        });\n                        console.log(\"\\uD83D\\uDCCD تم استعادة الموضع إلى: \".concat(targetPosition, \"px\"));\n                        // التحقق من نجاح العملية والتصحيح إذا لزم الأمر\n                        setTimeout({\n                            \"WeeklySchedulePage.useLayoutEffect.timer\": ()=>{\n                                const currentPosition = window.scrollY;\n                                const difference = Math.abs(currentPosition - targetPosition);\n                                if (difference > 5) {\n                                    console.log(\"\\uD83D\\uDCCD تصحيح الموضع: \".concat(currentPosition, \"px → \").concat(targetPosition, \"px (فرق: \").concat(difference, \"px)\"));\n                                    window.scrollTo({\n                                        top: targetPosition,\n                                        behavior: 'instant'\n                                    });\n                                } else {\n                                    console.log(\"✅ تم تثبيت موضع التمرير بنجاح\");\n                                }\n                            }\n                        }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 100);\n                        shouldRestoreScroll.current = false;\n                    }\n                }[\"WeeklySchedulePage.useLayoutEffect.timer\"], 200);\n                return ({\n                    \"WeeklySchedulePage.useLayoutEffect\": ()=>clearTimeout(timer)\n                })[\"WeeklySchedulePage.useLayoutEffect\"];\n            }\n        }\n    }[\"WeeklySchedulePage.useLayoutEffect\"], [\n        scheduleItems\n    ]);\n    const fetchScheduleData = async ()=>{\n        try {\n            setLoading(true);\n            console.log('🔄 بدء تحديث البيانات...');\n            // التأكد من حفظ موضع التمرير إذا لم يكن محفوظاً\n            if (!shouldRestoreScroll.current) {\n                const currentScrollPosition = window.scrollY;\n                scrollPositionRef.current = currentScrollPosition;\n                shouldRestoreScroll.current = true;\n                console.log(\"\\uD83D\\uDCCD حفظ تلقائي لموضع التمرير: \".concat(currentScrollPosition, \"px\"));\n            }\n            console.log('🌐 جلب البيانات من API (يتضمن المواد المؤقتة والإعادات)');\n            const url = \"/api/weekly-schedule?weekStart=\".concat(selectedWeek);\n            console.log('🌐 إرسال طلب إلى:', url);\n            const response = await fetch(url);\n            console.log('📡 تم استلام الاستجابة:', response.status);\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const result = await response.json();\n            console.log('📊 تم تحليل البيانات:', result.success);\n            if (result.success) {\n                // API يُرجع جميع البيانات (عادية + مؤقتة + إعادات)\n                const allItems = result.data.scheduleItems || [];\n                const apiTempItems = result.data.tempItems || [];\n                // تحديث المواد المؤقتة في القائمة الجانبية\n                console.log('📦 المواد المؤقتة الواردة من API:', apiTempItems.map((item)=>({\n                        id: item.id,\n                        name: item.name\n                    })));\n                setTempMediaItems(apiTempItems);\n                setScheduleItems(allItems);\n                setAvailableMedia(result.data.availableMedia || []);\n                const regularItems = allItems.filter((item)=>!item.isTemporary && !item.isRerun);\n                const tempItems = allItems.filter((item)=>item.isTemporary && !item.isRerun);\n                const reruns = allItems.filter((item)=>item.isRerun);\n                console.log(\"\\uD83D\\uDCCA تم تحديث الجدول: \".concat(regularItems.length, \" مادة عادية + \").concat(tempItems.length, \" مؤقتة + \").concat(reruns.length, \" إعادة = \").concat(allItems.length, \" إجمالي\"));\n                console.log(\"\\uD83D\\uDCE6 تم تحديث القائمة الجانبية: \".concat(apiTempItems.length, \" مادة مؤقتة\"));\n            } else {\n                console.error('❌ خطأ في الاستجابة:', result.error);\n                alert(\"خطأ في تحديث البيانات: \".concat(result.error));\n                // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n                setScheduleItems(currentTempItems);\n                setAvailableMedia([]);\n            }\n        } catch (error) {\n            console.error('❌ خطأ في جلب البيانات:', error);\n            alert(\"خطأ في الاتصال: \".concat(error.message));\n            // الحفاظ على المواد المؤقتة حتى في حالة الخطأ\n            const currentTempItemsError = scheduleItems.filter((item)=>item.isTemporary);\n            setScheduleItems(currentTempItemsError);\n            setAvailableMedia([]);\n        } finally{\n            console.log('✅ انتهاء تحديث البيانات');\n            setLoading(false);\n        }\n    };\n    // إضافة مادة مؤقتة\n    const addTempMedia = async ()=>{\n        if (!tempMediaName.trim()) {\n            alert(t('schedule.enterMediaName'));\n            return;\n        }\n        const newTempMedia = {\n            id: \"temp_\".concat(Date.now()),\n            name: tempMediaName.trim(),\n            type: tempMediaType,\n            duration: tempMediaDuration,\n            description: tempMediaNotes.trim() || undefined,\n            isTemporary: true,\n            temporaryType: tempMediaType === 'LIVE' ? 'برنامج هواء مباشر' : tempMediaType === 'PENDING' ? 'مادة قيد التسليم' : 'مادة مؤقتة'\n        };\n        try {\n            // حفظ المادة المؤقتة في القائمة الجانبية عبر API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'saveTempToSidebar',\n                    tempMedia: newTempMedia,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>[\n                        ...prev,\n                        newTempMedia\n                    ]);\n                console.log('✅ تم حفظ المادة المؤقتة في القائمة الجانبية');\n            } else {\n                alert(result.error || t('messages.saveFailed'));\n                return;\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حفظ المادة المؤقتة:', error);\n            alert(t('messages.saveFailed'));\n            return;\n        }\n        // إعادة تعيين النموذج\n        setTempMediaName('');\n        setTempMediaNotes('');\n        setTempMediaDuration('01:00:00');\n        console.log('✅ تم إضافة مادة مؤقتة:', newTempMedia.name);\n    };\n    // حذف مادة مؤقتة من القائمة الجانبية\n    const deleteTempMedia = async (tempMediaId)=>{\n        if (!confirm(t('schedule.confirmDeleteTemp'))) {\n            return;\n        }\n        try {\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    action: 'deleteTempFromSidebar',\n                    tempMediaId,\n                    weekStart: selectedWeek\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setTempMediaItems((prev)=>prev.filter((item)=>item.id !== tempMediaId));\n                console.log('✅ تم حذف المادة المؤقتة من القائمة الجانبية');\n            } else {\n                alert(result.error || t('messages.deleteFailed'));\n            }\n        } catch (error) {\n            console.error('❌ خطأ في حذف المادة المؤقتة:', error);\n            alert(t('messages.deleteFailed'));\n        }\n    };\n    // حذف مادة مؤقتة\n    const removeTempMedia = (id)=>{\n        setTempMediaItems((prev)=>prev.filter((item)=>item.id !== id));\n    };\n    // دمج المواد العادية والمؤقتة\n    const allAvailableMedia = [\n        ...availableMedia,\n        ...tempMediaItems\n    ];\n    // فلترة المواد حسب النوع والبحث\n    const filteredMedia = allAvailableMedia.filter((item)=>{\n        const matchesType = selectedType === '' || item.type === selectedType;\n        const itemName = item.name || '';\n        const itemType = item.type || '';\n        const matchesSearch = itemName.toLowerCase().includes(searchTerm.toLowerCase()) || itemType.toLowerCase().includes(searchTerm.toLowerCase());\n        return matchesType && matchesSearch;\n    });\n    // التحقق من البرايم تايم\n    const isPrimeTimeSlot = (dayOfWeek, timeStr)=>{\n        const hour = parseInt(timeStr.split(':')[0]);\n        // الأحد-الأربعاء: 18:00-23:59\n        if (dayOfWeek >= 0 && dayOfWeek <= 3) {\n            return hour >= 18;\n        }\n        // الخميس-السبت: 18:00-23:59 أو 00:00-01:59\n        if (dayOfWeek >= 4 && dayOfWeek <= 6) {\n            return hour >= 18 || hour < 2;\n        }\n        return false;\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalRerunsWithItems = (tempItems, checkItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateLocalReruns = (tempItems)=>{\n        console.log('⚠️ دالة توليد الإعادات المحلية تم إيقافها نهائياً');\n        return [];\n    };\n    // دالة توليد الإعادات تم إيقافها نهائياً\n    const generateTempReruns = (originalItem)=>{\n        console.log('⚠️ دالة توليد الإعادات المؤقتة تم إيقافها نهائياً');\n        return [];\n    };\n    // التحقق من التداخل في الأوقات\n    const checkTimeConflict = (newItem, existingItems)=>{\n        try {\n            const newStart = parseInt(newItem.startTime.split(':')[0]) * 60 + parseInt(newItem.startTime.split(':')[1] || '0');\n            const newEnd = parseInt(newItem.endTime.split(':')[0]) * 60 + parseInt(newItem.endTime.split(':')[1] || '0');\n            const conflict = existingItems.some((item)=>{\n                if (item.dayOfWeek !== newItem.dayOfWeek) return false;\n                const itemStart = parseInt(item.startTime.split(':')[0]) * 60 + parseInt(item.startTime.split(':')[1] || '0');\n                const itemEnd = parseInt(item.endTime.split(':')[0]) * 60 + parseInt(item.endTime.split(':')[1] || '0');\n                return newStart < itemEnd && newEnd > itemStart;\n            });\n            if (conflict) {\n                console.log('⚠️ تم اكتشاف تداخل:', newItem);\n            }\n            return conflict;\n        } catch (error) {\n            console.error('خطأ في فحص التداخل:', error);\n            return false; // في حالة الخطأ، اسمح بالإضافة\n        }\n    };\n    // إضافة مادة للجدول\n    const addItemToSchedule = async (mediaItem, dayOfWeek, hour)=>{\n        try {\n            // حفظ موضع التمرير الحالي بدقة\n            const currentScrollPosition = window.scrollY;\n            scrollPositionRef.current = currentScrollPosition;\n            // حفظ الصف المرئي قبل التحديث\n            saveVisibleRowIndex();\n            shouldRestoreScroll.current = true;\n            console.log(\"\\uD83D\\uDCCD تم حفظ موضع التمرير: \".concat(currentScrollPosition, \"px والصف المرئي عند إضافة مادة\"));\n            console.log('🎯 محاولة إضافة مادة:', {\n                name: mediaItem.name,\n                isTemporary: mediaItem.isTemporary,\n                dayOfWeek,\n                hour,\n                scrollPosition: scrollPositionRef.current\n            });\n            const startTime = hour;\n            const endTime = \"\".concat((parseInt(hour.split(':')[0]) + 1).toString().padStart(2, '0'), \":00\");\n            // التحقق من التداخل\n            const newItem = {\n                dayOfWeek,\n                startTime,\n                endTime\n            };\n            if (checkTimeConflict(newItem, scheduleItems)) {\n                alert('⚠️ ' + t('schedule.timeConflict'));\n                console.log('❌ تم منع الإضافة بسبب التداخل');\n                return;\n            }\n            // التحقق من المواد المؤقتة\n            if (mediaItem.isTemporary) {\n                console.log('🟣 نقل مادة مؤقتة من القائمة الجانبية إلى الجدول...');\n                console.log('📋 بيانات المادة:', {\n                    id: mediaItem.id,\n                    name: mediaItem.name,\n                    type: mediaItem.type,\n                    duration: mediaItem.duration,\n                    fullItem: mediaItem\n                });\n                // التحقق من صحة البيانات\n                if (!mediaItem.name || mediaItem.name === 'undefined') {\n                    console.error('❌ بيانات المادة المؤقتة غير صحيحة:', mediaItem);\n                    alert('خطأ: بيانات المادة غير صحيحة. يرجى المحاولة مرة أخرى.');\n                    shouldRestoreScroll.current = false;\n                    return;\n                }\n                // حذف المادة من القائمة الجانبية محلياً أولاً\n                setTempMediaItems((prev)=>{\n                    const filtered = prev.filter((item)=>item.id !== mediaItem.id);\n                    console.log('🗑️ حذف المادة محلياً من القائمة الجانبية:', mediaItem.name);\n                    console.log('📊 المواد المتبقية في القائمة:', filtered.length);\n                    return filtered;\n                });\n                // التأكد من صحة البيانات قبل الإرسال\n                const cleanMediaItem = {\n                    ...mediaItem,\n                    name: mediaItem.name || mediaItem.title || 'مادة مؤقتة',\n                    id: mediaItem.id || \"temp_\".concat(Date.now()),\n                    type: mediaItem.type || 'PROGRAM',\n                    duration: mediaItem.duration || '01:00:00'\n                };\n                console.log('📤 إرسال البيانات المنظفة:', cleanMediaItem);\n                // إرسال المادة المؤقتة إلى API\n                const response = await fetch('/api/weekly-schedule', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        mediaItemId: cleanMediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: cleanMediaItem\n                    })\n                });\n                const result = await response.json();\n                if (result.success) {\n                    console.log('✅ تم نقل المادة المؤقتة إلى الجدول');\n                    // حذف المادة من القائمة الجانبية في الخادم بعد النجاح\n                    try {\n                        await fetch('/api/weekly-schedule', {\n                            method: 'PATCH',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                action: 'deleteTempFromSidebar',\n                                tempMediaId: mediaItem.id,\n                                weekStart: selectedWeek\n                            })\n                        });\n                        console.log('🗑️ تم حذف المادة من القائمة الجانبية في الخادم');\n                    } catch (error) {\n                        console.warn('⚠️ خطأ في حذف المادة من القائمة الجانبية:', error);\n                    }\n                    // تحديث الجدول محلياً بدلاً من إعادة التحميل الكامل\n                    const newScheduleItem = {\n                        id: result.data.id,\n                        mediaItemId: mediaItem.id,\n                        dayOfWeek,\n                        startTime,\n                        endTime,\n                        weekStart: selectedWeek,\n                        isTemporary: true,\n                        mediaItem: mediaItem\n                    };\n                    setScheduleItems((prev)=>[\n                            ...prev,\n                            newScheduleItem\n                        ]);\n                    console.log('✅ تم إضافة المادة للجدول محلياً');\n                } else {\n                    // في حالة الفشل، أعد المادة للقائمة الجانبية\n                    setTempMediaItems((prev)=>[\n                            ...prev,\n                            mediaItem\n                        ]);\n                    alert(result.error);\n                    shouldRestoreScroll.current = false; // إلغاء استعادة التمرير في حالة الخطأ\n                }\n                return;\n            }\n            // للمواد العادية - استخدام API\n            const response = await fetch('/api/weekly-schedule', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime,\n                    endTime,\n                    weekStart: selectedWeek,\n                    // إرسال تفاصيل الحلقة/الجزء إذا كانت موجودة\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                // تحديث محلي بدلاً من إعادة تحميل كامل\n                const newScheduleItem = {\n                    id: result.data.id,\n                    mediaItemId: mediaItem.id,\n                    dayOfWeek,\n                    startTime: startTime,\n                    endTime: endTime,\n                    weekStart: selectedWeek,\n                    mediaItem: mediaItem,\n                    isTemporary: mediaItem.isTemporary || false,\n                    episodeNumber: mediaItem.episodeNumber,\n                    seasonNumber: mediaItem.seasonNumber,\n                    partNumber: mediaItem.partNumber\n                };\n                // إضافة المادة للجدول محلياً\n                setScheduleItems((prev)=>[\n                        ...prev,\n                        newScheduleItem\n                    ]);\n                console.log('✅ تم إضافة المادة للجدول محلياً بدون إعادة تحميل');\n            } else {\n                alert(result.error);\n            }\n        } catch (error) {\n            console.error('خطأ في إضافة المادة:', error);\n        }\n    };\n    // حذف مادة مع تأكيد\n    const deleteItem = async (item)=>{\n        var _item_mediaItem;\n        const itemName = ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.name) || t('schedule.unknown');\n        const itemType = item.isRerun ? t('schedule.rerunMaterial') : item.isTemporary ? t('schedule.tempMaterial') : t('schedule.originalMaterial');\n        const warningMessage = item.isRerun ? t('schedule.deleteWarningRerun') : item.isTemporary ? t('schedule.deleteWarningTemp') : t('schedule.deleteWarningOriginal');\n        const confirmed = window.confirm(\"\".concat(t('schedule.confirmDelete', {\n            type: itemType,\n            name: itemName\n        }), \"\\n\\n\") + \"\".concat(t('schedule.time'), \": \").concat(item.startTime, \" - \").concat(item.endTime, \"\\n\") + warningMessage);\n        if (!confirmed) return;\n        try {\n            // للمواد المؤقتة - حذف من الخادم أيضًا\n            if (item.isTemporary) {\n                console.log(\"\\uD83D\\uDDD1️ بدء حذف المادة المؤقتة: \".concat(itemName, \" (\").concat(item.id, \")\"));\n                try {\n                    var _item_mediaItem1, _item_mediaItem2;\n                    // حذف المادة المؤقتة من الخادم\n                    console.log(\"\\uD83D\\uDD0D إرسال طلب حذف المادة المؤقتة: \".concat(item.id, \" (\").concat(itemName, \")\"));\n                    console.log(\"\\uD83D\\uDCCA بيانات المادة المؤقتة:\", {\n                        id: item.id,\n                        mediaItemId: item.mediaItemId,\n                        dayOfWeek: item.dayOfWeek,\n                        startTime: item.startTime,\n                        endTime: item.endTime,\n                        isTemporary: item.isTemporary,\n                        isRerun: item.isRerun,\n                        mediaItemName: (_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.name\n                    });\n                    const response = await fetch('/api/weekly-schedule', {\n                        method: 'PATCH',\n                        headers: {\n                            'Content-Type': 'application/json'\n                        },\n                        body: JSON.stringify({\n                            action: 'deleteTempItem',\n                            tempItemId: item.id,\n                            mediaItemId: item.mediaItemId,\n                            dayOfWeek: item.dayOfWeek,\n                            startTime: item.startTime,\n                            endTime: item.endTime,\n                            weekStart: selectedWeek,\n                            mediaItemName: (_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.name\n                        })\n                    });\n                    const result = await response.json();\n                    if (result.success) {\n                        console.log(\"✅ تم حذف المادة المؤقتة من الخادم: \".concat(itemName));\n                        // حذف محلي بعد نجاح الحذف من الخادم\n                        if (item.isRerun) {\n                            // حذف إعادة مؤقتة فقط\n                            setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id));\n                            console.log(\"✅ تم حذف إعادة مؤقتة محليًا: \".concat(itemName));\n                        } else {\n                            // حذف المادة الأصلية وجميع إعاداتها المؤقتة\n                            setScheduleItems((prev)=>prev.filter((scheduleItem)=>scheduleItem.id !== item.id && !(scheduleItem.isRerun && scheduleItem.originalId === item.id)));\n                            console.log(\"✅ تم حذف المادة المؤقتة وإعاداتها محليًا: \".concat(itemName));\n                        }\n                        // إعادة تحميل البيانات للتأكد من التزامن\n                        // حفظ الصف المرئي قبل إعادة التحميل\n                        saveVisibleRowIndex();\n                        shouldRestoreScroll.current = true;\n                        console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي قبل إعادة تحميل البيانات\");\n                        await fetchScheduleData();\n                    } else {\n                        console.error(\"❌ فشل حذف المادة المؤقتة من الخادم: \".concat(result.error));\n                        alert(\"فشل حذف المادة المؤقتة: \".concat(result.error));\n                    }\n                } catch (error) {\n                    console.error(\"❌ خطأ أثناء حذف المادة المؤقتة: \".concat(error));\n                    alert('حدث خطأ أثناء حذف المادة المؤقتة');\n                }\n                return;\n            }\n            // حفظ الصف المرئي قبل التحديث\n            saveVisibleRowIndex();\n            shouldRestoreScroll.current = true;\n            console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي عند حذف مادة\");\n            // للمواد العادية - استخدام API\n            const response = await fetch(\"/api/weekly-schedule?id=\".concat(item.id, \"&weekStart=\").concat(selectedWeek), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                // حفظ الصف المرئي قبل إعادة التحميل\n                saveVisibleRowIndex();\n                shouldRestoreScroll.current = true;\n                console.log(\"\\uD83D\\uDCCD تم حفظ الصف المرئي قبل إعادة تحميل البيانات بعد الحذف\");\n                await fetchScheduleData();\n                console.log(\"✅ تم حذف \".concat(itemType, \": \").concat(itemName));\n            } else {\n                const result = await response.json();\n                alert(\"خطأ في الحذف: \".concat(result.error));\n            }\n        } catch (error) {\n            console.error('خطأ في حذف المادة:', error);\n            alert('حدث خطأ أثناء حذف المادة');\n        }\n    };\n    // الحصول على المواد في خلية معينة\n    const getItemsForCell = (dayOfWeek, hour)=>{\n        return scheduleItems.filter((item)=>item.dayOfWeek === dayOfWeek && item.startTime <= hour && item.endTime > hour);\n    };\n    // معالجة السحب والإفلات\n    const handleDragStart = (e, mediaItem)=>{\n        console.log('🖱️ بدء السحب:', {\n            id: mediaItem.id,\n            name: mediaItem.name,\n            isTemporary: mediaItem.isTemporary,\n            type: mediaItem.type,\n            duration: mediaItem.duration,\n            fullItem: mediaItem\n        });\n        // التأكد من أن جميع البيانات موجودة\n        const itemToSet = {\n            ...mediaItem,\n            name: mediaItem.name || mediaItem.title || 'مادة غير معروفة',\n            id: mediaItem.id || \"temp_\".concat(Date.now())\n        };\n        console.log('📦 المادة المحفوظة للسحب:', itemToSet);\n        setDraggedItem(itemToSet);\n    };\n    // سحب مادة من الجدول نفسه (نسخ)\n    const handleScheduleItemDragStart = (e, scheduleItem)=>{\n        var _scheduleItem_mediaItem, _scheduleItem_mediaItem1, _scheduleItem_mediaItem2, _scheduleItem_mediaItem3;\n        // إنشاء نسخة من المادة للسحب مع الاحتفاظ بتفاصيل الحلقة/الجزء\n        const itemToCopy = {\n            ...scheduleItem.mediaItem,\n            // الاحتفاظ بتفاصيل الحلقة/الجزء من العنصر المجدول\n            episodeNumber: scheduleItem.episodeNumber || ((_scheduleItem_mediaItem = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem === void 0 ? void 0 : _scheduleItem_mediaItem.episodeNumber),\n            seasonNumber: scheduleItem.seasonNumber || ((_scheduleItem_mediaItem1 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem1 === void 0 ? void 0 : _scheduleItem_mediaItem1.seasonNumber),\n            partNumber: scheduleItem.partNumber || ((_scheduleItem_mediaItem2 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem2 === void 0 ? void 0 : _scheduleItem_mediaItem2.partNumber),\n            isFromSchedule: true,\n            originalScheduleItem: scheduleItem\n        };\n        setDraggedItem(itemToCopy);\n        console.log('🔄 سحب مادة من الجدول:', (_scheduleItem_mediaItem3 = scheduleItem.mediaItem) === null || _scheduleItem_mediaItem3 === void 0 ? void 0 : _scheduleItem_mediaItem3.name);\n    };\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n    };\n    const handleDrop = (e, dayOfWeek, hour)=>{\n        e.preventDefault();\n        console.log('📍 إفلات في:', {\n            dayOfWeek,\n            hour\n        });\n        if (draggedItem) {\n            console.log('📦 المادة المسحوبة:', {\n                id: draggedItem.id,\n                name: draggedItem.name,\n                isTemporary: draggedItem.isTemporary,\n                type: draggedItem.type,\n                fullItem: draggedItem\n            });\n            // التأكد من أن البيانات سليمة قبل الإرسال\n            if (!draggedItem.name || draggedItem.name === 'undefined') {\n                console.error('⚠️ اسم المادة غير صحيح:', draggedItem);\n                alert('خطأ: اسم المادة غير صحيح. يرجى المحاولة مرة أخرى.');\n                setDraggedItem(null);\n                return;\n            }\n            addItemToSchedule(draggedItem, dayOfWeek, hour);\n            setDraggedItem(null);\n        } else {\n            console.log('❌ لا توجد مادة مسحوبة');\n        }\n    };\n    // تغيير الأسبوع\n    const changeWeek = (direction)=>{\n        if (!selectedWeek) return;\n        const currentDate = new Date(selectedWeek + 'T00:00:00');\n        console.log('📅 تغيير الأسبوع - البداية:', {\n            direction: direction > 0 ? 'التالي' : 'السابق',\n            currentWeek: selectedWeek,\n            currentDayOfWeek: currentDate.getDay()\n        });\n        // إضافة أو طرح 7 أيام\n        currentDate.setDate(currentDate.getDate() + direction * 7);\n        const newWeekStart = currentDate.toISOString().split('T')[0];\n        console.log('📅 تغيير الأسبوع - النتيجة:', {\n            newWeek: newWeekStart,\n            newDayOfWeek: currentDate.getDay()\n        });\n        setSelectedWeek(newWeekStart);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"⏳ \",\n                    t('schedule.loadingSchedule')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 955,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 954,\n            columnNumber: 7\n        }, this);\n    }\n    // إذا لم يتم تحديد الأسبوع بعد\n    if (!selectedWeek) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: 'center',\n                padding: '50px'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    fontSize: '1.5rem'\n                },\n                children: [\n                    \"\\uD83D\\uDCC5 \",\n                    t('schedule.selectingDate')\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 964,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 963,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.AuthGuard, {\n        requiredPermissions: [\n            'SCHEDULE_READ'\n        ],\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            title: t('schedule.weeklySchedule'),\n            subtitle: t('schedule.weeklySubtitle'),\n            icon: \"\\uD83D\\uDCC5\",\n            fullWidth: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    height: 'calc(100vh - 120px)',\n                    fontFamily: 'Arial, sans-serif',\n                    direction: isRTL ? 'rtl' : 'ltr',\n                    gap: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            width: '320px',\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            border: '1px solid #6b7280',\n                            padding: '20px',\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                style: {\n                                    margin: '0 0 15px 0',\n                                    color: '#f3f4f6'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDA \",\n                                    t('schedule.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 988,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#1f2937',\n                                    border: '2px solid #f59e0b',\n                                    borderRadius: '8px',\n                                    padding: '12px',\n                                    marginBottom: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            margin: '0 0 10px 0',\n                                            color: '#fbbf24',\n                                            fontSize: '0.9rem'\n                                        },\n                                        children: [\n                                            \"⚡ \",\n                                            t('schedule.addTempMedia')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.mediaName'),\n                                        value: tempMediaName,\n                                        onChange: (e)=>setTempMediaName(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1002,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: tempMediaType,\n                                        onChange: (e)=>setTempMediaType(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROGRAM\",\n                                                children: [\n                                                    \"\\uD83D\\uDCFB \",\n                                                    tMediaType('PROGRAM')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"SERIES\",\n                                                children: [\n                                                    \"\\uD83D\\uDCFA \",\n                                                    tMediaType('SERIES')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1034,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"FILM\",\n                                                children: [\n                                                    \"\\uD83C\\uDFA5 \",\n                                                    tMediaType('FILM')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1035,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"FILLER\",\n                                                children: [\n                                                    \"⏸️ \",\n                                                    tMediaType('FILLER')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1036,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"STING\",\n                                                children: [\n                                                    \"⚡ \",\n                                                    tMediaType('STING')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1037,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PROMO\",\n                                                children: [\n                                                    \"\\uD83D\\uDCE2 \",\n                                                    tMediaType('PROMO')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1038,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"NEXT\",\n                                                children: [\n                                                    \"▶️ \",\n                                                    tMediaType('NEXT')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"NOW\",\n                                                children: [\n                                                    \"\\uD83D\\uDD34 \",\n                                                    tMediaType('NOW')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1040,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"سنعود\",\n                                                children: [\n                                                    \"⏰ \",\n                                                    tMediaType('سنعود')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1041,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"عدنا\",\n                                                children: [\n                                                    \"✅ \",\n                                                    tMediaType('عدنا')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1042,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"MINI\",\n                                                children: [\n                                                    \"\\uD83D\\uDD38 \",\n                                                    tMediaType('MINI')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1043,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"CROSS\",\n                                                children: [\n                                                    \"✖️ \",\n                                                    tMediaType('CROSS')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1044,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"LIVE\",\n                                                children: [\n                                                    \"\\uD83D\\uDD34 \",\n                                                    t('schedule.liveProgram')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"PENDING\",\n                                                children: [\n                                                    \"\\uD83D\\uDFE1 \",\n                                                    t('schedule.pendingDelivery')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1019,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.duration'),\n                                        value: tempMediaDuration,\n                                        onChange: (e)=>setTempMediaDuration(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '8px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1049,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        placeholder: t('schedule.notes'),\n                                        value: tempMediaNotes,\n                                        onChange: (e)=>setTempMediaNotes(e.target.value),\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            border: '1px solid #6b7280',\n                                            borderRadius: '4px',\n                                            marginBottom: '10px',\n                                            fontSize: '13px',\n                                            color: '#333',\n                                            background: 'white'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1066,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: addTempMedia,\n                                        style: {\n                                            width: '100%',\n                                            padding: '8px',\n                                            background: '#ff9800',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '13px',\n                                            fontWeight: 'bold',\n                                            cursor: 'pointer',\n                                            marginBottom: '8px'\n                                        },\n                                        children: [\n                                            \"➕ \",\n                                            t('schedule.add')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1083,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: async ()=>{\n                                            console.log('🔄 تحديث الإعادات...');\n                                            scrollPositionRef.current = window.scrollY;\n                                            shouldRestoreScroll.current = true;\n                                            await fetchScheduleData();\n                                        },\n                                        style: {\n                                            width: '100%',\n                                            padding: '6px',\n                                            background: '#4caf50',\n                                            color: 'white',\n                                            border: 'none',\n                                            borderRadius: '4px',\n                                            fontSize: '12px',\n                                            cursor: 'pointer'\n                                        },\n                                        children: [\n                                            \"♻️ \",\n                                            t('schedule.updateReruns')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 991,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: selectedType,\n                                onChange: (e)=>setSelectedType(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '10px',\n                                    fontSize: '14px',\n                                    backgroundColor: 'white',\n                                    color: '#333'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('schedule.allTypes')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SERIES\",\n                                        children: \"\\uD83D\\uDCFA Series\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILM\",\n                                        children: \"\\uD83C\\uDFA5 Film\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROGRAM\",\n                                        children: \"\\uD83D\\uDCFB Program\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"SONG\",\n                                        children: \"\\uD83C\\uDFB5 Song\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"PROMO\",\n                                        children: \"\\uD83D\\uDCE2 Promo\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"STING\",\n                                        children: \"⚡ Sting\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"FILLER\",\n                                        children: \"⏸️ Filler\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NEXT\",\n                                        children: \"▶️ Next\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"NOW\",\n                                        children: \"\\uD83D\\uDD34 Now\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"سنعود\",\n                                        children: \"⏰ سنعود\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1148,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"عدنا\",\n                                        children: \"✅ عدنا\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1149,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"MINI\",\n                                        children: \"\\uD83D\\uDD38 Mini\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1150,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"CROSS\",\n                                        children: \"✖️ Cross\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1151,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"\\uD83D\\uDD0D \".concat(t('schedule.searchMedia')),\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                style: {\n                                    width: '100%',\n                                    padding: '10px',\n                                    border: '1px solid #6b7280',\n                                    borderRadius: '5px',\n                                    marginBottom: '15px',\n                                    fontSize: '14px',\n                                    color: '#333',\n                                    background: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    fontSize: '12px',\n                                    color: '#d1d5db',\n                                    marginBottom: '10px',\n                                    textAlign: 'center',\n                                    padding: '5px',\n                                    background: '#1f2937',\n                                    borderRadius: '4px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCA \",\n                                    t('schedule.resultsCount', {\n                                        count: filteredMedia.length,\n                                        total: allAvailableMedia.length\n                                    })\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1173,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    flexDirection: 'column',\n                                    gap: '8px'\n                                },\n                                children: filteredMedia.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        textAlign: 'center',\n                                        padding: '20px',\n                                        color: '#666',\n                                        background: '#f8f9fa',\n                                        borderRadius: '8px',\n                                        border: '2px dashed #dee2e6'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '2rem',\n                                                marginBottom: '10px'\n                                            },\n                                            children: \"\\uD83D\\uDD0D\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1197,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontWeight: 'bold',\n                                                marginBottom: '5px'\n                                            },\n                                            children: t('schedule.noMedia')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                fontSize: '12px'\n                                            },\n                                            children: searchTerm || selectedType ? t('schedule.changeFilter') : t('schedule.addNewMedia')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1199,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1189,\n                                    columnNumber: 17\n                                }, this) : filteredMedia.map((item)=>{\n                                    // تحديد لون المادة حسب النوع\n                                    const getItemStyle = ()=>{\n                                        if (item.isTemporary) {\n                                            switch(item.type){\n                                                case 'LIVE':\n                                                    return {\n                                                        background: '#ffebee',\n                                                        border: '2px solid #f44336',\n                                                        borderLeft: '5px solid #f44336'\n                                                    };\n                                                case 'PENDING':\n                                                    return {\n                                                        background: '#fff8e1',\n                                                        border: '2px solid #ffc107',\n                                                        borderLeft: '5px solid #ffc107'\n                                                    };\n                                                default:\n                                                    return {\n                                                        background: '#f3e5f5',\n                                                        border: '2px solid #9c27b0',\n                                                        borderLeft: '5px solid #9c27b0'\n                                                    };\n                                            }\n                                        }\n                                        return {\n                                            background: '#fff',\n                                            border: '1px solid #ddd'\n                                        };\n                                    };\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>handleDragStart(e, item),\n                                        style: {\n                                            ...getItemStyle(),\n                                            borderRadius: '8px',\n                                            padding: '12px',\n                                            cursor: 'grab',\n                                            transition: 'all 0.2s',\n                                            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                                            position: 'relative'\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(-2px)';\n                                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0,0,0,0.15)';\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = 'translateY(0)';\n                                            e.currentTarget.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';\n                                        },\n                                        children: [\n                                            item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteTempMedia(item.id);\n                                                },\n                                                style: {\n                                                    position: 'absolute',\n                                                    top: '5px',\n                                                    left: '5px',\n                                                    background: '#f44336',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '50%',\n                                                    width: '20px',\n                                                    height: '20px',\n                                                    fontSize: '12px',\n                                                    cursor: 'pointer',\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    justifyContent: 'center'\n                                                },\n                                                title: t('schedule.confirmDeleteTemp'),\n                                                children: \"\\xd7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1260,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontWeight: 'bold',\n                                                    color: '#333',\n                                                    marginBottom: '4px'\n                                                },\n                                                children: [\n                                                    item.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            fontSize: '10px',\n                                                            background: item.type === 'LIVE' ? '#f44336' : item.type === 'PENDING' ? '#ffc107' : '#9c27b0',\n                                                            color: 'white',\n                                                            padding: '2px 6px',\n                                                            borderRadius: '10px',\n                                                            marginLeft: '5px'\n                                                        },\n                                                        children: item.type === 'LIVE' ? \"\\uD83D\\uDD34 \".concat(t('schedule.liveProgram')) : item.type === 'PENDING' ? \"\\uD83D\\uDFE1 \".concat(t('schedule.pendingDelivery')) : \"\\uD83D\\uDFE3 \".concat(t('schedule.temporary'))\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1289,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    getMediaDisplayText(item)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1287,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '12px',\n                                                    color: '#666'\n                                                },\n                                                children: [\n                                                    item.type,\n                                                    \" • \",\n                                                    item.duration\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1304,\n                                                columnNumber: 23\n                                            }, this),\n                                            item.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '11px',\n                                                    color: '#888',\n                                                    marginTop: '4px'\n                                                },\n                                                children: item.description\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1236,\n                                        columnNumber: 21\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 980,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            overflowY: 'auto'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px',\n                                    background: '#4a5568',\n                                    padding: '15px',\n                                    borderRadius: '15px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>window.location.href = '/daily-schedule',\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #007bff, #0056b3)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: [\n                                                    \"\\uD83D\\uDCCB \",\n                                                    t('schedule.broadcastSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1333,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: async ()=>{\n                                                    try {\n                                                        console.log('📊 بدء تصدير الخريطة الأسبوعية...');\n                                                        const response = await fetch(\"/api/export-schedule?weekStart=\".concat(selectedWeek));\n                                                        if (!response.ok) {\n                                                            throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                                                        }\n                                                        const blob = await response.blob();\n                                                        const url = window.URL.createObjectURL(blob);\n                                                        const a = document.createElement('a');\n                                                        a.href = url;\n                                                        a.download = \"Weekly_Schedule_\".concat(selectedWeek, \".xlsx\");\n                                                        document.body.appendChild(a);\n                                                        a.click();\n                                                        window.URL.revokeObjectURL(url);\n                                                        document.body.removeChild(a);\n                                                        console.log('✅ تم تصدير الخريطة بنجاح');\n                                                    } catch (error) {\n                                                        console.error('❌ خطأ في تصدير الخريطة:', error);\n                                                        alert('فشل في تصدير الخريطة: ' + error.message);\n                                                    }\n                                                },\n                                                style: {\n                                                    background: 'linear-gradient(45deg, #28a745, #20c997)',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '8px',\n                                                    padding: '10px 20px',\n                                                    fontSize: '0.9rem',\n                                                    fontWeight: 'bold',\n                                                    cursor: 'pointer',\n                                                    boxShadow: '0 4px 15px rgba(0,0,0,0.2)',\n                                                    transition: 'transform 0.2s ease',\n                                                    marginLeft: '10px'\n                                                },\n                                                onMouseEnter: (e)=>e.currentTarget.style.transform = 'translateY(-2px)',\n                                                onMouseLeave: (e)=>e.currentTarget.style.transform = 'translateY(0)',\n                                                children: [\n                                                    \"\\uD83D\\uDCCA \",\n                                                    t('schedule.exportSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1358,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                style: {\n                                                    margin: 0,\n                                                    color: '#f3f4f6',\n                                                    fontSize: '1.2rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCC5 \",\n                                                    t('schedule.selectedWeek')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1405,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1332,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'flex',\n                                            alignItems: 'center',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(-1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    \"← \",\n                                                    t('schedule.previousWeek')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1409,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: selectedWeek,\n                                                onChange: (e)=>{\n                                                    const selectedDate = new Date(e.target.value + 'T00:00:00');\n                                                    const dayOfWeek = selectedDate.getDay();\n                                                    console.log('📅 تغيير التاريخ من التقويم:', {\n                                                        selectedDate: e.target.value,\n                                                        dayOfWeek: dayOfWeek,\n                                                        dayName: [\n                                                            'الأحد',\n                                                            'الاثنين',\n                                                            'الثلاثاء',\n                                                            'الأربعاء',\n                                                            'الخميس',\n                                                            'الجمعة',\n                                                            'السبت'\n                                                        ][dayOfWeek]\n                                                    });\n                                                    // حساب بداية الأسبوع (الأحد)\n                                                    const sunday = new Date(selectedDate);\n                                                    sunday.setHours(0, 0, 0, 0); // تصفير الوقت\n                                                    sunday.setDate(selectedDate.getDate() - dayOfWeek);\n                                                    const weekStart = sunday.toISOString().split('T')[0];\n                                                    console.log('📅 بداية الأسبوع المحسوبة:', weekStart);\n                                                    console.log('📊 يوم الأسبوع لبداية الأسبوع:', sunday.getDay(), '(يجب أن يكون 0)');\n                                                    setSelectedWeek(weekStart);\n                                                },\n                                                style: {\n                                                    padding: '8px',\n                                                    border: '1px solid #6b7280',\n                                                    borderRadius: '5px',\n                                                    color: '#333',\n                                                    background: 'white'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1423,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>changeWeek(1),\n                                                style: {\n                                                    padding: '8px 15px',\n                                                    background: '#007bff',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    t('schedule.nextWeek'),\n                                                    \" →\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1456,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1322,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: scheduleTableRef,\n                                style: {\n                                    background: 'linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%)',\n                                    borderRadius: '10px',\n                                    overflow: 'hidden',\n                                    boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n                                    minHeight: '80vh'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    style: {\n                                        width: '100%',\n                                        borderCollapse: 'collapse'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                style: {\n                                                    background: '#f8f9fa'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                        style: {\n                                                            padding: '12px',\n                                                            border: '1px solid #dee2e6',\n                                                            fontWeight: 'bold',\n                                                            width: '80px',\n                                                            color: '#000'\n                                                        },\n                                                        children: t('schedule.time')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1486,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    days.map((day, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            style: {\n                                                                padding: '12px',\n                                                                border: '1px solid #dee2e6',\n                                                                fontWeight: 'bold',\n                                                                width: \"\".concat(100 / 7, \"%\"),\n                                                                textAlign: 'center'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        marginBottom: '4px',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: day\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1503,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        fontSize: '1rem',\n                                                                        color: '#000',\n                                                                        fontWeight: 'bold'\n                                                                    },\n                                                                    children: weekDates[index]\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1504,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1496,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1485,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1484,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                            children: hours.map((hour, hourIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"hour-row\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                            style: {\n                                                                background: '#f8f9fa',\n                                                                fontWeight: 'bold',\n                                                                textAlign: 'center',\n                                                                padding: '8px',\n                                                                border: '1px solid #dee2e6',\n                                                                color: '#000'\n                                                            },\n                                                            children: hour\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                            lineNumber: 1516,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        days.map((_, dayIndex)=>{\n                                                            const cellItems = getItemsForCell(dayIndex, hour);\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                onDragOver: handleDragOver,\n                                                                onDrop: (e)=>handleDrop(e, dayIndex, hour),\n                                                                style: {\n                                                                    border: '1px solid #dee2e6',\n                                                                    padding: '8px',\n                                                                    height: '150px',\n                                                                    cursor: 'pointer',\n                                                                    background: cellItems.length > 0 ? 'transparent' : 'rgba(255,255,255,0.3)',\n                                                                    verticalAlign: 'top'\n                                                                },\n                                                                children: cellItems.map((item)=>{\n                                                                    var _item_mediaItem, _item_mediaItem1, _item_mediaItem2, _item_mediaItem3, _item_mediaItem4, _item_mediaItem5, _item_mediaItem6, _item_mediaItem7;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        draggable: true,\n                                                                        onDragStart: (e)=>handleScheduleItemDragStart(e, item),\n                                                                        onClick: ()=>deleteItem(item),\n                                                                        style: {\n                                                                            background: item.isRerun ? '#f0f0f0' : item.isTemporary ? ((_item_mediaItem = item.mediaItem) === null || _item_mediaItem === void 0 ? void 0 : _item_mediaItem.type) === 'LIVE' ? '#ffebee' : ((_item_mediaItem1 = item.mediaItem) === null || _item_mediaItem1 === void 0 ? void 0 : _item_mediaItem1.type) === 'PENDING' ? '#fff8e1' : '#f3e5f5' : '#fff3e0',\n                                                                            border: \"2px solid \".concat(item.isRerun ? '#888888' : item.isTemporary ? ((_item_mediaItem2 = item.mediaItem) === null || _item_mediaItem2 === void 0 ? void 0 : _item_mediaItem2.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem3 = item.mediaItem) === null || _item_mediaItem3 === void 0 ? void 0 : _item_mediaItem3.type) === 'PENDING' ? '#ffc107' : '#9c27b0' : '#ff9800'),\n                                                                            borderRadius: '4px',\n                                                                            padding: '8px 6px',\n                                                                            marginBottom: '4px',\n                                                                            fontSize: '1rem',\n                                                                            cursor: 'grab',\n                                                                            transition: 'all 0.2s ease',\n                                                                            minHeight: '60px',\n                                                                            display: 'flex',\n                                                                            flexDirection: 'column',\n                                                                            justifyContent: 'center'\n                                                                        },\n                                                                        onMouseEnter: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1.02)';\n                                                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.2)';\n                                                                        },\n                                                                        onMouseLeave: (e)=>{\n                                                                            e.currentTarget.style.transform = 'scale(1)';\n                                                                            e.currentTarget.style.boxShadow = 'none';\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontWeight: 'bold',\n                                                                                    display: 'flex',\n                                                                                    alignItems: 'center',\n                                                                                    gap: '4px',\n                                                                                    color: '#000'\n                                                                                },\n                                                                                children: [\n                                                                                    item.isRerun ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#666'\n                                                                                        },\n                                                                                        children: [\n                                                                                            \"♻️ \",\n                                                                                            item.rerunPart ? \"ج\".concat(item.rerunPart) : '',\n                                                                                            item.rerunCycle ? \"(\".concat(item.rerunCycle, \")\") : ''\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1584,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : item.isTemporary ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: ((_item_mediaItem4 = item.mediaItem) === null || _item_mediaItem4 === void 0 ? void 0 : _item_mediaItem4.type) === 'LIVE' ? '#f44336' : ((_item_mediaItem5 = item.mediaItem) === null || _item_mediaItem5 === void 0 ? void 0 : _item_mediaItem5.type) === 'PENDING' ? '#ffc107' : '#9c27b0'\n                                                                                        },\n                                                                                        children: ((_item_mediaItem6 = item.mediaItem) === null || _item_mediaItem6 === void 0 ? void 0 : _item_mediaItem6.type) === 'LIVE' ? '🔴' : ((_item_mediaItem7 = item.mediaItem) === null || _item_mediaItem7 === void 0 ? void 0 : _item_mediaItem7.type) === 'PENDING' ? '🟡' : '🟣'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1588,\n                                                                                        columnNumber: 33\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#ff9800'\n                                                                                        },\n                                                                                        children: \"\\uD83C\\uDF1F\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1596,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        style: {\n                                                                                            color: '#000'\n                                                                                        },\n                                                                                        children: item.mediaItem ? getMediaDisplayText(item.mediaItem) : t('schedule.unknown')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                        lineNumber: 1598,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1582,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.8rem',\n                                                                                    color: '#000',\n                                                                                    marginTop: '4px'\n                                                                                },\n                                                                                children: [\n                                                                                    item.startTime,\n                                                                                    \" - \",\n                                                                                    item.endTime\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1602,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            item.isRerun && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                style: {\n                                                                                    fontSize: '0.5rem',\n                                                                                    color: '#888',\n                                                                                    fontStyle: 'italic'\n                                                                                },\n                                                                                children: t('schedule.rerunIndicator')\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                                lineNumber: 1606,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, item.id, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1544,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, dayIndex, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1530,\n                                                                columnNumber: 23\n                                                            }, this);\n                                                        })\n                                                    ]\n                                                }, hourIndex, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                    lineNumber: 1515,\n                                                    columnNumber: 17\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                            lineNumber: 1513,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                    lineNumber: 1482,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1473,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    background: '#4a5568',\n                                    padding: '20px',\n                                    borderRadius: '15px',\n                                    marginTop: '20px',\n                                    border: '1px solid #6b7280'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            margin: '0 0 20px 0',\n                                            fontSize: '1.3rem',\n                                            textAlign: 'center'\n                                        },\n                                        children: t('schedule.usageInstructions')\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px',\n                                            marginBottom: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.addMediaTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1633,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction1')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1635,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction2')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1636,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction3')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1637,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.addMediaInstruction4')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1638,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1634,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1632,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.deleteMediaTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1642,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            t('schedule.originalMaterial'),\n                                                                            \":\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1644,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    t('schedule.deleteOriginalInfo')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1644,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: [\n                                                                            t('schedule.rerunMaterial'),\n                                                                            \":\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1645,\n                                                                        columnNumber: 53\n                                                                    }, this),\n                                                                    \" \",\n                                                                    t('schedule.deleteRerunInfo')\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1645,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.deleteConfirmInfo')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1646,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1643,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1641,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1631,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '20px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.primeTimeTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1653,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.primeTimeSchedule1')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1655,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: t('schedule.primeTimeSchedule2')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1656,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    style: {\n                                                                        color: '#fbbf24'\n                                                                    },\n                                                                    children: t('schedule.primeTimeColor')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1657,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1657,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1654,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1652,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        style: {\n                                                            color: '#fbbf24',\n                                                            fontSize: '1.1rem'\n                                                        },\n                                                        children: t('schedule.rerunsTitle')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1661,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                        style: {\n                                                            margin: '10px 0',\n                                                            paddingRight: '20px',\n                                                            fontSize: '1rem',\n                                                            color: '#d1d5db'\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: t('schedule.rerunsSchedule1')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1663,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1663,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart1Sun')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1665,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart2Sun')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1666,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1664,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: t('schedule.rerunsSchedule2')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1668,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1668,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                style: {\n                                                                    margin: '5px 0',\n                                                                    paddingRight: '15px',\n                                                                    fontSize: '0.9rem'\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart1Thu')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1670,\n                                                                        columnNumber: 19\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        style: {\n                                                                            marginBottom: '5px'\n                                                                        },\n                                                                        children: t('schedule.rerunsPart2Thu')\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                        lineNumber: 1671,\n                                                                        columnNumber: 19\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1669,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                style: {\n                                                                    marginBottom: '8px'\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    style: {\n                                                                        color: '#9ca3af'\n                                                                    },\n                                                                    children: t('schedule.rerunsColor')\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                    lineNumber: 1673,\n                                                                    columnNumber: 53\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                                lineNumber: 1673,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                        lineNumber: 1662,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1660,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1651,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #f59e0b'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#fbbf24',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: t('schedule.dateManagementTitle')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1679,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: [\n                                                    \" \",\n                                                    t('schedule.dateManagementInfo')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1680,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1678,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px',\n                                            padding: '15px',\n                                            background: '#1f2937',\n                                            borderRadius: '10px',\n                                            border: '1px solid #3b82f6'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                style: {\n                                                    color: '#60a5fa',\n                                                    fontSize: '1.1rem'\n                                                },\n                                                children: t('schedule.importantNoteTitle')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1684,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    color: '#d1d5db',\n                                                    fontSize: '1rem'\n                                                },\n                                                children: [\n                                                    \" \",\n                                                    t('schedule.importantNoteInfo')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                                lineNumber: 1685,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                        lineNumber: 1683,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                                lineNumber: 1622,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                        lineNumber: 1320,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n                lineNumber: 972,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n            lineNumber: 971,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\weekly-schedule\\\\page.tsx\",\n        lineNumber: 970,\n        columnNumber: 5\n    }, this);\n}\n_s(WeeklySchedulePage, \"z+bOjI1IMp+y5Jz2wzsc+HDFldA=\", false, function() {\n    return [\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = WeeklySchedulePage;\nvar _c;\n$RefreshReg$(_c, \"WeeklySchedulePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/weekly-schedule/page.tsx\n"));

/***/ })

});