'use client';
import React, { useState, useEffect } from 'react';
import { AuthGuard, useAuth } from '@/components/AuthGuard';
import DashboardLayout from '@/components/DashboardLayout';
import { useTranslatedToast } from '@/hooks/useTranslatedToast';
import { useAppTranslation } from '@/hooks/useAppTranslation';
import * as XLSX from 'xlsx';
import ExcelJS from 'exceljs';

// تعريف أنواع البيانات
interface MediaItem {
  id: string;
  name: string;
  type: string;
  duration: string;
  category?: string;
}

interface ScheduleItem {
  id: string;
  mediaId: string;
  date: string;
  time: string;
  mediaName: string;
  mediaType: string;
  mediaDuration: string;
  isRerun?: boolean;
  isTemporary?: boolean;
}

interface ReportItem {
  id: string;
  mediaId: string;
  mediaName: string;
  mediaType: string;
  mediaDuration: string;
  date: string;
  time: string;
  source: 'weekly' | 'daily';
  isRerun?: boolean;
  isTemporary?: boolean;
  mediaCode?: string;
}

// تعريف نوع البيانات للإحصائيات
interface StatsItem {
  mediaType: string;
  count: number;
  totalDuration: number; // بالثواني
  formattedDuration: string; // بتنسيق HH:MM:SS
}

export default function ReportsPage() {
  const { showSuccessToast, showErrorToast, ToastContainer } = useTranslatedToast();
  const { t, i18n } = useTranslation('common');
  const currentLang = i18n.language || 'ar';
  const isRTL = currentLang === 'ar';

  const [loading, setLoading] = useState(false);
  const [mediaTypes, setMediaTypes] = useState<string[]>([]);
  const [mediaItems, setMediaItems] = useState<MediaItem[]>([]);
  const [filteredMediaItems, setFilteredMediaItems] = useState<MediaItem[]>([]);
  const [reportResults, setReportResults] = useState<ReportItem[]>([]);
  const [weeklySchedule, setWeeklySchedule] = useState<ScheduleItem[]>([]);
  const [dailySchedule, setDailySchedule] = useState<ScheduleItem[]>([]);
  
  // إحصائيات المواد
  const [statsResults, setStatsResults] = useState<StatsItem[]>([]);
  const [totalStats, setTotalStats] = useState({ count: 0, duration: '00:00:00' });
  const [showStats, setShowStats] = useState(false);
  
  // Search filters
  const [filters, setFilters] = useState({
    mediaType: '',
    mediaName: '',
    mediaCode: '', // Add search by code
    source: 'both', // 'weekly', 'daily', 'both'
    dateFrom: '',
    dateTo: '',
  });

  // تحميل البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchMediaItems();
    // لم نعد بحاجة إلى تحميل الجداول مباشرة لأننا نستخدم API التقارير
    // fetchWeeklySchedule();
    // fetchDailySchedule();
  }, []);

  // Update filtered media list when media type changes
  useEffect(() => {
    if (mediaItems && mediaItems.length > 0) {
      if (filters.mediaType) {
        setFilteredMediaItems(mediaItems.filter(item => item.type === filters.mediaType));
      } else {
        setFilteredMediaItems(mediaItems);
      }
    } else {
      setFilteredMediaItems([]);
    }
  }, [filters.mediaType, mediaItems]);

  // تحديد أنواع المواد المتاحة (بناءً على البيانات الحقيقية)
  useEffect(() => {
    // أنواع المواد المدعومة في النظام
    const availableTypes = [
      'PROGRAM',
      'FILM',
      'SERIES',
      'SONG',
      'FILLER',
      'STING',
      'PROMO',
      'NEXT',
      'NOW',
      'سنعود',
      'عدنا'
    ];

    setMediaTypes(availableTypes);
    console.log('✅ تم تحديد أنواع المواد المتاحة:', availableTypes);
  }, []);

  // جلب قائمة المواد
  const fetchMediaItems = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/media');
      const data = await response.json();
      
      if (data.success) {
        setMediaItems(data.media);
      } else {
        showErrorToast('serverConnection');
      }
    } catch (error) {
      console.error('Error fetching media:', error);
      showErrorToast('serverConnection');
    } finally {
      setLoading(false);
    }
  };

  // جلب بيانات الخريطة البرامجية - لم تعد مستخدمة مباشرة
  const fetchWeeklySchedule = async () => {
    try {
      const response = await fetch('/api/schedule/weekly');
      const data = await response.json();
      
      if (data.success) {
        setWeeklySchedule(data.scheduleItems || []);
      } else {
        console.log('ملاحظة: سيتم استخدام API التقارير الجديدة بدلاً من هذا');
      }
    } catch (error) {
      console.error('Error fetching weekly schedule:', error);
      // لا نعرض رسالة خطأ لأننا سنستخدم API التقارير الجديدة
    }
  };

  // جلب بيانات جدول الإذاعة اليومي - لم تعد مستخدمة مباشرة
  const fetchDailySchedule = async () => {
    try {
      const response = await fetch('/api/schedule/daily');
      const data = await response.json();
      
      if (data.success) {
        setDailySchedule(data.scheduleItems || []);
      } else {
        console.log('ملاحظة: سيتم استخدام API التقارير الجديدة بدلاً من هذا');
      }
    } catch (error) {
      console.error('Error fetching daily schedule:', error);
      // لا نعرض رسالة خطأ لأننا سنستخدم API التقارير الجديدة
    }
  };

  // Update filters
  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  // دالة مساعدة لتحديد ما إذا كان العنصر ينتمي إلى اليوم الإذاعي المحدد
  const isBroadcastDayMatch = (itemDate: string, itemTime: string, broadcastDate: Date): boolean => {
    // تحويل التاريخ والوقت إلى كائن Date
    const itemDateTime = new Date(`${itemDate}T${itemTime}`);
    
    // Define broadcast day start (08:00 AM of specified date)
    const broadcastDayStart = new Date(broadcastDate);
    broadcastDayStart.setHours(8, 0, 0, 0);
    
    // تحديد نهاية اليوم الإذاعي (07:59:59.999 صباحًا من اليوم التالي)
    const broadcastDayEnd = new Date(broadcastDate);
    broadcastDayEnd.setDate(broadcastDayEnd.getDate() + 1);
    broadcastDayEnd.setHours(7, 59, 59, 999);
    
    // التحقق مما إذا كان العنصر يقع ضمن اليوم الإذاعي
    return itemDateTime >= broadcastDayStart && itemDateTime <= broadcastDayEnd;
  };

  // Search and generate report
  const handleSearch = async () => {
    if (!filters.mediaName && !filters.mediaType && !filters.mediaCode && !filters.dateFrom && !filters.dateTo) {
      showErrorToast('invalidData');
      return;
    }

    setLoading(true);

    try {
      // Build URL with search criteria
      const queryParams = new URLSearchParams();

      if (filters.mediaName && filters.mediaName.trim()) {
        queryParams.append('mediaName', filters.mediaName.trim());
      }

      if (filters.mediaCode && filters.mediaCode.trim()) {
        queryParams.append('mediaCode', filters.mediaCode.trim());
      }

      if (filters.mediaType && filters.mediaType !== '') {
        queryParams.append('mediaType', filters.mediaType);
      }

      if (filters.source && filters.source !== 'both') {
        queryParams.append('source', filters.source);
      }

      if (filters.dateFrom) {
        queryParams.append('dateFrom', filters.dateFrom);
      }

      if (filters.dateTo) {
        queryParams.append('dateTo', filters.dateTo);
      }

      // Print search information for debugging
      console.log('🔍 Search criteria sent:', {
        mediaName: filters.mediaName,
        mediaType: filters.mediaType,
        source: filters.source,
        dateFrom: filters.dateFrom,
        dateTo: filters.dateTo,
        queryString: queryParams.toString()
      });

      // استدعاء API التقارير المحسن
      const response = await fetch(`/api/reports?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`خطأ في الاستجابة: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        const results = data.results || [];
        console.log('✅ Final results:', results.length, 'results');

        // Update search results
        setReportResults(results);

        // Calculate statistics
        const { stats, total } = calculateStats(results);
        setStatsResults(stats);
        setTotalStats(total);
        setShowStats(results.length > 0);

        if (results.length === 0) {
          showErrorToast('mediaNotFound');
        } else {
          showSuccessToast('exportSuccess');
        }
      } else {
        throw new Error(data.error || 'خطأ غير معروف في الاستجابة');
      }
    } catch (error) {
      console.error('❌ خطأ في إنشاء التقرير:', error);
      showErrorToast('unknownError');
    } finally {
      setLoading(false);
    }
  };

  // تصدير التقرير إلى ملف Excel
  const exportToExcel = async () => {
    if (reportResults.length === 0) {
      showErrorToast('mediaNotFound');
      return;
    }

    try {
      // إنشاء مصنف جديد
      const workbook = new ExcelJS.Workbook();

      // إعداد خصائص المصنف
      workbook.views = [{
        x: 0, y: 0, width: 10000, height: 20000,
        firstSheet: 0, activeTab: 0, visibility: 'visible'
      }];

      // إنشاء ورقة العمل
      const worksheet = workbook.addWorksheet('تقرير البث', {
        rightToLeft: true,
        views: [{
          rightToLeft: true,
          zoomScale: 80,
          showGridLines: true
        }]
      });

      // تحديد الأعمدة
      worksheet.columns = [
        { header: 'Code', key: 'code', width: 20 },
        { header: 'Media Name', key: 'name', width: 35 },
        { header: 'Media Type', key: 'type', width: 15 },
        { header: 'Duration', key: 'duration', width: 12 },
        { header: 'Date', key: 'date', width: 25 },
        { header: 'Time', key: 'time', width: 12 },
        { header: 'Source', key: 'source', width: 22 },
        { header: 'Broadcast Type', key: 'broadcastType', width: 12 }
      ];

      // تنسيق رأس الجدول
      const headerRow = worksheet.getRow(1);
      headerRow.height = 25;
      headerRow.eachCell((cell) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FF4472C4' }
        };
        cell.font = {
          color: { argb: 'FFFFFFFF' },
          bold: true,
          size: 12
        };
        cell.alignment = {
          horizontal: 'center',
          vertical: 'middle'
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });

      // إضافة البيانات
      reportResults.forEach((item, index) => {
        const row = worksheet.addRow({
          code: item.mediaCode || item.mediaId || 'Not specified',
          name: item.mediaName,
          type: translateMediaType(item.mediaType),
          duration: item.mediaDuration,
          date: formatDate(item.date),
          time: item.time,
          source: item.source === 'weekly' ? 'Weekly Schedule' : 'Daily Schedule',
          broadcastType: item.isRerun ? 'Rerun' : (item.isTemporary ? 'Temporary' : 'Primary')
        });

        // تنسيق صفوف البيانات
        row.height = 20;
        row.eachCell((cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: index % 2 === 0 ? 'FFFFFFFF' : 'FFF2F2F2' }
          };
          cell.alignment = {
            horizontal: 'center',
            vertical: 'middle'
          };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        });
      });

      // إنشاء buffer من المصنف
      const buffer = await workbook.xlsx.writeBuffer();

      // تحديد اسم الملف
      let fileName = 'تقرير_البث';

      // إضافة تفاصيل الفلترة لاسم الملف
      if (filters.mediaName) {
        fileName += `_${filters.mediaName}`;
      } else if (filters.mediaType) {
        fileName += `_${translateMediaType(filters.mediaType)}`;
      }

      if (filters.dateFrom) {
        fileName += `_من_${filters.dateFrom}`;
      }

      if (filters.dateTo) {
        fileName += `_إلى_${filters.dateTo}`;
      }

      // إنشاء رابط التنزيل
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${fileName}.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);

      showSuccessToast('exportSuccess');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      showErrorToast('exportFailed');
    }
  };
  
  // تصدير الإحصائيات فقط إلى ملف Excel
  const exportStatsToExcel = () => {
    if (statsResults.length === 0) {
      showErrorToast('mediaNotFound');
      return;
    }
    
    try {
      // تحويل بيانات الإحصائيات إلى تنسيق مناسب للتصدير
      const statsExportData = statsResults.map(item => ({
        'Media Type': translateMediaType(item.mediaType),
        'Count': item.count,
        'Total Duration': item.formattedDuration
      }));
      
      // إضافة صف للإجمالي
      statsExportData.push({
        'Media Type': 'Total',
        'Count': totalStats.count,
        'Total Duration': totalStats.duration
      });
      
      // إنشاء ورقة عمل
      const worksheet = XLSX.utils.json_to_sheet(statsExportData, { header: Object.keys(statsExportData[0]) });
      
      // تنسيق عرض الأعمدة
      const columnWidths = [
        { wch: 20 }, // Media Type
        { wch: 15 }, // Count
        { wch: 15 }  // Total Duration
      ];
      worksheet['!cols'] = columnWidths;
      
      // إنشاء مصنف عمل
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'إحصائيات البث');
      
      // تحديد اسم الملف
      let fileName = 'إحصائيات_البث';
      
      // إضافة تفاصيل الفلترة لاسم الملف
      if (filters.mediaName) {
        fileName += `_${filters.mediaName}`;
      } else if (filters.mediaType) {
        fileName += `_${translateMediaType(filters.mediaType)}`;
      }
      
      if (filters.dateFrom) {
        fileName += `_من_${filters.dateFrom}`;
      }
      
      if (filters.dateTo) {
        fileName += `_إلى_${filters.dateTo}`;
      }
      
      // تصدير الملف
      XLSX.writeFile(workbook, `${fileName}.xlsx`);
      
      showSuccessToast('exportSuccess');
    } catch (error) {
      console.error('Error exporting stats to Excel:', error);
      showErrorToast('exportFailed');
    }
  };

  // تنسيق التاريخ للعرض
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  // Translate media type to Arabic
  const translateMediaType = (type: string) => {
    switch (type) {
      case "PROGRAM": return "برنامج";
      case "SERIES": return "مسلسل";
      case "MOVIE": return "فيلم";
      case "SONG": return "أغنية";
      case "NEWS": return "نشرة أخبار";
      case "SPORT": return "رياضة";
      case "ANALYSIS": return "تحليل";
      case "INTERVIEW": return "مقابلة";
      case "STING": return "ستنج";
      case "FILL_IN": return "فيل إن";
      case "FILLER": return "فيلر";
      case "PROMO": return "برومو";
      case "COMMERCIAL": return "إعلان";
      case "NEXT": return "NEXT";
      case "NOW": return "NOW";
      case "سنعود": return "سنعود";
      case "عدنا": return "عدنا";
      case "OTHER": return "أخرى";
      default: return type;
    }
  };
  
  // تحويل المدة من تنسيق HH:MM:SS إلى ثواني
  const durationToSeconds = (duration: string): number => {
    if (!duration) return 0;
    
    const parts = duration.split(':');
    if (parts.length !== 3) return 0;
    
    const hours = parseInt(parts[0], 10);
    const minutes = parseInt(parts[1], 10);
    const seconds = parseInt(parts[2], 10);
    
    return hours * 3600 + minutes * 60 + seconds;
  };
  
  // تحويل الثواني إلى تنسيق HH:MM:SS
  const secondsToFormattedDuration = (totalSeconds: number): string => {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  // Calculate statistics from search results
  const calculateStats = (results: ReportItem[]): { stats: StatsItem[], total: { count: number, duration: string } } => {
    // Group data by media type
    const statsByType: Record<string, { count: number, totalDuration: number }> = {};
    
    let totalCount = 0;
    let totalDurationSeconds = 0;
    
    // Calculate count and duration for each type
    results.forEach(item => {
      totalCount++;
      const durationSeconds = durationToSeconds(item.mediaDuration);
      totalDurationSeconds += durationSeconds;
      
      if (!statsByType[item.mediaType]) {
        statsByType[item.mediaType] = { count: 0, totalDuration: 0 };
      }
      
      statsByType[item.mediaType].count++;
      statsByType[item.mediaType].totalDuration += durationSeconds;
    });
    
    // تحويل البيانات إلى مصفوفة
    const statsArray: StatsItem[] = Object.keys(statsByType).map(type => ({
      mediaType: type,
      count: statsByType[type].count,
      totalDuration: statsByType[type].totalDuration,
      formattedDuration: secondsToFormattedDuration(statsByType[type].totalDuration)
    }));
    
    // ترتيب النتائج حسب العدد (تنازليًا)
    statsArray.sort((a, b) => b.count - a.count);
    
    return {
      stats: statsArray,
      total: {
        count: totalCount,
        duration: secondsToFormattedDuration(totalDurationSeconds)
      }
    };
  };

  // أنماط CSS محسنة
  const inputStyle = {
    width: '100%',
    padding: '12px 15px',
    borderRadius: '8px',
    border: '1px solid #4b5563',
    background: '#2d3748',
    color: 'white',
    fontSize: '1rem',
    outline: 'none',
    transition: 'all 0.3s ease',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  };

  const buttonStyle = {
    padding: '12px 24px',
    borderRadius: '8px',
    border: 'none',
    fontWeight: 'bold' as const,
    cursor: 'pointer',
    fontSize: '1rem',
    transition: 'all 0.3s ease',
    boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
  };
  
  const cardStyle = {
    background: '#4a5568',
    borderRadius: '15px',
    padding: '25px',
    marginBottom: '30px',
    border: '1px solid #6b7280',
    boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
  };

  return (
    <AuthGuard requiredRole="VIEWER">
      <DashboardLayout
        title={t('reports.title')}
        subtitle={t('reports.subtitle')}
        icon="📊"
      >
        <div style={{ padding: '20px' }}>
          {/* رسالة ترحيبية وتوضيحية */}
          <div style={{
            ...cardStyle,
            background: 'linear-gradient(135deg, #1e3a8a, #3b82f6)',
            marginBottom: '20px',
            textAlign: 'center'
          }}>
            <h1 style={{
              color: 'white',
              fontSize: '2rem',
              margin: '0 0 15px 0',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '15px'
            }}>
              <span style={{ fontSize: '2.5rem' }}>📊</span>
              {t('reports.title')}
            </h1>
            <p style={{
              color: '#e2e8f0',
              fontSize: '1.1rem',
              lineHeight: '1.6',
              margin: '0 0 10px 0'
            }}>
              {t('unified.searchDescription')}
            </p>
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '30px',
              flexWrap: 'wrap',
              marginTop: '15px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '1.2rem' }}>📅</span>
                <span style={{ color: '#cbd5e1' }}>{t('reports.programsFilmsSeries')}</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '1.2rem' }}>📺</span>
                <span style={{ color: '#cbd5e1' }}>{t('reports.promosFillersSting')}</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '1.2rem' }}>📈</span>
                <span style={{ color: '#cbd5e1' }}>{t('reports.detailedStatistics')}</span>
              </div>
            </div>
          </div>

          {/* قسم الفلاتر - تصميم محسن */}
          <div style={cardStyle}>
            <h2 style={{ 
              color: 'white', 
              marginBottom: '20px', 
              fontSize: '1.5rem',
              borderBottom: '2px solid #60a5fa',
              paddingBottom: '10px',
              display: 'flex',
              alignItems: 'center',
              gap: '10px'
            }}>
              <span style={{ 
                background: '#3b82f6', 
                borderRadius: '50%', 
                width: '36px', 
                height: '36px', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
              }}>
                🔍
              </span>
              {t('reports.searchFilters')}
            </h2>
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
              gap: '20px',
              marginBottom: '25px'
            }}>
              {/* Media Type */}
              <div>
                <label style={{ 
                  display: 'block', 
                  marginBottom: '8px', 
                  color: 'white', 
                  fontWeight: 'bold',
                  fontSize: '1.05rem'
                }}>
                  <span style={{ marginLeft: '8px' }}>📊</span>
                  {t('reports.mediaType')}
                </label>
                <select
                  name="mediaType"
                  value={filters.mediaType}
                  onChange={handleFilterChange}
                  style={{
                    ...inputStyle,
                    borderLeft: '4px solid #60a5fa'
                  }}
                >
                  <option value="">🔍 {t('reports.allTypes')}</option>
                  <optgroup label="📺 المحتوى الأساسي">
                    {mediaTypes.filter(type => ['PROGRAM', 'SERIES', 'MOVIE', 'NEWS', 'SPORT', 'ANALYSIS', 'INTERVIEW', 'SONG'].includes(type)).map(type => (
                      <option key={type} value={type}>
                        {translateMediaType(type)}
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="📢 الفواصل والبرومو">
                    {mediaTypes.filter(type => ['PROMO', 'STING', 'FILL_IN', 'FILLER', 'COMMERCIAL', 'NEXT', 'NOW', 'سنعود', 'عدنا'].includes(type)).map(type => (
                      <option key={type} value={type}>
                        {translateMediaType(type)}
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="🔧 أخرى">
                    {mediaTypes.filter(type => !['PROGRAM', 'SERIES', 'MOVIE', 'NEWS', 'SPORT', 'ANALYSIS', 'INTERVIEW', 'SONG', 'PROMO', 'STING', 'FILL_IN', 'FILLER', 'COMMERCIAL', 'NEXT', 'NOW', 'سنعود', 'عدنا'].includes(type)).map(type => (
                      <option key={type} value={type}>
                        {translateMediaType(type)}
                      </option>
                    ))}
                  </optgroup>
                </select>
              </div>
              
              {/* Media Name (for search) */}
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '1.05rem'
                }}>
                  <span style={{ marginLeft: '8px' }}>📝</span>
                  {t('reports.mediaName')}
                </label>
                <input
                  type="text"
                  name="mediaName"
                  value={filters.mediaName}
                  onChange={handleFilterChange}
                  placeholder={t('reports.mediaName')}
                  style={{
                    ...inputStyle,
                    borderLeft: '4px solid #f59e0b'
                  }}
                />
              </div>

              {/* Media Code (for search) */}
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '1.05rem'
                }}>
                  <span style={{ marginLeft: '8px' }}>🔢</span>
                  {t('reports.mediaCode')}
                </label>
                <input
                  type="text"
                  name="mediaCode"
                  value={filters.mediaCode}
                  onChange={handleFilterChange}
                  placeholder={t('reports.mediaCode')}
                  style={{
                    ...inputStyle,
                    borderLeft: '4px solid #8b5cf6'
                  }}
                />
              </div>
              
              {/* مصدر البيانات */}
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '8px',
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '1.05rem'
                }}>
                  <span style={{ marginLeft: '8px' }}>📋</span>
                  {t('reports.source')}
                </label>
                <select
                  name="source"
                  value={filters.source}
                  onChange={handleFilterChange}
                  style={{
                    ...inputStyle,
                    borderLeft: '4px solid #10b981'
                  }}
                >
                  <option value="both">🔍 {t('reports.both')}</option>
                  <option value="weekly">📅 {t('reports.weekly')}</option>
                  <option value="daily">📺 {t('reports.daily')}</option>
                </select>
                <div style={{
                  fontSize: '0.85rem',
                  color: '#d1d5db',
                  marginTop: '5px',
                  background: '#374151',
                  padding: '5px 10px',
                  borderRadius: '4px'
                }}>
                  <span style={{ marginLeft: '5px' }}>💡</span>
                  اختر "الجدول اليومي" للبحث عن البرومو والفواصل والستينغ
                </div>
              </div>
              
              {/* من تاريخ */}
              <div>
                <label style={{ 
                  display: 'block', 
                  marginBottom: '8px', 
                  color: 'white', 
                  fontWeight: 'bold',
                  fontSize: '1.05rem'
                }}>
                  <span style={{ marginLeft: '8px' }}>📅</span>
                  {t('reports.dateFrom')}
                </label>
                <input
                  type="date"
                  name="dateFrom"
                  value={filters.dateFrom}
                  onChange={handleFilterChange}
                  style={{
                    ...inputStyle,
                    borderLeft: '4px solid #8b5cf6'
                  }}
                />
                <div style={{
                  fontSize: '0.85rem',
                  color: '#d1d5db',
                  marginTop: '5px',
                  background: '#374151',
                  padding: '5px 10px',
                  borderRadius: '4px'
                }}>
                  <span style={{ marginLeft: '5px' }}>⏰</span>
                  يوم البث يبدأ في الساعة 08:00 صباحاً
                </div>
              </div>
              
              {/* إلى تاريخ */}
              <div>
                <label style={{ 
                  display: 'block', 
                  marginBottom: '8px', 
                  color: 'white', 
                  fontWeight: 'bold',
                  fontSize: '1.05rem'
                }}>
                  <span style={{ marginLeft: '8px' }}>📅</span>
                  {t('reports.dateTo')}
                </label>
                <input
                  type="date"
                  name="dateTo"
                  value={filters.dateTo}
                  onChange={handleFilterChange}
                  style={{
                    ...inputStyle,
                    borderLeft: '4px solid #8b5cf6'
                  }}
                />
                <div style={{ 
                  fontSize: '0.85rem', 
                  color: '#d1d5db', 
                  marginTop: '5px',
                  background: '#374151',
                  padding: '5px 10px',
                  borderRadius: '4px'
                }}>
                  <span style={{ marginLeft: '5px' }}>⏰</span>
                  يوم البث ينتهي في الساعة 07:59 صباحاً من اليوم التالي
                </div>
              </div>
            </div>
            
            {/* Search and Export Buttons */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: '15px',
              marginTop: '20px'
            }}>
              <button
                onClick={handleSearch}
                disabled={loading}
                style={{
                  ...buttonStyle,
                  background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
                  color: 'white',
                  minWidth: '180px',
                  transform: loading ? 'scale(0.98)' : 'scale(1)',
                  opacity: loading ? 0.9 : 1
                }}
                onMouseOver={(e) => {
                  if (!loading) e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseOut={(e) => {
                  if (!loading) e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                {loading ? `⏳ ${t('common.loading')}` : `🔍 ${t('reports.search')}`}
              </button>
            </div>
          </div>
          
          {/* قسم الإحصائيات - تصميم محسن */}
          {showStats && statsResults.length > 0 && (
            <div style={{
              ...cardStyle,
              background: 'linear-gradient(to bottom, #4a5568, #374151)'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '20px',
                borderBottom: '2px solid #60a5fa',
                paddingBottom: '10px'
              }}>
                <h2 style={{ 
                  color: 'white', 
                  fontSize: '1.5rem', 
                  margin: 0,
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px'
                }}>
                  <span style={{ 
                    background: '#8b5cf6', 
                    borderRadius: '50%', 
                    width: '36px', 
                    height: '36px', 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                  }}>
                    📊
                  </span>
                  {t('reports.statistics')}
                </h2>
                
                <button
                  onClick={exportStatsToExcel}
                  style={{
                    ...buttonStyle,
                    background: 'linear-gradient(45deg, #8b5cf6, #a78bfa)',
                    color: 'white',
                    fontSize: '0.95rem',
                    padding: '10px 18px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                  onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
                  onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
                >
                  <span>📥</span>
                  {t('reports.exportExcel')}
                </button>
              </div>
              
              <div style={{ 
                overflowX: 'auto',
                borderRadius: '10px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                background: '#2d3748'
              }}>
                <table style={{
                  width: '100%',
                  borderCollapse: 'collapse',
                  color: 'white'
                }}>
                  <thead>
                    <tr style={{ 
                      background: 'linear-gradient(45deg, #3b82f6, #60a5fa)',
                      borderRadius: '10px 10px 0 0'
                    }}>
                      <th style={{ 
                        padding: '15px', 
                        textAlign: isRTL ? 'right' : 'left',
                        borderBottom: '2px solid #4b5563',
                        borderTopRightRadius: '10px'
                      }}>
                        {t('reports.type')}
                      </th>
                      <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #4b5563' }}>
                        {t('reports.count')}
                      </th>
                      <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #4b5563' }}>
                        {t('reports.duration')}
                      </th>
                      <th style={{ 
                        padding: '15px', 
                        textAlign: 'right', 
                        borderBottom: '2px solid #4b5563',
                        borderTopLeftRadius: '10px'
                      }}>
                        {t('percentage')}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {statsResults.map((item, index) => {
                      // Determine row background color based on media type
                      let rowBackground;
                      switch(item.mediaType) {
                        case 'PROGRAM':
                          rowBackground = 'rgba(59, 130, 246, 0.1)';
                          break;
                        case 'SERIES':
                          rowBackground = 'rgba(16, 185, 129, 0.1)';
                          break;
                        case 'MOVIE':
                          rowBackground = 'rgba(245, 158, 11, 0.1)';
                          break;
                        case 'PROMO':
                          rowBackground = 'rgba(139, 92, 246, 0.1)';
                          break;
                        case 'COMMERCIAL':
                          rowBackground = 'rgba(236, 72, 153, 0.1)';
                          break;
                        default:
                          rowBackground = index % 2 === 0 ? '#2d3748' : '#374151';
                      }
                      
                      return (
                        <tr 
                          key={item.mediaType}
                          style={{
                            background: rowBackground,
                            borderBottom: '1px solid #4b5563',
                            transition: 'background 0.3s ease'
                          }}
                          onMouseOver={(e) => {
                            e.currentTarget.style.background = 'rgba(75, 85, 99, 0.3)';
                          }}
                          onMouseOut={(e) => {
                            e.currentTarget.style.background = rowBackground;
                          }}
                        >
                          <td style={{ 
                            padding: '15px',
                            fontWeight: 'bold',
                            borderRight: `4px solid ${
                              item.mediaType === 'PROGRAM' ? '#3b82f6' :
                              item.mediaType === 'SERIES' ? '#10b981' :
                              item.mediaType === 'MOVIE' ? '#f59e0b' :
                              item.mediaType === 'PROMO' ? '#8b5cf6' :
                              item.mediaType === 'COMMERCIAL' ? '#ec4899' : '#6b7280'
                            }`
                          }}>
                            {translateMediaType(item.mediaType)}
                          </td>
                          <td style={{ padding: '15px', textAlign: 'center' }}>
                            <span style={{
                              background: '#374151',
                              padding: '5px 10px',
                              borderRadius: '20px',
                              fontWeight: 'bold'
                            }}>
                              {item.count}
                            </span>
                          </td>
                          <td style={{ padding: '15px', textAlign: 'center' }}>
                            <span style={{
                              background: '#374151',
                              padding: '5px 10px',
                              borderRadius: '20px',
                              fontWeight: 'bold',
                              color: '#10b981'
                            }}>
                              {item.formattedDuration}
                            </span>
                          </td>
                          <td style={{ padding: '15px', textAlign: 'center' }}>
                            <div style={{
                              width: `${Math.round((item.count / totalStats.count) * 100)}%`,
                              background: `${
                                item.mediaType === 'PROGRAM' ? '#3b82f6' :
                                item.mediaType === 'SERIES' ? '#10b981' :
                                item.mediaType === 'MOVIE' ? '#f59e0b' :
                                item.mediaType === 'PROMO' ? '#8b5cf6' :
                                item.mediaType === 'COMMERCIAL' ? '#ec4899' : '#6b7280'
                              }`,
                              height: '8px',
                              borderRadius: '4px',
                              marginBottom: '5px'
                            }}></div>
                            <span style={{ fontWeight: 'bold' }}>
                              {totalStats.count > 0 ? `${Math.round((item.count / totalStats.count) * 100)}%` : '0%'}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                    <tr style={{ 
                      background: 'linear-gradient(45deg, #1e293b, #111827)', 
                      fontWeight: 'bold',
                      borderTop: '2px solid #4b5563'
                    }}>
                      <td style={{ padding: '15px', color: '#f3f4f6' }}>
                        الإجمالي
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <span style={{
                          background: '#374151',
                          padding: '5px 15px',
                          borderRadius: '20px',
                          fontWeight: 'bold',
                          color: '#f3f4f6'
                        }}>
                          {totalStats.count}
                        </span>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center' }}>
                        <span style={{
                          background: '#374151',
                          padding: '5px 15px',
                          borderRadius: '20px',
                          fontWeight: 'bold',
                          color: '#10b981'
                        }}>
                          {totalStats.duration}
                        </span>
                      </td>
                      <td style={{ padding: '15px', textAlign: 'center', color: '#f3f4f6' }}>
                        100%
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          )}
          
          {/* قسم النتائج - تصميم محسن */}
          <div style={{
            ...cardStyle,
            background: 'linear-gradient(to bottom, #4a5568, #374151)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px',
              borderBottom: '2px solid #10b981',
              paddingBottom: '10px'
            }}>
              <h2 style={{ 
                color: 'white', 
                fontSize: '1.5rem', 
                margin: 0,
                display: 'flex',
                alignItems: 'center',
                gap: '10px'
              }}>
                <span style={{ 
                  background: '#10b981', 
                  borderRadius: '50%', 
                  width: '36px', 
                  height: '36px', 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
                }}>
                  📋
                </span>
                {t('reports.searchResults')}
                {reportResults.length > 0 && (
                  <span style={{
                    background: '#10b981',
                    color: 'white',
                    borderRadius: '20px',
                    padding: '2px 12px',
                    fontSize: '1rem',
                    marginRight: '10px'
                  }}>
                    {reportResults.length}
                  </span>
                )}
              </h2>
              
              <div style={{ display: 'flex', gap: '10px' }}>
                {reportResults.length > 0 && (
                  <button
                    onClick={exportToExcel}
                    style={{
                      ...buttonStyle,
                      background: 'linear-gradient(45deg, #10b981, #34d399)',
                      color: 'white',
                      fontSize: '0.95rem',
                      padding: '10px 18px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                    onMouseOver={(e) => e.currentTarget.style.transform = 'translateY(-2px)'}
                    onMouseOut={(e) => e.currentTarget.style.transform = 'translateY(0)'}
                  >
                    <span>📥</span>
                    {t('reports.exportExcel')}
                  </button>
                )}
              </div>
            </div>
            
            {reportResults.length > 0 && (
              <div style={{ 
                color: '#d1d5db', 
                fontSize: '0.95rem', 
                marginBottom: '20px',
                background: '#2d3748',
                padding: '15px',
                borderRadius: '10px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
                display: 'flex',
                flexWrap: 'wrap',
                gap: '15px'
              }}>
                {filters.mediaName && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    background: 'rgba(245, 158, 11, 0.1)',
                    padding: '8px 15px',
                    borderRadius: '8px',
                    borderRight: '3px solid #f59e0b'
                  }}>
                    <span style={{ color: '#f59e0b' }}>📝</span>
                    <span style={{ fontWeight: 'bold', color: '#f3f4f6' }}>Media Name:</span>
                    <span>{filters.mediaName}</span>
                  </div>
                )}
                
                {filters.mediaType && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    background: 'rgba(59, 130, 246, 0.1)',
                    padding: '8px 15px',
                    borderRadius: '8px',
                    borderRight: '3px solid #3b82f6'
                  }}>
                    <span style={{ color: '#3b82f6' }}>📊</span>
                    <span style={{ fontWeight: 'bold', color: '#f3f4f6' }}>Media Type:</span>
                    <span>{translateMediaType(filters.mediaType)}</span>
                  </div>
                )}
                
                {!filters.mediaName && !filters.mediaType && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    background: 'rgba(107, 114, 128, 0.1)',
                    padding: '8px 15px',
                    borderRadius: '8px',
                    borderRight: '3px solid #6b7280'
                  }}>
                    <span style={{ color: '#6b7280' }}>🔍</span>
                    <span>كل المواد</span>
                  </div>
                )}
                
                {filters.dateFrom && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    background: 'rgba(139, 92, 246, 0.1)',
                    padding: '8px 15px',
                    borderRadius: '8px',
                    borderRight: '3px solid #8b5cf6'
                  }}>
                    <span style={{ color: '#8b5cf6' }}>📅</span>
                    <span style={{ fontWeight: 'bold', color: '#f3f4f6' }}>من:</span>
                    <span>{new Date(filters.dateFrom).toLocaleDateString('en-US')} (08:00 AM)</span>
                  </div>
                )}
                
                {filters.dateTo && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    background: 'rgba(139, 92, 246, 0.1)',
                    padding: '8px 15px',
                    borderRadius: '8px',
                    borderRight: '3px solid #8b5cf6'
                  }}>
                    <span style={{ color: '#8b5cf6' }}>📅</span>
                    <span style={{ fontWeight: 'bold', color: '#f3f4f6' }}>إلى:</span>
                    <span>{new Date(filters.dateTo).toLocaleDateString('en-US')} (07:59 AM next day)</span>
                  </div>
                )}
                
                {filters.source !== 'both' && (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    background: 'rgba(16, 185, 129, 0.1)',
                    padding: '8px 15px',
                    borderRadius: '8px',
                    borderRight: '3px solid #10b981'
                  }}>
                    <span style={{ color: '#10b981' }}>📋</span>
                    <span style={{ fontWeight: 'bold', color: '#f3f4f6' }}>Source:</span>
                    <span>{filters.source === 'weekly' ? 'Weekly Schedule Only' : 'Daily Schedule Only'}</span>
                  </div>
                )}
              </div>
            )}
            
            {reportResults.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '60px 20px',
                color: '#d1d5db',
                background: '#2d3748',
                borderRadius: '10px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
              }}>
                <div style={{ 
                  fontSize: '4rem', 
                  marginBottom: '20px',
                  color: '#6b7280',
                  animation: 'pulse 2s infinite ease-in-out'
                }}>📊</div>
                <p style={{ 
                  fontSize: '1.2rem',
                  maxWidth: '500px',
                  margin: '0 auto',
                  lineHeight: '1.6'
                }}>
                  {t('unified.noResultsMessage')}
                </p>
                <style jsx>{`
                  @keyframes pulse {
                    0% { transform: scale(1); opacity: 0.8; }
                    50% { transform: scale(1.05); opacity: 1; }
                    100% { transform: scale(1); opacity: 0.8; }
                  }
                `}</style>
              </div>
            ) : (
              <div style={{ 
                overflowX: 'auto',
                borderRadius: '10px',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
                background: '#2d3748'
              }}>
                <table style={{
                  width: '100%',
                  borderCollapse: 'collapse',
                  color: 'white'
                }}>
                  <thead>
                    <tr style={{ 
                      background: 'linear-gradient(45deg, #10b981, #34d399)',
                      borderRadius: '10px 10px 0 0'
                    }}>
                      <th style={{
                        padding: '15px',
                        textAlign: 'right',
                        borderBottom: '2px solid #4b5563',
                        borderTopRightRadius: '10px'
                      }}>
                        {t('reports.code')}
                      </th>
                      <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #4b5563' }}>
                        {t('reports.name')}
                      </th>
                      <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #4b5563' }}>
                        {t('reports.type')}
                      </th>
                      <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #4b5563' }}>
                        {t('reports.duration')}
                      </th>
                      <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #4b5563' }}>
                        {t('reports.date')}
                      </th>
                      <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #4b5563' }}>
                        {t('reports.time')}
                      </th>
                      <th style={{ padding: '15px', textAlign: 'right', borderBottom: '2px solid #4b5563' }}>
                        {t('reports.source')}
                      </th>
                      <th style={{ 
                        padding: '15px', 
                        textAlign: 'right', 
                        borderBottom: '2px solid #4b5563',
                        borderTopLeftRadius: '10px'
                      }}>
                        نوع البث
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportResults.map((item, index) => {
                      // Determine row background color based on media type
                      let rowBackground;
                      switch(item.mediaType) {
                        case 'PROGRAM':
                          rowBackground = 'rgba(59, 130, 246, 0.05)';
                          break;
                        case 'SERIES':
                          rowBackground = 'rgba(16, 185, 129, 0.05)';
                          break;
                        case 'MOVIE':
                          rowBackground = 'rgba(245, 158, 11, 0.05)';
                          break;
                        case 'PROMO':
                          rowBackground = 'rgba(139, 92, 246, 0.05)';
                          break;
                        case 'COMMERCIAL':
                          rowBackground = 'rgba(236, 72, 153, 0.05)';
                          break;
                        default:
                          rowBackground = index % 2 === 0 ? '#2d3748' : '#374151';
                      }
                      
                      return (
                        <tr 
                          key={item.id + index}
                          style={{
                            background: rowBackground,
                            borderBottom: '1px solid #4b5563',
                            transition: 'background 0.3s ease'
                          }}
                          onMouseOver={(e) => {
                            e.currentTarget.style.background = 'rgba(75, 85, 99, 0.2)';
                          }}
                          onMouseOut={(e) => {
                            e.currentTarget.style.background = rowBackground;
                          }}
                        >
                          <td style={{
                            padding: '15px',
                            fontWeight: 'bold',
                            color: '#60a5fa',
                            maxWidth: '150px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {item.mediaCode || item.mediaId || 'Not specified'}
                          </td>
                          <td style={{
                            padding: '15px',
                            fontWeight: 'bold',
                            maxWidth: '250px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}>
                            {item.mediaName}
                          </td>
                          <td style={{ padding: '15px' }}>
                            <span style={{
                              display: 'inline-block',
                              background: `${
                                item.mediaType === 'PROGRAM' ? 'rgba(59, 130, 246, 0.2)' :
                                item.mediaType === 'SERIES' ? 'rgba(16, 185, 129, 0.2)' :
                                item.mediaType === 'MOVIE' ? 'rgba(245, 158, 11, 0.2)' :
                                item.mediaType === 'PROMO' ? 'rgba(139, 92, 246, 0.2)' :
                                item.mediaType === 'COMMERCIAL' ? 'rgba(236, 72, 153, 0.2)' : 'rgba(107, 114, 128, 0.2)'
                              }`,
                              color: `${
                                item.mediaType === 'PROGRAM' ? '#3b82f6' :
                                item.mediaType === 'SERIES' ? '#10b981' :
                                item.mediaType === 'MOVIE' ? '#f59e0b' :
                                item.mediaType === 'PROMO' ? '#8b5cf6' :
                                item.mediaType === 'COMMERCIAL' ? '#ec4899' : '#6b7280'
                              }`,
                              padding: '5px 10px',
                              borderRadius: '20px',
                              fontWeight: 'bold',
                              fontSize: '0.9rem'
                            }}>
                              {translateMediaType(item.mediaType)}
                            </span>
                          </td>
                          <td style={{ padding: '15px', textAlign: 'center' }}>
                            <span style={{
                              background: '#374151',
                              padding: '5px 10px',
                              borderRadius: '20px',
                              fontWeight: 'bold',
                              color: '#10b981'
                            }}>
                              {item.mediaDuration}
                            </span>
                          </td>
                          <td style={{ padding: '15px' }}>
                            {formatDate(item.date)}
                          </td>
                          <td style={{ padding: '15px', textAlign: 'center' }}>
                            <span style={{
                              background: '#374151',
                              padding: '5px 10px',
                              borderRadius: '20px',
                              fontWeight: 'bold'
                            }}>
                              {item.time}
                            </span>
                          </td>
                          <td style={{ padding: '15px', textAlign: 'center' }}>
                            <span style={{
                              background: item.source === 'weekly' ? 'rgba(59, 130, 246, 0.2)' : 'rgba(16, 185, 129, 0.2)',
                              color: item.source === 'weekly' ? '#3b82f6' : '#10b981',
                              padding: '5px 10px',
                              borderRadius: '20px',
                              fontWeight: 'bold',
                              fontSize: '0.9rem',
                              display: 'inline-flex',
                              alignItems: 'center',
                              gap: '5px'
                            }}>
                              <span>{item.source === 'weekly' ? '📊' : '📋'}</span>
                              {item.source === 'weekly' ? t('reports.weekly') : t('reports.daily')}
                            </span>
                          </td>
                          <td style={{ padding: '15px', textAlign: 'center' }}>
                            <span style={{
                              background: item.isRerun ? 'rgba(245, 158, 11, 0.2)' : 
                                        (item.isTemporary ? 'rgba(139, 92, 246, 0.2)' : 'rgba(16, 185, 129, 0.2)'),
                              color: item.isRerun ? '#f59e0b' : 
                                    (item.isTemporary ? '#8b5cf6' : '#10b981'),
                              padding: '5px 10px',
                              borderRadius: '20px',
                              fontWeight: 'bold',
                              fontSize: '0.9rem',
                              display: 'inline-flex',
                              alignItems: 'center',
                              gap: '5px'
                            }}>
                              <span>{item.isRerun ? '♻️' : (item.isTemporary ? '🔄' : '🌟')}</span>
                              {item.isRerun ? t('reports.rerun') : (item.isTemporary ? t('reports.temporary') : t('reports.original'))}
                            </span>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
        <ToastContainer />
      </DashboardLayout>
    </AuthGuard>
  );
}