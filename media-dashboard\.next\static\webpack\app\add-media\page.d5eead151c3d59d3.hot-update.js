"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast)();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        description: '',\n        channel: '',\n        source: '',\n        status: '',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: '',\n        showInTX: false\n    });\n    // إضافة حالة لإظهار/إخفاء الحقول الخاصة حسب نوع المادة\n    const [showEpisodeNumber, setShowEpisodeNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSeasonNumber, setShowSeasonNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPartNumber, setShowPartNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث الحقول المرئية عند تغيير نوع المادة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddMediaPage.useEffect\": ()=>{\n            console.log('🔍 نوع المادة المختار:', formData.type);\n            if (formData.type === 'FILM') {\n                // فيلم: يظهر رقم الجزء فقط\n                console.log('✅ عرض حقول الفيلم: رقم الجزء فقط');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'SERIES') {\n                // مسلسل: يظهر رقم الحلقة ورقم الجزء\n                console.log('✅ عرض حقول المسلسل: رقم الحلقة + رقم الجزء');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'PROGRAM') {\n                // برنامج: يظهر رقم الحلقة ورقم الموسم\n                console.log('✅ عرض حقول البرنامج: رقم الحلقة + رقم الموسم');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(true);\n                setShowPartNumber(false);\n            } else {\n                // باقي الأنواع: لا تظهر حقول إضافية\n                console.log('❌ إخفاء جميع الحقول الإضافية');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n            }\n            console.log('📊 حالة الحقول:', {\n                showEpisodeNumber,\n                showSeasonNumber,\n                showPartNumber\n            });\n        }\n    }[\"AddMediaPage.useEffect\"], [\n        formData.type\n    ]);\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: '',\n            timeIn: '00:00:00',\n            timeOut: '',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: field === 'showInTX' ? value === 'true' : value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n        // التحقق من كود السيجمانت عند تغييره\n        if (field === 'segmentCode' && value.trim()) {\n            validateSegmentCode(segmentId, value.trim());\n        } else if (field === 'segmentCode' && !value.trim()) {\n            // إزالة التحقق إذا كان الكود فارغ\n            setSegmentCodeValidation((prev)=>{\n                const newValidation = {\n                    ...prev\n                };\n                delete newValidation[segmentId];\n                return newValidation;\n            });\n        }\n    };\n    // دالة التحقق من كود السيجمانت\n    const validateSegmentCode = async (segmentId, code)=>{\n        // تحديث حالة التحقق\n        setSegmentCodeValidation((prev)=>({\n                ...prev,\n                [segmentId]: {\n                    isValid: true,\n                    message: '',\n                    isChecking: true\n                }\n            }));\n        try {\n            const response = await fetch(\"/api/validate-segment-code?code=\".concat(encodeURIComponent(code)));\n            const result = await response.json();\n            if (result.success) {\n                const isValid = !result.isDuplicate;\n                const message = result.isDuplicate ? \"الكود موجود في: \".concat(result.duplicates.map((d)=>d.mediaName).join(', ')) : 'الكود متاح';\n                setSegmentCodeValidation((prev)=>({\n                        ...prev,\n                        [segmentId]: {\n                            isValid,\n                            message,\n                            isChecking: false\n                        }\n                    }));\n            }\n        } catch (error) {\n            console.error('Error validating segment code:', error);\n            setSegmentCodeValidation((prev)=>({\n                    ...prev,\n                    [segmentId]: {\n                        isValid: true,\n                        message: 'خطأ في التحقق',\n                        isChecking: false\n                    }\n                }));\n        }\n    };\n    const calculateDuration = (segmentId)=>{\n        const segment = segments.find((s)=>s.id === segmentId);\n        if (!segment || !segment.timeIn || !segment.timeOut) return;\n        try {\n            const timeIn = segment.timeIn.split(':').map(Number);\n            const timeOut = segment.timeOut.split(':').map(Number);\n            // تحويل إلى ثواني\n            const inSeconds = timeIn[0] * 3600 + timeIn[1] * 60 + timeIn[2];\n            const outSeconds = timeOut[0] * 3600 + timeOut[1] * 60 + timeOut[2];\n            // حساب الفرق\n            let durationSeconds = outSeconds - inSeconds;\n            if (durationSeconds < 0) {\n                durationSeconds += 24 * 3600; // إضافة يوم كامل إذا كان الوقت سالب\n            }\n            // تحويل إلى تنسيق HH:MM:SS\n            const hours = Math.floor(durationSeconds / 3600);\n            const minutes = Math.floor(durationSeconds % 3600 / 60);\n            const seconds = durationSeconds % 60;\n            const duration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n            handleSegmentChange(segmentId, 'duration', duration);\n        } catch (error) {\n            console.error('Error calculating duration:', error);\n        }\n    };\n    const addSegment = ()=>{\n        const newId = segmentCount + 1;\n        setSegmentCount(newId);\n        setSegments((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    segmentCode: '',\n                    timeIn: '00:00:00',\n                    timeOut: '00:00:00',\n                    duration: '00:00:00'\n                }\n            ]);\n    };\n    const removeSegment = (id)=>{\n        if (segments.length <= 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegments((prev)=>prev.filter((segment)=>segment.id !== id));\n    };\n    const setSegmentCount2 = (count)=>{\n        if (count < 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            newSegments.push({\n                id: i,\n                segmentCode: '',\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [segmentCodeValidation, setSegmentCodeValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // التحقق من الحقول المطلوبة\n        const requiredFields = [];\n        if (!formData.hardDiskNumber.trim()) requiredFields.push('رقم الهارد');\n        if (!formData.type) requiredFields.push('نوع المادة');\n        if (!formData.channel) requiredFields.push('القناة');\n        if (!formData.status) requiredFields.push('الحالة');\n        // التحقق من كود السيجمانت\n        const segmentsWithoutCode = segments.filter((segment)=>!segment.segmentCode || segment.segmentCode.trim() === '');\n        if (segmentsWithoutCode.length > 0) {\n            requiredFields.push('كود السيجمانت (مطلوب لجميع السيجمانتات)');\n        }\n        // التحقق من Time Out للسيجمانت\n        const invalidSegments = segments.filter((segment)=>!segment.timeOut || segment.timeOut === '00:00:00');\n        if (invalidSegments.length > 0) {\n            requiredFields.push('Time Out للسيجمانت');\n        }\n        if (requiredFields.length > 0) {\n            showErrorToast('invalidData');\n            return;\n        }\n        // التحقق من تكرار أكواد السيجمانت\n        const segmentCodes = segments.map((s)=>s.segmentCode).filter((code)=>code && code.trim());\n        try {\n            const validationResponse = await fetch('/api/validate-segment-code', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    segmentCodes\n                })\n            });\n            const validationResult = await validationResponse.json();\n            if (validationResult.success && validationResult.hasAnyDuplicates) {\n                let errorMessage = 'تم العثور على أكواد مكررة:\\n\\n';\n                if (validationResult.hasInternalDuplicates) {\n                    errorMessage += \"\\uD83D\\uDD34 أكواد مكررة داخل نفس المادة: \".concat(validationResult.internalDuplicates.join(', '), \"\\n\\n\");\n                }\n                if (validationResult.hasExternalDuplicates) {\n                    errorMessage += '🔴 أكواد موجودة في مواد أخرى:\\n';\n                    validationResult.externalDuplicates.forEach((dup)=>{\n                        errorMessage += '- الكود \"'.concat(dup.segmentCode, '\" موجود في المادة: ').concat(dup.mediaName, \" (\").concat(dup.mediaType, \")\\n\");\n                    });\n                }\n                errorMessage += '\\nيرجى تغيير الأكواد المكررة قبل الحفظ.';\n                showErrorToast('invalidData');\n                return;\n            }\n        } catch (validationError) {\n            console.error('Error validating segment codes:', validationError);\n            showErrorToast('invalidData');\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = \"token_\".concat(user.id || 'unknown', \"_\").concat(user.role || 'unknown');\n            console.log('Sending with token:', tokenWithRole);\n            console.log('User data:', user);\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(tokenWithRole)\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('mediaAdded');\n                // مسح جميع الحقول للبدء من جديد\n                setFormData({\n                    name: '',\n                    type: '',\n                    description: '',\n                    channel: '',\n                    source: '',\n                    status: '',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: '',\n                    showInTX: false\n                });\n                // مسح السيجمانتات والعودة لسيجمانت واحد\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: '',\n                        timeIn: '00:00:00',\n                        timeOut: '',\n                        duration: ''\n                    }\n                ]);\n                // إخفاء الحقول الخاصة\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n                console.log('✅ تم مسح جميع الحقول بنجاح');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error saving media:', error);\n            showErrorToast('serverConnection');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // أنماط CSS\n    const inputStyle = {\n        width: '100%',\n        padding: '10px',\n        borderRadius: '5px',\n        marginBottom: '10px'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: t('media.addNew'),\n        subtitle: t('media.title'),\n        icon: \"➕\",\n        requiredPermissions: [\n            'MEDIA_CREATE'\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '15px',\n                    padding: '20px',\n                    marginBottom: '25px',\n                    border: '1px solid #6b7280',\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#ffffff',\n                            marginBottom: '10px',\n                            fontSize: '1.4rem',\n                            fontWeight: 'bold'\n                        },\n                        children: [\n                            \"➕ \",\n                            t('addMedia.title')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#e2e8f0',\n                            fontSize: '0.95rem',\n                            margin: 0\n                        },\n                        children: t('addMedia.subtitle')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 376,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDD \",\n                                    t('addMedia.basicInfo')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 1fr',\n                                            gap: '15px',\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    fontWeight: 'bold',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCBE \",\n                                                    t('addMedia.hardDiskNumber'),\n                                                    \": \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 54\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('addMedia.hardDiskNumber'),\n                                                value: formData.hardDiskNumber,\n                                                onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    maxWidth: '200px',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '500px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    t('common.name'),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 38\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('common.name'),\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                },\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('common.type'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 40\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectType')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILM\",\n                                                                children: t('mediaTypes.FILM')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: t('mediaTypes.SERIES')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: t('mediaTypes.PROGRAM')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: t('mediaTypes.SONG')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: t('mediaTypes.FILLER')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: t('mediaTypes.STING')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: t('mediaTypes.PROMO')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEXT\",\n                                                                children: t('mediaTypes.NEXT')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NOW\",\n                                                                children: t('mediaTypes.NOW')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"سنعود\",\n                                                                children: t('mediaTypes.سنعود')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"عدنا\",\n                                                                children: t('mediaTypes.عدنا')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MINI\",\n                                                                children: t('mediaTypes.MINI')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CROSS\",\n                                                                children: t('mediaTypes.CROSS')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('addMedia.channel'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectChannel')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: t('channels.DOCUMENTARY')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: t('channels.NEWS')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: t('channels.OTHER')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('common.status'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectStatus')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: \"صالح\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_CENSORSHIP\",\n                                                                children: \"مرفوض رقابياً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_TECHNICAL\",\n                                                                children: \"مرفوض هندسياً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: \"منتهى\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"HOLD\",\n                                                                children: \"Hold\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.source')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: t('addMedia.source'),\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.startDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.startDate,\n                                                        onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.endDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.endDate,\n                                                        onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            showEpisodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.episodeNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.episodeNumber'),\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, this),\n                                            showSeasonNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.seasonNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.seasonNumber'),\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 19\n                                            }, this),\n                                            showPartNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.partNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.partNumber'),\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.description')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.description'),\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.notes')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.additionalNotes'),\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.showInTX,\n                                                        onChange: (e)=>handleInputChange('showInTX', e.target.checked.toString()),\n                                                        style: {\n                                                            marginLeft: '10px',\n                                                            width: '18px',\n                                                            height: '18px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            background: '#10b981',\n                                                            color: 'white',\n                                                            padding: '2px 8px',\n                                                            borderRadius: '4px',\n                                                            marginLeft: '10px',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"TX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t('addMedia.showInSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '0.8rem',\n                                                    color: '#9ca3af',\n                                                    marginTop: '5px',\n                                                    marginRight: '35px'\n                                                },\n                                                children: t('addMedia.txDescription')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('addMedia.segments')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: '#3b82f6',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    padding: '8px 15px',\n                                                    marginLeft: '10px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: t('addMedia.addSegment')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                value: segmentCount,\n                                                onChange: (e)=>setSegmentCount2(parseInt(e.target.value)),\n                                                style: {\n                                                    width: '60px',\n                                                    padding: '8px',\n                                                    borderRadius: '5px',\n                                                    border: '1px solid #6b7280',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    textAlign: 'center'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 13\n                            }, this),\n                            segments.map((segment, index)=>{\n                                var _segmentCodeValidation_segment_id, _segmentCodeValidation_segment_id1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#374151',\n                                        borderRadius: '10px',\n                                        padding: '15px',\n                                        marginBottom: '15px',\n                                        border: '1px solid #4b5563'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                justifyContent: 'space-between',\n                                                alignItems: 'center',\n                                                marginBottom: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#f3f4f6',\n                                                        margin: 0\n                                                    },\n                                                    children: [\n                                                        t('addMedia.segment'),\n                                                        \" \",\n                                                        segment.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeSegment(segment.id),\n                                                    style: {\n                                                        background: '#ef4444',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '5px',\n                                                        padding: '5px 10px',\n                                                        cursor: 'pointer'\n                                                    },\n                                                    children: t('common.delete')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'grid',\n                                                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                gap: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.segmentCode')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: 'relative'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: segment.segmentCode,\n                                                                    onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                    placeholder: \"مثال: DDC000055-P1-3\",\n                                                                    required: true,\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: segment.segmentCode ? '#1f2937' : '#7f1d1d',\n                                                                        color: 'white',\n                                                                        border: ((_segmentCodeValidation_segment_id = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id === void 0 ? void 0 : _segmentCodeValidation_segment_id.isValid) === false ? '2px solid #ef4444' : segment.segmentCode ? ((_segmentCodeValidation_segment_id1 = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id1 === void 0 ? void 0 : _segmentCodeValidation_segment_id1.isValid) === true ? '2px solid #10b981' : '1px solid #6b7280' : '2px solid #ef4444',\n                                                                        paddingRight: segmentCodeValidation[segment.id] ? '35px' : '12px'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                segmentCodeValidation[segment.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: 'absolute',\n                                                                        right: '8px',\n                                                                        top: '50%',\n                                                                        transform: 'translateY(-50%)',\n                                                                        fontSize: '16px'\n                                                                    },\n                                                                    children: segmentCodeValidation[segment.id].isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#fbbf24'\n                                                                        },\n                                                                        children: \"⏳\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 829,\n                                                                        columnNumber: 29\n                                                                    }, this) : segmentCodeValidation[segment.id].isValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#10b981'\n                                                                        },\n                                                                        children: \"✅\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#ef4444'\n                                                                        },\n                                                                        children: \"❌\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        segmentCodeValidation[segment.id] && segmentCodeValidation[segment.id].message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                marginTop: '4px',\n                                                                color: segmentCodeValidation[segment.id].isValid ? '#10b981' : '#ef4444'\n                                                            },\n                                                            children: segmentCodeValidation[segment.id].message\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: \"Time In\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: [\n                                                                \"Time Out \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        color: '#ef4444'\n                                                                    },\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 869,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            onFocus: (e)=>{\n                                                                // إزالة القيمة الوهمية عند النقر\n                                                                if (e.target.value === '00:00:00') {\n                                                                    handleSegmentChange(segment.id, 'timeOut', '');\n                                                                }\n                                                            },\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.durationAutoCalculated')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.duration,\n                                                            readOnly: true,\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280',\n                                                                opacity: '0.7',\n                                                                cursor: 'not-allowed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, segment.id, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'center',\n                            gap: '15px',\n                            marginTop: '20px',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#10b981',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: isSubmitting ? \"⏳ \".concat(t('common.saving')) : \"\\uD83D\\uDCBE \".concat(t('addMedia.saveAndAddNew'))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>router.push('/media-list'),\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#3b82f6',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCB \",\n                                    t('navigation.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    // مسح جميع الحقول يدوياً\n                                    setFormData({\n                                        name: '',\n                                        type: '',\n                                        description: '',\n                                        channel: '',\n                                        source: '',\n                                        status: '',\n                                        startDate: new Date().toISOString().split('T')[0],\n                                        endDate: '',\n                                        notes: '',\n                                        episodeNumber: '',\n                                        seasonNumber: '',\n                                        partNumber: '',\n                                        hardDiskNumber: '',\n                                        showInTX: false\n                                    });\n                                    setSegmentCount(1);\n                                    setSegments([\n                                        {\n                                            id: 1,\n                                            segmentCode: '',\n                                            timeIn: '00:00:00',\n                                            timeOut: '',\n                                            duration: ''\n                                        }\n                                    ]);\n                                    setShowEpisodeNumber(false);\n                                    setShowSeasonNumber(false);\n                                    setShowPartNumber(false);\n                                    showSuccessToast('changesSaved');\n                                },\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#f59e0b',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDDD1️ \",\n                                    t('addMedia.clearFields')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 955,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 392,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 1011,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"zD8bHefytEBpMmtUNRBoYQorTRw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});