"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/NavigationCard.tsx":
/*!*******************************************!*\
  !*** ./src/components/NavigationCard.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NavigationCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n// خلفية مطابقة للشاشة مع حدود ضوئية ملونة\nconst getIconColors = (icon)=>{\n    switch(icon){\n        case '🎬':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(139, 92, 246, 0.3)',\n                hoverShadow: 'rgba(139, 92, 246, 0.8)',\n                hoverBorder: '#8B5CF6',\n                glowColor: '#8B5CF6',\n                iconBackground: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%)',\n                iconShadow: 'rgba(139, 92, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(139, 92, 246, 0.3)'\n            };\n        case '➕':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(6, 182, 212, 0.3)',\n                hoverShadow: 'rgba(6, 182, 212, 0.8)',\n                hoverBorder: '#06B6D4',\n                glowColor: '#06B6D4',\n                iconBackground: 'linear-gradient(135deg, #06B6D4 0%, #0891B2 50%, #0E7490 100%)',\n                iconShadow: 'rgba(6, 182, 212, 0.4)',\n                borderGlow: '0 0 20px rgba(6, 182, 212, 0.3)'\n            };\n        case '🔄':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(249, 115, 22, 0.3)',\n                hoverShadow: 'rgba(249, 115, 22, 0.8)',\n                hoverBorder: '#F97316',\n                glowColor: '#F97316',\n                iconBackground: 'linear-gradient(135deg, #F97316 0%, #EA580C 50%, #DC2626 100%)',\n                iconShadow: 'rgba(249, 115, 22, 0.4)',\n                borderGlow: '0 0 20px rgba(249, 115, 22, 0.3)'\n            };\n        case '📅':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(59, 130, 246, 0.3)',\n                hoverShadow: 'rgba(59, 130, 246, 0.8)',\n                hoverBorder: '#3B82F6',\n                glowColor: '#3B82F6',\n                iconBackground: 'linear-gradient(135deg, #3B82F6 0%, #2563EB 50%, #1D4ED8 100%)',\n                iconShadow: 'rgba(59, 130, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(59, 130, 246, 0.3)'\n            };\n        case '📊':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(16, 185, 129, 0.3)',\n                hoverShadow: 'rgba(16, 185, 129, 0.8)',\n                hoverBorder: '#10B981',\n                glowColor: '#10B981',\n                iconBackground: 'linear-gradient(135deg, #10B981 0%, #059669 50%, #047857 100%)',\n                iconShadow: 'rgba(16, 185, 129, 0.4)',\n                borderGlow: '0 0 20px rgba(16, 185, 129, 0.3)'\n            };\n        case '📋':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(239, 68, 68, 0.3)',\n                hoverShadow: 'rgba(239, 68, 68, 0.8)',\n                hoverBorder: '#EF4444',\n                glowColor: '#EF4444',\n                iconBackground: 'linear-gradient(135deg, #EF4444 0%, #DC2626 50%, #B91C1C 100%)',\n                iconShadow: 'rgba(239, 68, 68, 0.4)',\n                borderGlow: '0 0 20px rgba(239, 68, 68, 0.3)'\n            };\n        case '👥':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(245, 158, 11, 0.3)',\n                hoverShadow: 'rgba(245, 158, 11, 0.8)',\n                hoverBorder: '#F59E0B',\n                glowColor: '#F59E0B',\n                iconBackground: 'linear-gradient(135deg, #F59E0B 0%, #D97706 50%, #B45309 100%)',\n                iconShadow: 'rgba(245, 158, 11, 0.4)',\n                borderGlow: '0 0 20px rgba(245, 158, 11, 0.3)'\n            };\n        case '📈':\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(236, 72, 153, 0.3)',\n                hoverShadow: 'rgba(236, 72, 153, 0.8)',\n                hoverBorder: '#EC4899',\n                glowColor: '#EC4899',\n                iconBackground: 'linear-gradient(135deg, #EC4899 0%, #DB2777 50%, #BE185D 100%)',\n                iconShadow: 'rgba(236, 72, 153, 0.4)',\n                borderGlow: '0 0 20px rgba(236, 72, 153, 0.3)'\n            };\n        default:\n            return {\n                background: 'rgba(17, 24, 39, 0.8)',\n                shadow: 'rgba(0, 0, 0, 0.3)',\n                border: 'rgba(139, 92, 246, 0.3)',\n                hoverShadow: 'rgba(139, 92, 246, 0.8)',\n                hoverBorder: '#8B5CF6',\n                glowColor: '#8B5CF6',\n                iconBackground: 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 50%, #6D28D9 100%)',\n                iconShadow: 'rgba(139, 92, 246, 0.4)',\n                borderGlow: '0 0 20px rgba(139, 92, 246, 0.3)'\n            };\n    }\n};\nfunction NavigationCard(param) {\n    let { icon, title, subtitle, path, permission, adminOnly = false, height = 'auto' } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { i18n } = useTranslation('common');\n    // Get current language and direction\n    const currentLang = i18n.language || 'ar';\n    const isRTL = currentLang === 'ar';\n    // التحقق من الصلاحيات\n    if (adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') {\n        return null;\n    }\n    if (permission && !hasPermission(permission)) {\n        return null;\n    }\n    const handleClick = ()=>{\n        router.push(path);\n    };\n    const iconColors = getIconColors(icon);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onClick: handleClick,\n        style: {\n            background: iconColors.background,\n            borderRadius: '20px',\n            padding: '30px',\n            border: \"3px solid \".concat(iconColors.border),\n            position: 'relative',\n            overflow: 'hidden',\n            cursor: 'pointer',\n            transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n            transform: 'translateZ(0)',\n            height: height,\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            textAlign: 'center',\n            boxShadow: \"0 10px 30px \".concat(iconColors.shadow, \", \").concat(iconColors.borderGlow, \", inset 0 1px 0 rgba(255,255,255,0.1)\"),\n            direction: isRTL ? 'rtl' : 'ltr',\n            backdropFilter: 'blur(10px)',\n            WebkitBackdropFilter: 'blur(10px)'\n        },\n        onMouseEnter: (e)=>{\n            e.currentTarget.style.transform = 'translateY(-12px) scale(1.03)';\n            e.currentTarget.style.boxShadow = \"0 25px 50px \".concat(iconColors.hoverShadow, \", 0 0 40px \").concat(iconColors.hoverShadow, \", 0 0 80px \").concat(iconColors.hoverShadow, \", inset 0 1px 0 rgba(255,255,255,0.2)\");\n            e.currentTarget.style.border = \"3px solid \".concat(iconColors.hoverBorder);\n            e.currentTarget.style.background = \"rgba(17, 24, 39, 0.95)\";\n            // تأثير الإضاءة على الأيقونة\n            const iconElement = e.currentTarget.querySelector('.card-icon');\n            if (iconElement) {\n                iconElement.style.transform = 'scale(1.2) rotateY(5deg)';\n                iconElement.style.boxShadow = \"0 12px 35px \".concat(iconColors.iconShadow, \", 0 0 30px \").concat(iconColors.glowColor, \", 0 0 50px \").concat(iconColors.glowColor, \", inset 0 2px 8px rgba(255,255,255,0.3)\");\n                iconElement.style.filter = \"brightness(1.4) contrast(1.3) drop-shadow(0 0 25px \".concat(iconColors.glowColor, \")\");\n            }\n        },\n        onMouseLeave: (e)=>{\n            e.currentTarget.style.transform = 'translateY(0) scale(1)';\n            e.currentTarget.style.boxShadow = \"0 10px 30px \".concat(iconColors.shadow, \", \").concat(iconColors.borderGlow, \", inset 0 1px 0 rgba(255,255,255,0.1)\");\n            e.currentTarget.style.border = \"3px solid \".concat(iconColors.border);\n            e.currentTarget.style.background = iconColors.background;\n            // إعادة الأيقونة لحالتها الطبيعية\n            const iconElement = e.currentTarget.querySelector('.card-icon');\n            if (iconElement) {\n                iconElement.style.transform = 'scale(1) rotateY(0deg)';\n                iconElement.style.boxShadow = \"0 8px 25px \".concat(iconColors.iconShadow, \", 0 4px 15px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255,255,255,0.2)\");\n                iconElement.style.filter = 'brightness(1.1) contrast(1.1) drop-shadow(0 2px 4px rgba(0,0,0,0.3))';\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card-icon\",\n                style: {\n                    position: 'absolute',\n                    top: '25px',\n                    [isRTL ? 'right' : 'left']: '25px',\n                    width: '75px',\n                    height: '75px',\n                    background: iconColors.iconBackground,\n                    borderRadius: '20px',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    fontSize: '2.4rem',\n                    border: '3px solid rgba(255, 255, 255, 0.4)',\n                    boxShadow: \"0 8px 25px \".concat(iconColors.iconShadow, \", 0 4px 15px rgba(0, 0, 0, 0.3), inset 0 2px 4px rgba(255,255,255,0.2)\"),\n                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',\n                    backdropFilter: 'blur(10px)',\n                    WebkitBackdropFilter: 'blur(10px)',\n                    transformStyle: 'preserve-3d'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    style: {\n                        filter: 'brightness(1.3) contrast(1.2) drop-shadow(0 2px 4px rgba(0,0,0,0.3))',\n                        textShadow: '0 2px 8px rgba(0,0,0,0.4)',\n                        color: 'white',\n                        transition: 'all 0.3s ease'\n                    },\n                    children: icon\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: 'flex',\n                    flexDirection: 'column',\n                    justifyContent: 'center',\n                    alignItems: 'center',\n                    height: '100%',\n                    paddingTop: '20px'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.7rem',\n                            fontWeight: '600',\n                            color: 'rgba(255, 255, 255, 0.95)',\n                            marginBottom: '12px',\n                            textShadow: '0 2px 12px rgba(0,0,0,0.8), 0 0 20px rgba(255,255,255,0.1)',\n                            textAlign: 'center',\n                            letterSpacing: '0.5px',\n                            transition: 'all 0.3s ease'\n                        },\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: 'rgba(255, 255, 255, 0.75)',\n                            fontSize: '1.1rem',\n                            lineHeight: '1.5',\n                            textShadow: '0 1px 6px rgba(0,0,0,0.6)',\n                            textAlign: 'center',\n                            opacity: 0.9,\n                            transition: 'all 0.3s ease'\n                        },\n                        children: subtitle\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: '0',\n                    left: '0',\n                    right: '0',\n                    height: '2px',\n                    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.6), transparent)',\n                    transform: 'translateX(-100%)',\n                    transition: 'transform 0.6s ease'\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\NavigationCard.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_s(NavigationCard, \"Bc/aYeUZbhNEmAfuZtp6ihsSHwc=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = NavigationCard;\nvar _c;\n$RefreshReg$(_c, \"NavigationCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/NavigationCard.tsx\n"));

/***/ })

});