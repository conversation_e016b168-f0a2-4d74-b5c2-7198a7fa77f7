"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-media/page",{

/***/ "(app-pages-browser)/./src/app/add-media/page.tsx":
/*!************************************!*\
  !*** ./src/app/add-media/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddMediaPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTranslatedToast */ \"(app-pages-browser)/./src/hooks/useTranslatedToast.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DashboardLayout */ \"(app-pages-browser)/./src/components/DashboardLayout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction AddMediaPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { showSuccessToast, showErrorToast, ToastContainer } = (0,_hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast)();\n    const { t, tMediaType, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        type: '',\n        description: '',\n        channel: '',\n        source: '',\n        status: '',\n        startDate: new Date().toISOString().split('T')[0],\n        endDate: '',\n        notes: '',\n        episodeNumber: '',\n        seasonNumber: '',\n        partNumber: '',\n        hardDiskNumber: '',\n        showInTX: false\n    });\n    // إضافة حالة لإظهار/إخفاء الحقول الخاصة حسب نوع المادة\n    const [showEpisodeNumber, setShowEpisodeNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSeasonNumber, setShowSeasonNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showPartNumber, setShowPartNumber] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // تحديث الحقول المرئية عند تغيير نوع المادة\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddMediaPage.useEffect\": ()=>{\n            console.log('🔍 نوع المادة المختار:', formData.type);\n            if (formData.type === 'FILM') {\n                // فيلم: يظهر رقم الجزء فقط\n                console.log('✅ عرض حقول الفيلم: رقم الجزء فقط');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'SERIES') {\n                // مسلسل: يظهر رقم الحلقة ورقم الجزء\n                console.log('✅ عرض حقول المسلسل: رقم الحلقة + رقم الجزء');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(false);\n                setShowPartNumber(true);\n            } else if (formData.type === 'PROGRAM') {\n                // برنامج: يظهر رقم الحلقة ورقم الموسم\n                console.log('✅ عرض حقول البرنامج: رقم الحلقة + رقم الموسم');\n                setShowEpisodeNumber(true);\n                setShowSeasonNumber(true);\n                setShowPartNumber(false);\n            } else {\n                // باقي الأنواع: لا تظهر حقول إضافية\n                console.log('❌ إخفاء جميع الحقول الإضافية');\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n            }\n            console.log('📊 حالة الحقول:', {\n                showEpisodeNumber,\n                showSeasonNumber,\n                showPartNumber\n            });\n        }\n    }[\"AddMediaPage.useEffect\"], [\n        formData.type\n    ]);\n    const [segmentCount, setSegmentCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [segments, setSegments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: 1,\n            segmentCode: '',\n            timeIn: '00:00:00',\n            timeOut: '',\n            duration: '00:00:00'\n        }\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: field === 'showInTX' ? value === 'true' : value\n            }));\n    };\n    const handleSegmentChange = (segmentId, field, value)=>{\n        setSegments((prev)=>prev.map((segment)=>segment.id === segmentId ? {\n                    ...segment,\n                    [field]: value\n                } : segment));\n        // التحقق من كود السيجمانت عند تغييره\n        if (field === 'segmentCode' && value.trim()) {\n            validateSegmentCode(segmentId, value.trim());\n        } else if (field === 'segmentCode' && !value.trim()) {\n            // إزالة التحقق إذا كان الكود فارغ\n            setSegmentCodeValidation((prev)=>{\n                const newValidation = {\n                    ...prev\n                };\n                delete newValidation[segmentId];\n                return newValidation;\n            });\n        }\n    };\n    // دالة التحقق من كود السيجمانت\n    const validateSegmentCode = async (segmentId, code)=>{\n        // تحديث حالة التحقق\n        setSegmentCodeValidation((prev)=>({\n                ...prev,\n                [segmentId]: {\n                    isValid: true,\n                    message: '',\n                    isChecking: true\n                }\n            }));\n        try {\n            const response = await fetch(\"/api/validate-segment-code?code=\".concat(encodeURIComponent(code)));\n            const result = await response.json();\n            if (result.success) {\n                const isValid = !result.isDuplicate;\n                const message = result.isDuplicate ? \"الكود موجود في: \".concat(result.duplicates.map((d)=>d.mediaName).join(', ')) : 'الكود متاح';\n                setSegmentCodeValidation((prev)=>({\n                        ...prev,\n                        [segmentId]: {\n                            isValid,\n                            message,\n                            isChecking: false\n                        }\n                    }));\n            }\n        } catch (error) {\n            console.error('Error validating segment code:', error);\n            setSegmentCodeValidation((prev)=>({\n                    ...prev,\n                    [segmentId]: {\n                        isValid: true,\n                        message: 'خطأ في التحقق',\n                        isChecking: false\n                    }\n                }));\n        }\n    };\n    const calculateDuration = (segmentId)=>{\n        const segment = segments.find((s)=>s.id === segmentId);\n        if (!segment || !segment.timeIn || !segment.timeOut) return;\n        try {\n            const timeIn = segment.timeIn.split(':').map(Number);\n            const timeOut = segment.timeOut.split(':').map(Number);\n            // تحويل إلى ثواني\n            const inSeconds = timeIn[0] * 3600 + timeIn[1] * 60 + timeIn[2];\n            const outSeconds = timeOut[0] * 3600 + timeOut[1] * 60 + timeOut[2];\n            // حساب الفرق\n            let durationSeconds = outSeconds - inSeconds;\n            if (durationSeconds < 0) {\n                durationSeconds += 24 * 3600; // إضافة يوم كامل إذا كان الوقت سالب\n            }\n            // تحويل إلى تنسيق HH:MM:SS\n            const hours = Math.floor(durationSeconds / 3600);\n            const minutes = Math.floor(durationSeconds % 3600 / 60);\n            const seconds = durationSeconds % 60;\n            const duration = \"\".concat(hours.toString().padStart(2, '0'), \":\").concat(minutes.toString().padStart(2, '0'), \":\").concat(seconds.toString().padStart(2, '0'));\n            handleSegmentChange(segmentId, 'duration', duration);\n        } catch (error) {\n            console.error('Error calculating duration:', error);\n        }\n    };\n    const addSegment = ()=>{\n        const newId = segmentCount + 1;\n        setSegmentCount(newId);\n        setSegments((prev)=>[\n                ...prev,\n                {\n                    id: newId,\n                    segmentCode: '',\n                    timeIn: '00:00:00',\n                    timeOut: '00:00:00',\n                    duration: '00:00:00'\n                }\n            ]);\n    };\n    const removeSegment = (id)=>{\n        if (segments.length <= 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegments((prev)=>prev.filter((segment)=>segment.id !== id));\n    };\n    const setSegmentCount2 = (count)=>{\n        if (count < 1) {\n            showErrorToast('invalidData');\n            return;\n        }\n        setSegmentCount(count);\n        const newSegments = [];\n        for(let i = 1; i <= count; i++){\n            newSegments.push({\n                id: i,\n                segmentCode: '',\n                timeIn: '00:00:00',\n                timeOut: '00:00:00',\n                duration: '00:00:00'\n            });\n        }\n        setSegments(newSegments);\n    };\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [segmentCodeValidation, setSegmentCodeValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // التحقق من الحقول المطلوبة\n        const requiredFields = [];\n        if (!formData.hardDiskNumber.trim()) requiredFields.push(t('addMedia.hardDiskNumber'));\n        if (!formData.type) requiredFields.push(t('common.type'));\n        if (!formData.channel) requiredFields.push(t('addMedia.channel'));\n        if (!formData.status) requiredFields.push(t('common.status'));\n        // التحقق من كود السيجمانت\n        const segmentsWithoutCode = segments.filter((segment)=>!segment.segmentCode || segment.segmentCode.trim() === '');\n        if (segmentsWithoutCode.length > 0) {\n            requiredFields.push(t('addMedia.segmentCodeRequired'));\n        }\n        // التحقق من Time Out للسيجمانت\n        const invalidSegments = segments.filter((segment)=>!segment.timeOut || segment.timeOut === '00:00:00');\n        if (invalidSegments.length > 0) {\n            requiredFields.push(t('addMedia.timeOutRequired'));\n        }\n        if (requiredFields.length > 0) {\n            showErrorToast('invalidData');\n            return;\n        }\n        // التحقق من تكرار أكواد السيجمانت\n        const segmentCodes = segments.map((s)=>s.segmentCode).filter((code)=>code && code.trim());\n        try {\n            const validationResponse = await fetch('/api/validate-segment-code', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    segmentCodes\n                })\n            });\n            const validationResult = await validationResponse.json();\n            if (validationResult.success && validationResult.hasAnyDuplicates) {\n                let errorMessage = 'تم العثور على أكواد مكررة:\\n\\n';\n                if (validationResult.hasInternalDuplicates) {\n                    errorMessage += \"\\uD83D\\uDD34 أكواد مكررة داخل نفس المادة: \".concat(validationResult.internalDuplicates.join(', '), \"\\n\\n\");\n                }\n                if (validationResult.hasExternalDuplicates) {\n                    errorMessage += '🔴 أكواد موجودة في مواد أخرى:\\n';\n                    validationResult.externalDuplicates.forEach((dup)=>{\n                        errorMessage += '- الكود \"'.concat(dup.segmentCode, '\" موجود في المادة: ').concat(dup.mediaName, \" (\").concat(dup.mediaType, \")\\n\");\n                    });\n                }\n                errorMessage += '\\nيرجى تغيير الأكواد المكررة قبل الحفظ.';\n                showErrorToast('invalidData');\n                return;\n            }\n        } catch (validationError) {\n            console.error('Error validating segment codes:', validationError);\n            showErrorToast('invalidData');\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            // تحويل التوكن إلى الصيغة المتوقعة\n            const user = JSON.parse(localStorage.getItem('user') || '{}');\n            const tokenWithRole = \"token_\".concat(user.id || 'unknown', \"_\").concat(user.role || 'unknown');\n            console.log('Sending with token:', tokenWithRole);\n            console.log('User data:', user);\n            const response = await fetch('/api/media', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': \"Bearer \".concat(tokenWithRole)\n                },\n                body: JSON.stringify({\n                    formData,\n                    segments\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                showSuccessToast('mediaAdded');\n                // مسح جميع الحقول للبدء من جديد\n                setFormData({\n                    name: '',\n                    type: '',\n                    description: '',\n                    channel: '',\n                    source: '',\n                    status: '',\n                    startDate: new Date().toISOString().split('T')[0],\n                    endDate: '',\n                    notes: '',\n                    episodeNumber: '',\n                    seasonNumber: '',\n                    partNumber: '',\n                    hardDiskNumber: '',\n                    showInTX: false\n                });\n                // مسح السيجمانتات والعودة لسيجمانت واحد\n                setSegmentCount(1);\n                setSegments([\n                    {\n                        id: 1,\n                        segmentCode: '',\n                        timeIn: '00:00:00',\n                        timeOut: '',\n                        duration: ''\n                    }\n                ]);\n                // إخفاء الحقول الخاصة\n                setShowEpisodeNumber(false);\n                setShowSeasonNumber(false);\n                setShowPartNumber(false);\n                console.log('✅ تم مسح جميع الحقول بنجاح');\n            } else {\n                showErrorToast('unknownError');\n            }\n        } catch (error) {\n            console.error('Error saving media:', error);\n            showErrorToast('serverConnection');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    // أنماط CSS\n    const inputStyle = {\n        width: '100%',\n        padding: '10px',\n        borderRadius: '5px',\n        marginBottom: '10px'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DashboardLayout__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n        title: t('media.addNew'),\n        subtitle: t('media.title'),\n        icon: \"➕\",\n        requiredPermissions: [\n            'MEDIA_CREATE'\n        ],\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                    borderRadius: '15px',\n                    padding: '20px',\n                    marginBottom: '25px',\n                    border: '1px solid #6b7280',\n                    textAlign: 'center'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        style: {\n                            color: '#ffffff',\n                            marginBottom: '10px',\n                            fontSize: '1.4rem',\n                            fontWeight: 'bold'\n                        },\n                        children: [\n                            \"➕ \",\n                            t('addMedia.title')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        style: {\n                            color: '#e2e8f0',\n                            fontSize: '0.95rem',\n                            margin: 0\n                        },\n                        children: t('addMedia.subtitle')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 376,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                style: {\n                                    color: '#f3f4f6',\n                                    marginBottom: '20px',\n                                    fontSize: '1.3rem'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCDD \",\n                                    t('addMedia.basicInfo')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'grid',\n                                    gap: '15px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '200px 1fr',\n                                            gap: '15px',\n                                            alignItems: 'center'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    color: '#f3f4f6',\n                                                    fontWeight: 'bold',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    \"\\uD83D\\uDCBE \",\n                                                    t('addMedia.hardDiskNumber'),\n                                                    \": \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 54\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('addMedia.hardDiskNumber'),\n                                                value: formData.hardDiskNumber,\n                                                onChange: (e)=>handleInputChange('hardDiskNumber', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    maxWidth: '200px',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '500px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: [\n                                                    t('common.name'),\n                                                    \" \",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            color: '#ef4444'\n                                                        },\n                                                        children: \"*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 38\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                placeholder: t('common.name'),\n                                                value: formData.name,\n                                                onChange: (e)=>handleInputChange('name', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280'\n                                                },\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('common.type'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 40\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.type,\n                                                        onChange: (e)=>handleInputChange('type', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectType')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 458,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILM\",\n                                                                children: t('mediaTypes.FILM')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SERIES\",\n                                                                children: t('mediaTypes.SERIES')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROGRAM\",\n                                                                children: t('mediaTypes.PROGRAM')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 461,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"SONG\",\n                                                                children: t('mediaTypes.SONG')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"FILLER\",\n                                                                children: t('mediaTypes.FILLER')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"STING\",\n                                                                children: t('mediaTypes.STING')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"PROMO\",\n                                                                children: t('mediaTypes.PROMO')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEXT\",\n                                                                children: t('mediaTypes.NEXT')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NOW\",\n                                                                children: t('mediaTypes.NOW')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"سنعود\",\n                                                                children: t('mediaTypes.سنعود')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"عدنا\",\n                                                                children: t('mediaTypes.عدنا')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"MINI\",\n                                                                children: t('mediaTypes.MINI')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"CROSS\",\n                                                                children: t('mediaTypes.CROSS')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 471,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('addMedia.channel'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 45\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.channel,\n                                                        onChange: (e)=>handleInputChange('channel', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectChannel')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"DOCUMENTARY\",\n                                                                children: t('channels.DOCUMENTARY')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 491,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"NEWS\",\n                                                                children: t('channels.NEWS')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"OTHER\",\n                                                                children: t('channels.OTHER')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: [\n                                                            t('common.status'),\n                                                            \" \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: '#ef4444'\n                                                                },\n                                                                children: \"*\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 42\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        value: formData.status,\n                                                        onChange: (e)=>handleInputChange('status', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        },\n                                                        required: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"\",\n                                                                children: t('addMedia.selectStatus')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 512,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"VALID\",\n                                                                children: t('mediaStatus.VALID')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 513,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_CENSORSHIP\",\n                                                                children: t('mediaStatus.REJECTED_CENSORSHIP')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"REJECTED_TECHNICAL\",\n                                                                children: t('mediaStatus.REJECTED_TECHNICAL')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 515,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"EXPIRED\",\n                                                                children: t('mediaStatus.EXPIRED')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: \"HOLD\",\n                                                                children: t('mediaStatus.HOLD')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.source')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: t('addMedia.source'),\n                                                        value: formData.source,\n                                                        onChange: (e)=>handleInputChange('source', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.startDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 544,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.startDate,\n                                                        onChange: (e)=>handleInputChange('startDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 547,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.endDate')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 561,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"date\",\n                                                        value: formData.endDate,\n                                                        onChange: (e)=>handleInputChange('endDate', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            display: 'grid',\n                                            gridTemplateColumns: '1fr 1fr 1fr',\n                                            gap: '15px'\n                                        },\n                                        children: [\n                                            showEpisodeNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.episodeNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.episodeNumber'),\n                                                        value: formData.episodeNumber,\n                                                        onChange: (e)=>handleInputChange('episodeNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 19\n                                            }, this),\n                                            showSeasonNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.seasonNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.seasonNumber'),\n                                                        value: formData.seasonNumber,\n                                                        onChange: (e)=>handleInputChange('seasonNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 19\n                                            }, this),\n                                            showPartNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        style: {\n                                                            display: 'block',\n                                                            marginBottom: '5px',\n                                                            color: '#f3f4f6',\n                                                            fontSize: '0.9rem'\n                                                        },\n                                                        children: t('addMedia.partNumber')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 625,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"number\",\n                                                        placeholder: t('addMedia.partNumber'),\n                                                        value: formData.partNumber,\n                                                        onChange: (e)=>handleInputChange('partNumber', e.target.value),\n                                                        style: {\n                                                            ...inputStyle,\n                                                            background: '#1f2937',\n                                                            color: 'white',\n                                                            border: '1px solid #6b7280'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 624,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.description')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 645,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.description'),\n                                                value: formData.description,\n                                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            maxWidth: '600px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'block',\n                                                    marginBottom: '5px',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem'\n                                                },\n                                                children: t('addMedia.notes')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                placeholder: t('addMedia.additionalNotes'),\n                                                value: formData.notes,\n                                                onChange: (e)=>handleInputChange('notes', e.target.value),\n                                                style: {\n                                                    ...inputStyle,\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    border: '1px solid #6b7280',\n                                                    minHeight: '80px',\n                                                    resize: 'vertical',\n                                                    width: '100%'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 665,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginTop: '15px'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                style: {\n                                                    display: 'flex',\n                                                    alignItems: 'center',\n                                                    color: '#f3f4f6',\n                                                    fontSize: '0.9rem',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.showInTX,\n                                                        onChange: (e)=>handleInputChange('showInTX', e.target.checked.toString()),\n                                                        style: {\n                                                            marginLeft: '10px',\n                                                            width: '18px',\n                                                            height: '18px'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 694,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        style: {\n                                                            background: '#10b981',\n                                                            color: 'white',\n                                                            padding: '2px 8px',\n                                                            borderRadius: '4px',\n                                                            marginLeft: '10px',\n                                                            fontWeight: 'bold'\n                                                        },\n                                                        children: \"TX\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t('addMedia.showInSchedule')\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 687,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    fontSize: '0.8rem',\n                                                    color: '#9ca3af',\n                                                    marginTop: '5px',\n                                                    marginRight: '35px'\n                                                },\n                                                children: t('addMedia.txDescription')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 712,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 686,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 393,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: '#4a5568',\n                            borderRadius: '15px',\n                            padding: '25px',\n                            marginBottom: '25px',\n                            border: '1px solid #6b7280'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    justifyContent: 'space-between',\n                                    alignItems: 'center',\n                                    marginBottom: '20px'\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        style: {\n                                            color: '#f3f4f6',\n                                            fontSize: '1.3rem'\n                                        },\n                                        children: [\n                                            \"\\uD83C\\uDFAC \",\n                                            t('addMedia.segments')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: addSegment,\n                                                style: {\n                                                    background: '#3b82f6',\n                                                    color: 'white',\n                                                    border: 'none',\n                                                    borderRadius: '5px',\n                                                    padding: '8px 15px',\n                                                    marginLeft: '10px',\n                                                    cursor: 'pointer'\n                                                },\n                                                children: t('addMedia.addSegment')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                min: \"1\",\n                                                value: segmentCount,\n                                                onChange: (e)=>setSegmentCount2(parseInt(e.target.value)),\n                                                style: {\n                                                    width: '60px',\n                                                    padding: '8px',\n                                                    borderRadius: '5px',\n                                                    border: '1px solid #6b7280',\n                                                    background: '#1f2937',\n                                                    color: 'white',\n                                                    textAlign: 'center'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 13\n                            }, this),\n                            segments.map((segment, index)=>{\n                                var _segmentCodeValidation_segment_id, _segmentCodeValidation_segment_id1;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        background: '#374151',\n                                        borderRadius: '10px',\n                                        padding: '15px',\n                                        marginBottom: '15px',\n                                        border: '1px solid #4b5563'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                justifyContent: 'space-between',\n                                                alignItems: 'center',\n                                                marginBottom: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    style: {\n                                                        color: '#f3f4f6',\n                                                        margin: 0\n                                                    },\n                                                    children: [\n                                                        t('addMedia.segment'),\n                                                        \" \",\n                                                        segment.id\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"button\",\n                                                    onClick: ()=>removeSegment(segment.id),\n                                                    style: {\n                                                        background: '#ef4444',\n                                                        color: 'white',\n                                                        border: 'none',\n                                                        borderRadius: '5px',\n                                                        padding: '5px 10px',\n                                                        cursor: 'pointer'\n                                                    },\n                                                    children: t('common.delete')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 778,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'grid',\n                                                gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n                                                gap: '15px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.segmentCode')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 796,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                position: 'relative'\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: segment.segmentCode,\n                                                                    onChange: (e)=>handleSegmentChange(segment.id, 'segmentCode', e.target.value),\n                                                                    placeholder: t('addMedia.segmentCodeExample'),\n                                                                    required: true,\n                                                                    style: {\n                                                                        ...inputStyle,\n                                                                        background: segment.segmentCode ? '#1f2937' : '#7f1d1d',\n                                                                        color: 'white',\n                                                                        border: ((_segmentCodeValidation_segment_id = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id === void 0 ? void 0 : _segmentCodeValidation_segment_id.isValid) === false ? '2px solid #ef4444' : segment.segmentCode ? ((_segmentCodeValidation_segment_id1 = segmentCodeValidation[segment.id]) === null || _segmentCodeValidation_segment_id1 === void 0 ? void 0 : _segmentCodeValidation_segment_id1.isValid) === true ? '2px solid #10b981' : '1px solid #6b7280' : '2px solid #ef4444',\n                                                                        paddingRight: segmentCodeValidation[segment.id] ? '35px' : '12px'\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                segmentCodeValidation[segment.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    style: {\n                                                                        position: 'absolute',\n                                                                        right: '8px',\n                                                                        top: '50%',\n                                                                        transform: 'translateY(-50%)',\n                                                                        fontSize: '16px'\n                                                                    },\n                                                                    children: segmentCodeValidation[segment.id].isChecking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#fbbf24'\n                                                                        },\n                                                                        children: \"⏳\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 829,\n                                                                        columnNumber: 29\n                                                                    }, this) : segmentCodeValidation[segment.id].isValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#10b981'\n                                                                        },\n                                                                        children: \"✅\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 831,\n                                                                        columnNumber: 29\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: '#ef4444'\n                                                                        },\n                                                                        children: \"❌\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                        lineNumber: 833,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        segmentCodeValidation[segment.id] && segmentCodeValidation[segment.id].message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            style: {\n                                                                fontSize: '0.8rem',\n                                                                marginTop: '4px',\n                                                                color: segmentCodeValidation[segment.id].isValid ? '#10b981' : '#ef4444'\n                                                            },\n                                                            children: segmentCodeValidation[segment.id].message\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 839,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 795,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: \"Time In\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeIn,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeIn', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 853,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: [\n                                                                \"Time Out \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    style: {\n                                                                        color: '#ef4444'\n                                                                    },\n                                                                    children: \"*\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                                    lineNumber: 870,\n                                                                    columnNumber: 32\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 869,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.timeOut,\n                                                            onChange: (e)=>handleSegmentChange(segment.id, 'timeOut', e.target.value),\n                                                            onBlur: ()=>calculateDuration(segment.id),\n                                                            placeholder: \"00:00:00\",\n                                                            onFocus: (e)=>{\n                                                                // إزالة القيمة الوهمية عند النقر\n                                                                if (e.target.value === '00:00:00') {\n                                                                    handleSegmentChange(segment.id, 'timeOut', '');\n                                                                }\n                                                            },\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280'\n                                                            },\n                                                            required: true\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 872,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            style: {\n                                                                display: 'block',\n                                                                marginBottom: '5px',\n                                                                color: '#f3f4f6',\n                                                                fontSize: '0.9rem'\n                                                            },\n                                                            children: t('addMedia.durationAutoCalculated')\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: segment.duration,\n                                                            readOnly: true,\n                                                            style: {\n                                                                ...inputStyle,\n                                                                background: '#1f2937',\n                                                                color: 'white',\n                                                                border: '1px solid #6b7280',\n                                                                opacity: '0.7',\n                                                                cursor: 'not-allowed'\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                            lineNumber: 794,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, segment.id, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                    lineNumber: 769,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 725,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            justifyContent: 'center',\n                            gap: '15px',\n                            marginTop: '20px',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#10b981',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: isSubmitting ? \"⏳ \".concat(t('common.saving')) : \"\\uD83D\\uDCBE \".concat(t('addMedia.saveAndAddNew'))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>router.push('/media-list'),\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#3b82f6',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDCCB \",\n                                    t('navigation.mediaList')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: ()=>{\n                                    // مسح جميع الحقول يدوياً\n                                    setFormData({\n                                        name: '',\n                                        type: '',\n                                        description: '',\n                                        channel: '',\n                                        source: '',\n                                        status: '',\n                                        startDate: new Date().toISOString().split('T')[0],\n                                        endDate: '',\n                                        notes: '',\n                                        episodeNumber: '',\n                                        seasonNumber: '',\n                                        partNumber: '',\n                                        hardDiskNumber: '',\n                                        showInTX: false\n                                    });\n                                    setSegmentCount(1);\n                                    setSegments([\n                                        {\n                                            id: 1,\n                                            segmentCode: '',\n                                            timeIn: '00:00:00',\n                                            timeOut: '',\n                                            duration: ''\n                                        }\n                                    ]);\n                                    setShowEpisodeNumber(false);\n                                    setShowSeasonNumber(false);\n                                    setShowPartNumber(false);\n                                    showSuccessToast('changesSaved');\n                                },\n                                disabled: isSubmitting,\n                                style: {\n                                    background: '#f59e0b',\n                                    color: 'white',\n                                    border: 'none',\n                                    borderRadius: '5px',\n                                    padding: '12px 24px',\n                                    fontSize: '1rem',\n                                    cursor: isSubmitting ? 'not-allowed' : 'pointer',\n                                    opacity: isSubmitting ? 0.7 : 1,\n                                    fontWeight: 'bold'\n                                },\n                                children: [\n                                    \"\\uD83D\\uDDD1️ \",\n                                    t('addMedia.clearFields')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                                lineNumber: 955,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                        lineNumber: 917,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 392,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n                lineNumber: 1011,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\add-media\\\\page.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\n_s(AddMediaPage, \"zD8bHefytEBpMmtUNRBoYQorTRw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _hooks_useTranslatedToast__WEBPACK_IMPORTED_MODULE_2__.useTranslatedToast,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = AddMediaPage;\nvar _c;\n$RefreshReg$(_c, \"AddMediaPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-media/page.tsx\n"));

/***/ })

});