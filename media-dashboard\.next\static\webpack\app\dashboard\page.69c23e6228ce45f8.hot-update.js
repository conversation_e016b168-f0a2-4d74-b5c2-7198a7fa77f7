"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _components_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Sidebar */ \"(app-pages-browser)/./src/components/Sidebar.tsx\");\n/* harmony import */ var _components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NavigationCard */ \"(app-pages-browser)/./src/components/NavigationCard.tsx\");\n/* harmony import */ var _components_Logo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _styles_dashboard_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/dashboard.css */ \"(app-pages-browser)/./src/styles/dashboard.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, logout, hasPermission } = (0,_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__.useAppTranslation)();\n    // تعريف عناصر التنقل في القائمة العلوية\n    const topNavigationItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            active: false,\n            path: '/media-list'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            active: false,\n            path: '/add-media'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            active: false,\n            path: '/daily-schedule'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            active: false,\n            path: '/reports'\n        },\n        ...(user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && (user === null || user === void 0 ? void 0 : user.username) === 'admin' ? [\n            {\n                name: t('navigation.unifiedSystem'),\n                icon: '📤',\n                active: false,\n                path: '/unified-system'\n            }\n        ] : [],\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard'\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            active: false,\n            path: '/statistics'\n        }\n    ];\n    // تحديث الوقت كل ثانية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Dashboard.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Dashboard.useEffect.timer\"], 1000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(timer)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    // بيانات الإحصائيات الحقيقية\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalMedia: 0,\n        validMedia: 0,\n        rejectedMedia: 0,\n        expiredMedia: 0,\n        pendingMedia: 0,\n        activeUsers: 0,\n        onlineUsers: 0,\n        todayAdded: 0\n    });\n    // حالة تنبيه المواد المنتهية\n    const [expiredAlertShown, setExpiredAlertShown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تخزين المواد المنتهية للتنبيه\n    const [expiredMediaItems, setExpiredMediaItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showExpiredAlert, setShowExpiredAlert] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // جلب البيانات الحقيقية\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            fetchRealStats();\n            // تحديث البيانات كل 30 ثانية\n            const interval = setInterval(fetchRealStats, 30000);\n            return ({\n                \"Dashboard.useEffect\": ()=>clearInterval(interval)\n            })[\"Dashboard.useEffect\"];\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const fetchRealStats = async ()=>{\n        try {\n            setLoading(true);\n            // جلب البيانات من API مع timeout أطول\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 15000); // 15 ثانية timeout\n            const [mediaResponse, usersResponse] = await Promise.all([\n                fetch('/api/media', {\n                    signal: controller.signal,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                }),\n                fetch('/api/users', {\n                    signal: controller.signal,\n                    headers: {\n                        'Content-Type': 'application/json'\n                    }\n                })\n            ]);\n            clearTimeout(timeoutId);\n            let mediaData = [];\n            let userData = [];\n            if (mediaResponse.ok) {\n                try {\n                    const mediaResult = await mediaResponse.json();\n                    mediaData = mediaResult.success ? mediaResult.data : [];\n                } catch (e) {\n                    console.warn('Failed to parse media response:', e);\n                }\n            }\n            if (usersResponse.ok) {\n                try {\n                    const usersResult = await usersResponse.json();\n                    userData = usersResult.success ? usersResult.users : [];\n                } catch (e) {\n                    console.warn('Failed to parse users response:', e);\n                }\n            }\n            // حساب الإحصائيات الحقيقية\n            const totalMedia = mediaData.length;\n            const validMedia = mediaData.filter((item)=>item.status === 'VALID').length;\n            const rejectedMedia = mediaData.filter((item)=>item.status === 'REJECTED_CENSORSHIP' || item.status === 'REJECTED_TECHNICAL').length;\n            // التحقق من المواد المنتهية حسب التاريخ\n            const today = new Date();\n            const expiredByDateItems = mediaData.filter((item)=>item.endDate && new Date(item.endDate) < today);\n            // المواد المنتهية (إما بالحالة أو بالتاريخ)\n            const expiredStatusItems = mediaData.filter((item)=>item.status === 'EXPIRED');\n            const allExpiredItems = [\n                ...new Set([\n                    ...expiredByDateItems,\n                    ...expiredStatusItems\n                ])\n            ];\n            const expiredMedia = allExpiredItems.length;\n            // تحديث قائمة المواد المنتهية للتنبيه\n            setExpiredMediaItems(allExpiredItems);\n            // إظهار التنبيه مرة واحدة فقط في الجلسة\n            const alertKey = \"expired_alert_\".concat(new Date().toDateString());\n            const alertShownToday = localStorage.getItem(alertKey);\n            if (allExpiredItems.length > 0 && !alertShownToday && !expiredAlertShown) {\n                setShowExpiredAlert(true);\n                setExpiredAlertShown(true);\n                localStorage.setItem(alertKey, 'true');\n                console.log(\"⚠️ تم العثور على \".concat(allExpiredItems.length, \" مادة منتهية - عرض التنبيه\"));\n            } else {\n                setShowExpiredAlert(false);\n            }\n            const pendingMedia = mediaData.filter((item)=>item.status === 'PENDING').length;\n            // حساب المواد المضافة اليوم\n            const todayStr = new Date().toDateString();\n            const todayAdded = mediaData.filter((item)=>{\n                if (!item.createdAt) return false;\n                return new Date(item.createdAt).toDateString() === todayStr;\n            }).length;\n            setStats({\n                totalMedia,\n                validMedia,\n                rejectedMedia,\n                expiredMedia,\n                pendingMedia,\n                activeUsers: userData.length,\n                onlineUsers: userData.filter((u)=>u.isActive).length,\n                todayAdded\n            });\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // التحقق من نوع الخطأ\n            if (error instanceof Error && error.name === 'AbortError') {\n                console.log('Request was aborted due to timeout');\n            }\n            // بيانات افتراضية في حالة الخطأ\n            setStats({\n                totalMedia: 0,\n                validMedia: 0,\n                rejectedMedia: 0,\n                expiredMedia: 0,\n                pendingMedia: 0,\n                activeUsers: 4,\n                onlineUsers: 1,\n                todayAdded: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const navigationItems = [\n        {\n            name: 'لوحة التحكم',\n            icon: '📊',\n            active: true,\n            path: '/dashboard'\n        },\n        {\n            name: 'المواد الإعلامية',\n            icon: '🎬',\n            active: false,\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: 'إضافة مادة',\n            icon: '➕',\n            active: false,\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: 'الخريطة البرامجية',\n            icon: '📅',\n            active: false,\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: 'المستخدمين',\n            icon: '👥',\n            active: false,\n            path: '/admin-dashboard',\n            adminOnly: true\n        },\n        {\n            name: 'الإحصائيات',\n            icon: '📈',\n            active: false,\n            path: '/statistics',\n            adminOnly: true\n        }\n    ].filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: 'rtl'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: 'center',\n                    color: 'white'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            marginBottom: '30px'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            size: \"large\",\n                            style: {\n                                fontSize: '2rem'\n                            }\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '3rem',\n                            marginBottom: '20px'\n                        },\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontSize: '1.5rem',\n                            marginBottom: '10px'\n                        },\n                        children: t('common.loadingData')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            color: '#a0aec0',\n                            fontSize: '1rem'\n                        },\n                        children: t('messages.pleaseWait')\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.AuthGuard, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                minHeight: '100vh',\n                background: '#1a1d29',\n                color: 'white',\n                fontFamily: 'Cairo, Arial, sans-serif',\n                direction: isRTL ? 'rtl' : 'ltr'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isOpen: sidebarOpen,\n                    onToggle: ()=>setSidebarOpen(!sidebarOpen)\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this),\n                showExpiredAlert && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        top: '20px',\n                        left: '20px',\n                        right: '20px',\n                        zIndex: 1000,\n                        background: '#ef4444',\n                        color: 'white',\n                        padding: '15px 20px',\n                        borderRadius: '8px',\n                        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center',\n                        maxHeight: '300px',\n                        overflow: 'auto'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontWeight: 'bold',\n                                        fontSize: '1.1rem',\n                                        marginBottom: '10px'\n                                    },\n                                    children: [\n                                        \"⚠️ \",\n                                        t('messages.warning'),\n                                        \": \",\n                                        t('stats.expiredMedia'),\n                                        \" \",\n                                        expiredMediaItems.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: '0.9rem'\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            paddingRight: '20px',\n                                            margin: '5px 0'\n                                        },\n                                        children: [\n                                            expiredMediaItems.slice(0, 5).map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: [\n                                                        item.name,\n                                                        \" - \",\n                                                        item.endDate ? \"منتهية بتاريخ: \".concat(new Date(item.endDate).toLocaleDateString('ar-EG')) : 'منتهية'\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this)),\n                                            expiredMediaItems.length > 5 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"... والمزيد (\",\n                                                    expiredMediaItems.length - 5,\n                                                    \" مادة أخرى)\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setShowExpiredAlert(false);\n                                setExpiredAlertShown(true);\n                                // حفظ في localStorage لمنع ظهور التنبيه مرة أخرى اليوم\n                                const alertKey = \"expired_alert_\".concat(new Date().toDateString());\n                                localStorage.setItem(alertKey, 'true');\n                                console.log('🔕 تم إغلاق تنبيه المواد المنتهية نهائياً لليوم');\n                            },\n                            style: {\n                                background: 'rgba(255, 255, 255, 0.2)',\n                                border: 'none',\n                                color: 'white',\n                                borderRadius: '4px',\n                                padding: '5px 10px',\n                                cursor: 'pointer',\n                                marginRight: '10px'\n                            },\n                            children: t('common.close')\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 287,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        background: '#1a1d29',\n                        padding: '15px 30px',\n                        borderBottom: '1px solid #2d3748',\n                        display: 'flex',\n                        justifyContent: 'space-between',\n                        alignItems: 'center'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setSidebarOpen(!sidebarOpen),\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.5rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"☰\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Logo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: \"medium\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                gap: '5px'\n                            },\n                            children: topNavigationItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>router.push(item.path),\n                                    style: {\n                                        background: item.active ? '#4299e1' : 'transparent',\n                                        color: item.active ? 'white' : '#a0aec0',\n                                        border: 'none',\n                                        borderRadius: '8px',\n                                        padding: '8px 16px',\n                                        cursor: 'pointer',\n                                        fontSize: '0.9rem',\n                                        fontWeight: 'bold',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '8px',\n                                        transition: 'all 0.2s'\n                                    },\n                                    onMouseEnter: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = '#2d3748';\n                                            e.target.style.color = 'white';\n                                        }\n                                    },\n                                    onMouseLeave: (e)=>{\n                                        if (!item.active) {\n                                            e.target.style.background = 'transparent';\n                                            e.target.style.color = '#a0aec0';\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.icon\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this),\n                                        item.name\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '15px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        const newLang = currentLang === 'ar' ? 'en' : 'ar';\n                                        i18n.changeLanguage(newLang);\n                                    },\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '5px'\n                                    },\n                                    title: currentLang === 'ar' ? 'Switch to English' : 'التبديل للعربية',\n                                    children: [\n                                        \"\\uD83C\\uDF10\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            style: {\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: currentLang === 'ar' ? 'EN' : 'عر'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: logout,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer'\n                                    },\n                                    title: t('auth.logout'),\n                                    children: \"\\uD83D\\uDEAA\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: '30px',\n                        ...isRTL ? {\n                            marginRight: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-right 0.3s ease'\n                        } : {\n                            marginLeft: sidebarOpen ? '280px' : '0',\n                            transition: 'margin-left 0.3s ease'\n                        }\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'flex',\n                                justifyContent: 'space-between',\n                                alignItems: 'flex-start',\n                                marginBottom: '30px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '15px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                width: '50px',\n                                                height: '50px',\n                                                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                borderRadius: '12px',\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                justifyContent: 'center',\n                                                fontSize: '1.5rem'\n                                            },\n                                            children: \"\\uD83D\\uDCCA\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    style: {\n                                                        fontSize: '2rem',\n                                                        fontWeight: 'bold',\n                                                        margin: '0 0 5px 0',\n                                                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n                                                        WebkitBackgroundClip: 'text',\n                                                        WebkitTextFillColor: 'transparent'\n                                                    },\n                                                    children: t('dashboard.title')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 450,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#a0aec0',\n                                                        margin: 0,\n                                                        fontSize: '1rem'\n                                                    },\n                                                    children: t('dashboard.subtitle')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    style: {\n                                                        color: '#68d391',\n                                                        margin: '5px 0 0 0',\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        t('dashboard.overview'),\n                                                        \": \",\n                                                        stats.totalMedia,\n                                                        \" \",\n                                                        t('stats.totalMedia'),\n                                                        \" - \",\n                                                        stats.activeUsers,\n                                                        \" \",\n                                                        t('stats.activeUsers')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'flex',\n                                        alignItems: 'center',\n                                        gap: '20px',\n                                        color: '#a0aec0'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        width: '8px',\n                                                        height: '8px',\n                                                        background: '#68d391',\n                                                        borderRadius: '50%'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: t('common.loading')\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: 'flex',\n                                                alignItems: 'center',\n                                                gap: '8px'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"\\uD83D\\uDD04\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 493,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: '0.9rem'\n                                                    },\n                                                    children: [\n                                                        \"المزامنة: \",\n                                                        currentTime.toLocaleTimeString('ar-EG')\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83C\\uDFAC\",\n                                    title: t('navigation.mediaList'),\n                                    subtitle: t('media.list'),\n                                    path: \"/media-list\",\n                                    permission: \"MEDIA_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"➕\",\n                                    title: t('navigation.addMedia'),\n                                    subtitle: t('media.addNew'),\n                                    path: \"/add-media\",\n                                    permission: \"MEDIA_CREATE\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC5\",\n                                    title: t('navigation.weeklySchedule'),\n                                    subtitle: t('schedule.weekly'),\n                                    path: \"/weekly-schedule\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 526,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCA\",\n                                    title: t('navigation.dailySchedule'),\n                                    subtitle: t('schedule.daily'),\n                                    path: \"/daily-schedule\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'grid',\n                                gridTemplateColumns: 'repeat(4, 1fr)',\n                                gap: '20px',\n                                marginBottom: '20px'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCCB\",\n                                    title: t('navigation.reports'),\n                                    subtitle: t('dashboard.recentActivity'),\n                                    path: \"/reports\",\n                                    permission: \"SCHEDULE_READ\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 13\n                                }, this),\n                                (user === null || user === void 0 ? void 0 : user.role) === 'ADMIN' && (user === null || user === void 0 ? void 0 : user.username) === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCE4\",\n                                    title: t('navigation.unifiedSystem'),\n                                    subtitle: t('common.import') + '/' + t('common.export'),\n                                    path: \"/unified-system\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 563,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDC65\",\n                                    title: t('navigation.adminDashboard'),\n                                    subtitle: t('admin.users'),\n                                    path: \"/admin-dashboard\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NavigationCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    icon: \"\\uD83D\\uDCC8\",\n                                    title: t('navigation.statistics'),\n                                    subtitle: t('dashboard.statistics'),\n                                    path: \"/statistics\",\n                                    height: \"280px\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        position: 'fixed',\n                        bottom: '20px',\n                        left: '20px',\n                        color: '#6c757d',\n                        fontSize: '0.75rem',\n                        fontFamily: 'Arial, sans-serif',\n                        direction: 'ltr'\n                    },\n                    children: \"Powered By Mahmoud Ismail\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 241,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"TOxbwKN4xJEapjey2i9NarXO5zs=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_7__.useAppTranslation\n    ];\n});\n_c = Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Sidebar.tsx":
/*!************************************!*\
  !*** ./src/components/Sidebar.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _AuthGuard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthGuard */ \"(app-pages-browser)/./src/components/AuthGuard.tsx\");\n/* harmony import */ var _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAppTranslation */ \"(app-pages-browser)/./src/hooks/useAppTranslation.ts\");\n/* harmony import */ var _Logo__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Logo */ \"(app-pages-browser)/./src/components/Logo.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Sidebar(param) {\n    let { isOpen, onToggle } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { user, hasPermission } = (0,_AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { t, isRTL } = (0,_hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation)();\n    const menuItems = [\n        {\n            name: t('navigation.dashboard'),\n            icon: '📊',\n            path: '/dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.mediaList'),\n            icon: '🎬',\n            path: '/media-list',\n            permission: 'MEDIA_READ'\n        },\n        {\n            name: t('navigation.addMedia'),\n            icon: '➕',\n            path: '/add-media',\n            permission: 'MEDIA_CREATE'\n        },\n        {\n            name: t('navigation.weeklySchedule'),\n            icon: '📅',\n            path: '/weekly-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.dailySchedule'),\n            icon: '📊',\n            path: '/daily-schedule',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.reports'),\n            icon: '📋',\n            path: '/reports',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.importSchedule'),\n            icon: '📤',\n            path: '/daily-schedule/import',\n            permission: 'SCHEDULE_READ'\n        },\n        {\n            name: t('navigation.adminDashboard'),\n            icon: '👥',\n            path: '/admin-dashboard',\n            permission: null\n        },\n        {\n            name: t('navigation.statistics'),\n            icon: '📈',\n            path: '/statistics',\n            permission: null\n        }\n    ];\n    const filteredMenuItems = menuItems.filter((item)=>{\n        if (item.adminOnly && (user === null || user === void 0 ? void 0 : user.role) !== 'ADMIN') return false;\n        if (item.permission && !hasPermission(item.permission)) return false;\n        return true;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    background: 'rgba(0, 0, 0, 0.5)',\n                    zIndex: 998,\n                    display: window.innerWidth <= 768 ? 'block' : 'none'\n                },\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'fixed',\n                    top: 0,\n                    ...isRTL ? {\n                        right: isOpen ? 0 : '-280px',\n                        borderLeft: '1px solid #2d3748'\n                    } : {\n                        left: isOpen ? 0 : '-280px',\n                        borderRight: '1px solid #2d3748'\n                    },\n                    width: '280px',\n                    height: '100vh',\n                    background: '#1a1d29',\n                    transition: \"\".concat(isRTL ? 'right' : 'left', \" 0.3s ease\"),\n                    zIndex: 999,\n                    display: 'flex',\n                    flexDirection: 'column',\n                    fontFamily: 'Cairo, Arial, sans-serif'\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderBottom: '1px solid #2d3748',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'space-between'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '12px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Logo__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: \"small\",\n                                            style: {\n                                                color: 'white'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                color: '#a0aec0',\n                                                margin: 0,\n                                                fontSize: '0.8rem'\n                                            },\n                                            children: t('dashboard.subtitle')\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '10px'\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: onToggle,\n                                    style: {\n                                        background: 'transparent',\n                                        border: 'none',\n                                        color: '#a0aec0',\n                                        fontSize: '1.2rem',\n                                        cursor: 'pointer',\n                                        padding: '5px'\n                                    },\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1,\n                            padding: '20px 0',\n                            overflowY: 'auto'\n                        },\n                        children: filteredMenuItems.map((item, index)=>{\n                            const isActive = pathname === item.path;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    router.push(item.path);\n                                    if (window.innerWidth <= 768) {\n                                        onToggle();\n                                    }\n                                },\n                                style: {\n                                    width: '100%',\n                                    background: isActive ? '#2d3748' : 'transparent',\n                                    color: isActive ? 'white' : '#a0aec0',\n                                    border: 'none',\n                                    borderTop: 'none',\n                                    borderBottom: 'none',\n                                    ...isRTL ? {\n                                        borderLeft: 'none',\n                                        borderRight: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    } : {\n                                        borderRight: 'none',\n                                        borderLeft: isActive ? '3px solid #667eea' : '3px solid transparent'\n                                    },\n                                    padding: isRTL ? '12px 20px 12px 8px' : '12px 8px 12px 20px',\n                                    textAlign: isRTL ? 'right' : 'left',\n                                    cursor: 'pointer',\n                                    fontSize: '0.9rem',\n                                    fontWeight: 'bold',\n                                    transition: 'all 0.2s ease',\n                                    direction: isRTL ? 'rtl' : 'ltr'\n                                },\n                                children: item.name\n                            }, index, false, {\n                                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            padding: '20px',\n                            borderTop: '1px solid #2d3748'\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                localStorage.removeItem('user');\n                                localStorage.removeItem('token');\n                                router.push('/login');\n                            },\n                            style: {\n                                width: '100%',\n                                background: 'linear-gradient(45deg, #f56565, #e53e3e)',\n                                color: 'white',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '10px',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                gap: '8px',\n                                fontSize: '0.9rem',\n                                fontWeight: 'bold',\n                                marginBottom: '15px'\n                            },\n                            children: [\n                                \"\\uD83D\\uDEAA \",\n                                t('navigation.logout')\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\project sport\\\\media-dashboard-clean\\\\media-dashboard\\\\src\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"VJIKuK+HsyvOcEJPeGwsV88vEIU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _AuthGuard__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_useAppTranslation__WEBPACK_IMPORTED_MODULE_4__.useAppTranslation\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/dashboard.css":
/*!**********************************!*\
  !*** ./src/styles/dashboard.css ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8f0a083acb13\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZGFzaGJvYXJkLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkQ6XFxwcm9qZWN0IHNwb3J0XFxtZWRpYS1kYXNoYm9hcmQtY2xlYW5cXG1lZGlhLWRhc2hib2FyZFxcc3JjXFxzdHlsZXNcXGRhc2hib2FyZC5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZjBhMDgzYWNiMTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/dashboard.css\n"));

/***/ })

});